<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="warn" name="Agent" packages="">
    <Properties>
        <!--日志路径在jar包同级目录下的logs文件夹-->
        <Property name="baseDir">./logs</Property>
    </Properties>
    <Loggers>
        <Root level="info">
            <AppenderRef ref="info"/>
            <AppenderRef ref="error"/>
            <AppenderRef ref="Console"/>
        </Root>
        <!--屏蔽Spring info 级别以下的日志-->
        <Logger name="org.springframework" level="warn"/>
        <Logger name="org.apache.http" level="info"/>
    </Loggers>
    <Appenders>
        <!-- error 日志最多占用内存 10MB*11(.log*11)+10MB*10(.gz*10)=210MB 正常运行远小于210MB -->
        <RollingFile name="error" fileName="${baseDir}/error.log"
                     filePattern="${baseDir}/error/error-%d{yyyy-MM-dd}-%i.log.gz">
            <!--  ThresholdFilter 标签中的level是日志的级别  onMatch是匹配上的日志级别应该做的操作 onMismatch是没有匹配上要做的操作
                   level的取值：ALL< Trace < DEBUG < INFO < WARN < ERROR <FATAL
                   onMatch和onMismatch的取值：
                        ACCEPT - 接受level指定的日志级别, 后续的过滤器对该级别的日志不再过滤
                        DENY - 不接受level指定的日志级别, 后续的过滤器对该级别的日志不再过滤
                        NEUTRAL - 保持中立，根据后续的日志级别过滤
                   只指定onMatch的值时，后续的过滤不执行  -->
            <Filters>
                <ThresholdFilter level="error" onMatch="ACCEPT" onMismatch="NEUTRAL"/>
                <ThresholdFilter level="warn" onMatch="DENY" onMismatch="NEUTRAL"/>
                <ThresholdFilter level="info" onMatch="DENY" onMismatch="NEUTRAL"/>
                <ThresholdFilter level="debug" onMatch="DENY" onMismatch="NEUTRAL"/>
            </Filters>
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %-5level %c{1.} %line %m%n"/>
            <Policies>
                <!-- 文件每10M分割 -->
                <SizeBasedTriggeringPolicy size="10 MB"/>
            </Policies>
            <!-- 最多个数10个 -->
            <DefaultRolloverStrategy max="10">
                <Delete basePath="${baseDir}" maxDepth="2">
                    <IfLastModified age="30d" />
                    <IfFileName glob="*/error-*.log.gz" />
                </Delete>
            </DefaultRolloverStrategy>
        </RollingFile>
        <!-- info 日志最多占用内存 10MB*11(.log*11)+10MB*10(.gz*10)=210MB 实际远小于210MB -->
        <RollingFile name="info" fileName="${baseDir}/info.log"
                     filePattern="${baseDir}/info/info-%d{yyyy-MM-dd}-%i.log.gz">
            <Filters>
                <ThresholdFilter level="error" onMatch="DENY" onMismatch="NEUTRAL"/>
                <ThresholdFilter level="warn" onMatch="DENY" onMismatch="NEUTRAL"/>
                <ThresholdFilter level="info" onMatch="ACCEPT" onMismatch="NEUTRAL"/>
                <ThresholdFilter level="debug" onMatch="DENY" onMismatch="NEUTRAL"/>
            </Filters>
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %-5level %c{1.} %line %m%n"/>
            <Policies>
                <!-- 文件每10M分割 -->
                <SizeBasedTriggeringPolicy size="10 MB"/>
            </Policies>
            <!-- 每天文件个数10个 -->
            <DefaultRolloverStrategy max="10">
                <Delete basePath="${baseDir}" maxDepth="2">
                    <IfLastModified age="7d" />
                    <IfFileName glob="*/info-*.log.gz" />
                </Delete>
            </DefaultRolloverStrategy>
        </RollingFile>
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %-5level %c{1.} %line %m%n"/>
        </Console>
    </Appenders>
</Configuration>
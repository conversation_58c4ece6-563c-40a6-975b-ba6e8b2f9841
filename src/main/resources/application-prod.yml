# ==================== 生产环境配置 ====================
# 说明：生产环境专用配置，优化性能和安全性设置

server:
  port: 8099

# ==================== 数据源配置 ====================
spring:
  datasource:
    dynamic:
      primary: master
      strict: false
      datasource:
        master:
          url: *****************************************
          username: postgres
          password: Jkxy12345_
          driver-class-name: org.postgresql.Driver
          hikari:
            maximum-pool-size: 20
            minimum-idle: 5
            connection-timeout: 30000
            idle-timeout: 600000
            max-lifetime: 1800000
            leak-detection-threshold: 60000
            connection-test-query: SELECT 1
        fastgate:
          url: *****************************************
          username: postgres
          password: Jkxy12345_
          driver-class-name: org.postgresql.Driver
          hikari:
            maximum-pool-size: 30
            minimum-idle: 10
            connection-timeout: 30000
            idle-timeout: 600000
            max-lifetime: 1800000
            leak-detection-threshold: 60000
            connection-test-query: SELECT 1

  # Redis配置 - 生产环境
  redis:
    host: 127.0.0.1
    port: 6379
    database: 1
    password: uniview-123
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5
        max-wait: 3000ms
    # 键过期时间配置
    expire:
      default: 7200 # 2小时
      statistics: 600 # 10分钟

# ==================== FastGate平台配置 - 生产环境 ====================
fastgate:
  passwd: Jkxy12345_  # 使用实际密码
  url: http://127.0.0.1:8088/fastgate  # 使用实际URL
  
  api:
    addperson: /api/resource/v1/person
    delperson: /api/resource/v1/person
    getPersonList: /api/resource/v1/person
    getpicture: /api/resource/v1/person/picture/search
    addpicture: /api/resource/v1/person/picture
    delpicture: /api/resource/v1/person/picture

# ==================== Mock数据配置 - 生产环境 ====================
app:
  mock:
    enabled: false # 生产环境禁用Mock数据

# ==================== 异步处理配置 - 生产环境 ====================
async:
  persistence:
    enabled: true
    thread:
      core: 5
      max: 20
    batch:
      size: 500
      timeout: 3000
    retry:
      max: 5
      delay: 2000
    max:
      tps: 1000
    queue:
      max: 20000



# ==================== JVM优化提示 ====================
# 生产环境JVM启动参数建议:
# java -Xms4g -Xmx8g -XX:+UseG1GC -XX:MaxGCPauseMillis=100 
#      -XX:+PrintGC -XX:+PrintGCDetails -XX:+PrintGCTimeStamps
#      -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/var/log/fastgate-demo/
#      -Dspring.profiles.active=prod
#      -jar demo-0.0.1-SNAPSHOT.jar
package fastgatedemo.demo.service;

import com.alibaba.fastjson.JSONObject;
import fastgatedemo.demo.config.FastGateConf;
import fastgatedemo.demo.constant.BaseConstant;
import fastgatedemo.demo.dto.DailyAccessRecordStatsDto;
import fastgatedemo.demo.model.ScheduledTaskInfo;
import fastgatedemo.demo.model.TaskExecutionLog;
import fastgatedemo.demo.model.UniResult;
import fastgatedemo.demo.repository.ScheduledTaskInfoRepository;
import fastgatedemo.demo.util.UniHttpUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * @description 每日出入记录成功人数统计服务
 */
@Service
public class DailyAccessRecordStatsService {

    private static final Logger logger = LoggerFactory.getLogger(DailyAccessRecordStatsService.class);

    @Autowired
    private FastGateConf fastGateConf;

    @Autowired
    private ScheduledTaskManagementService taskManagementService;

    @Autowired
    private ScheduledTaskInfoRepository taskInfoRepository;

    // 缓存最近查询结果
    private DailyAccessRecordStatsDto lastQueryResult = null;
    private String lastQueryTime = "";

    /**
     * 检查任务是否启用
     * @param taskName 任务名称
     * @return 是否启用
     */
    private boolean isTaskEnabled(String taskName) {
        try {
            Optional<ScheduledTaskInfo> taskOpt = taskInfoRepository.findByTaskName(taskName);
            if (taskOpt.isPresent()) {
                Boolean enabled = taskOpt.get().getEnabled();
                return enabled != null && enabled;
            }
            // 如果任务不存在，默认为启用（向后兼容）
            logger.warn("任务 {} 在数据库中不存在，默认为启用状态", taskName);
            return true;
        } catch (Exception e) {
            logger.error("检查任务启用状态失败: {}", taskName, e);
            // 出现异常时默认为启用（避免因为数据库问题导致任务完全停止）
            return true;
        }
    }

    /**
     * 定时查询每日出入记录成功人数统计
     * 每天晚上8点执行一次
     */
    @Scheduled(cron = "0 0 20 * * ?") // 每天晚上8点执行
    public void scheduledQueryDailyAccessStats() {
        String taskName = "DailyAccessRecordStatsTask";

        // 首先检查任务是否启用
        if (!isTaskEnabled(taskName)) {
            logger.debug("任务 {} 已禁用，跳过执行", taskName);
            return;
        }

        TaskExecutionLog executionLog = taskManagementService.recordTaskStart(taskName);
        logger.info("开始定时查询每日出入记录成功人数统计");

        try {
            DailyAccessRecordStatsDto statsResult = performQuery(executionLog);
            
            // 更新缓存结果
            this.lastQueryResult = statsResult;
            this.lastQueryTime = java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            
            logger.info("定时查询完成：当天校验成功总人数: {} 人", statsResult.getTotalSuccessCount());
            
        } catch (Exception e) {
            logger.error("定时查询异常", e);
            taskManagementService.recordTaskFailure(executionLog, "定时查询异常: " + e.getMessage());
        }
    }

    /**
     * 手动触发查询每日出入记录成功人数统计
     * @return 查询结果
     */
    public Map<String, Object> manualQueryDailyAccessStats() {
        Map<String, Object> result = new HashMap<>();
        String taskName = "ManualDailyAccessRecordStatsTask";
        TaskExecutionLog executionLog = taskManagementService.recordTaskStart(taskName);

        logger.info("手动触发查询每日出入记录成功人数统计");

        try {
            DailyAccessRecordStatsDto statsResult = performQuery(executionLog);
            
            // 更新缓存结果
            this.lastQueryResult = statsResult;
            this.lastQueryTime = java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

            result.put("success", true);
            result.put("message", "手动查询完成");
            result.put("totalSuccessCount", statsResult.getTotalSuccessCount());
            result.put("statsData", statsResult);
            result.put("queryTime", this.lastQueryTime);

            return result;

        } catch (Exception e) {
            logger.error("手动查询异常", e);
            taskManagementService.recordTaskFailure(executionLog, "手动查询异常: " + e.getMessage());

            result.put("success", false);
            result.put("message", "手动查询失败: " + e.getMessage());
            return result;
        }
    }

    /**
     * 获取最近查询结果（用于前端展示）
     * @return 查询结果数据
     */
    public Map<String, Object> getLastQueryResults() {
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("statsData", this.lastQueryResult);
        result.put("queryTime", this.lastQueryTime);
        result.put("message", "获取缓存结果成功");
        return result;
    }

    /**
     * 执行查询操作的核心方法
     * @param executionLog 执行日志
     * @return 查询结果
     * @throws Exception
     */
    private DailyAccessRecordStatsDto performQuery(TaskExecutionLog executionLog) throws Exception {
        long startTime = System.currentTimeMillis();
        
        try {
            // 首先进行登录验证，获取Cookie
            String loginUrl = fastGateConf.getUrl() + BaseConstant.LOGIN_URL;
            String user = fastGateConf.getUser();
            String passwd = fastGateConf.getPasswd();
            UniHttpUtil.getCookieStore(loginUrl, user, passwd);
            logger.info("FastGate系统登录验证完成");

            // 构建当天的时间范围
            LocalDate today = LocalDate.now();
            String startTimeStr = today.format(DateTimeFormatter.ofPattern("yyyy/MM/dd")) + " 00:00:00";
            String endTimeStr = today.format(DateTimeFormatter.ofPattern("yyyy/MM/dd")) + " 23:59:59";

            // 初始化统计结果
            DailyAccessRecordStatsDto statsDto = new DailyAccessRecordStatsDto();
            statsDto.setStatsDate(today);
            statsDto.setStartTime(startTimeStr);
            statsDto.setEndTime(endTimeStr);

            logger.info("开始查询时间范围: {} 到 {}", startTimeStr, endTimeStr);

            // 构建查询参数 - 只需查询第一页获取总数
            JSONObject conditionParam = new JSONObject();
            conditionParam.put("startTime", startTimeStr);
            conditionParam.put("endTime", endTimeStr);
            conditionParam.put("matchStatus", "1"); // 1-核验成功
            conditionParam.put("page", "1");
            conditionParam.put("size", "1"); // 只需要1条记录，主要是获取totalcount

            // 构建完整URL
            String queryUrl = fastGateConf.getUrl() + BaseConstant.QUERY_ACCESS_RECORD + 
                             URLEncoder.encode(conditionParam.toJSONString(), "UTF-8");

            logger.info("查询出入记录总数: {}", queryUrl);

            // 调用FastGate平台API
            UniResult apiResult = callAccessRecordsAPI(queryUrl);
            
            int totalCount = 0;
            if (apiResult != null && apiResult.getErrCode() == 200) {
                // 解析返回数据，直接从totalcount字段获取总数
                JSONObject data = (JSONObject) apiResult.getData();
                if (data != null) {
                    totalCount = data.getIntValue("totalcount");
                    logger.info("从API获取到当天校验成功总记录数: {}", totalCount);
                } else {
                    logger.warn("FastGate返回数据为空");
                    throw new RuntimeException("FastGate返回数据为空");
                }
            } else {
                String errorMsg = apiResult != null ? apiResult.getErrMsg() : "未知错误";
                logger.error("查询FastGate平台失败: {}", errorMsg);
                throw new RuntimeException("查询FastGate平台失败: " + errorMsg);
            }

            // 设置统计结果
            statsDto.setTotalSuccessCount(totalCount);
            statsDto.setTotalRecords(totalCount);
            
            long endTime = System.currentTimeMillis();
            statsDto.setQueryDurationMs(endTime - startTime);
            statsDto.setRemarks("查询完成");

            // 记录任务执行成功
            taskManagementService.recordTaskSuccess(executionLog, 
                String.format("查询完成，当天校验成功总人数: %d 人", totalCount),
                totalCount, totalCount, 0);

            logger.info("=== 每日出入记录统计完成 ===");
            logger.info("统计日期: {}", today.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            logger.info("查询时间范围: {} 到 {}", startTimeStr, endTimeStr);
            logger.info("校验成功总人数: {} 人", totalCount);
            logger.info("查询耗时: {} 毫秒", statsDto.getQueryDurationMs());
            logger.info("===============================");

            return statsDto;

        } catch (Exception e) {
            logger.error("查询过程中发生异常", e);
            throw e;
        }
    }

    /**
     * 调用FastGate平台出入记录查询API
     * @param queryUrl 查询URL
     * @return API调用结果
     * @throws IOException
     */
    private UniResult callAccessRecordsAPI(String queryUrl) throws IOException {
        try {
            return UniHttpUtil.sendHttpGetJson(queryUrl, null);
        } catch (IOException e) {
            logger.error("调用FastGate平台出入记录查询API失败: {}", queryUrl, e);
            throw e;
        }
    }
} 
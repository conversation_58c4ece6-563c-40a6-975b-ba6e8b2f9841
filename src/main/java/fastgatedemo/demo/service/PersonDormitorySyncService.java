package fastgatedemo.demo.service;

import fastgatedemo.demo.model.PersonDormitoryRelation;
import fastgatedemo.demo.model.PersonInfo;
import fastgatedemo.demo.model.ScheduledTaskInfo;
import fastgatedemo.demo.model.TaskExecutionLog;
import fastgatedemo.demo.model.UniResult;
import fastgatedemo.demo.repository.PersonDormitoryRelationRepository;
import fastgatedemo.demo.repository.PersonRepository;
import fastgatedemo.demo.repository.ScheduledTaskInfoRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import java.util.*;
import java.util.stream.Collectors;
import javax.persistence.PersistenceContext;

/**
 * @description 人员宿舍关联定时同步服务
 */
@Service
public class PersonDormitorySyncService {

    private static final Logger logger = LoggerFactory.getLogger(PersonDormitorySyncService.class);

    /**
     * 检查任务是否启用
     * @param taskName 任务名称
     * @return 是否启用
     */
    private boolean isTaskEnabled(String taskName) {
        try {
            Optional<ScheduledTaskInfo> taskOpt = taskInfoRepository.findByTaskName(taskName);
            if (taskOpt.isPresent()) {
                Boolean enabled = taskOpt.get().getEnabled();
                return enabled != null && enabled;
            }
            // 如果任务不存在，默认为启用（向后兼容）
            logger.warn("任务 {} 在数据库中不存在，默认为启用状态", taskName);
            return true;
        } catch (Exception e) {
            logger.error("检查任务启用状态失败: {}", taskName, e);
            // 出现异常时默认为启用（避免因为数据库问题导致任务完全停止）
            return true;
        }
    }

    @Autowired
    private PersonDormitoryRelationRepository relationRepository;

    @Autowired
    private PersonRepository personRepository;

    @Autowired
    private DormitoryService dormitoryService;

    @Autowired
    private PersonService personService;

    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    private ScheduledTaskManagementService taskManagementService;

    @Autowired
    private ScheduledTaskInfoRepository taskInfoRepository;

    /**
     * 手动触发同步未同步的人员宿舍关联关系
     * @return 同步结果
     */
    public Map<String, Object> manualSyncUnsyncedPersonDormitoryRelations() {
        Map<String, Object> result = new HashMap<>();
        String taskName = "ManualPersonDormitorySyncTask";
        TaskExecutionLog executionLog = taskManagementService.recordTaskStart(taskName);

        logger.info("手动触发同步未同步的人员宿舍关联关系");

        try {
            SyncResult syncResult = performSync(executionLog);

            result.put("success", true);
            result.put("message", "手动同步完成");
            result.put("totalProcessed", syncResult.getTotalProcessed());
            result.put("successCount", syncResult.getSuccessCount());
            result.put("failedCount", syncResult.getFailedCount());

            return result;

        } catch (Exception e) {
            logger.error("手动同步异常", e);
            taskManagementService.recordTaskFailure(executionLog, "手动同步异常: " + e.getMessage());

            result.put("success", false);
            result.put("message", "手动同步失败: " + e.getMessage());
            return result;
        }
    }

    /**
     * 定时同步未同步的人员宿舍关联关系
     * 每5分钟执行一次
     */
    @Scheduled(fixedRate = 300000) // 5分钟 = 300000毫秒
    public void syncUnsyncedPersonDormitoryRelations() {
        String taskName = "PersonDormitorySyncTask";

        // 首先检查任务是否启用
        if (!isTaskEnabled(taskName)) {
            logger.debug("任务 {} 已禁用，跳过执行", taskName);
            return;
        }

        TaskExecutionLog executionLog = taskManagementService.recordTaskStart(taskName);
        logger.info("开始定时同步未同步的人员宿舍关联关系");

        try {
            performSync(executionLog);
        } catch (Exception e) {
            logger.error("定时同步异常", e);
            taskManagementService.recordTaskFailure(executionLog, "定时同步异常: " + e.getMessage());
        }
    }

    /**
     * 执行同步操作的核心方法
     * @param executionLog 执行日志
     * @return 同步结果
     */
    private SyncResult performSync(TaskExecutionLog executionLog) {
        logger.info("开始查询未同步的人员宿舍关联关系...");

        // 查询未同步的关联关系
        List<PersonDormitoryRelation> unsyncedRelations;
        try {
            unsyncedRelations = relationRepository.findByStatusAndSyncFlag(1, 0);
            logger.info("查询到 {} 条未同步的关联关系", unsyncedRelations.size());
        } catch (Exception e) {
            logger.error("查询未同步关联关系失败", e);
            taskManagementService.recordTaskFailure(executionLog, "查询未同步关联关系失败: " + e.getMessage());
            throw new RuntimeException("查询未同步关联关系失败", e);
        }

        if (unsyncedRelations.isEmpty()) {
            logger.info("没有需要同步的人员宿舍关联关系");
            taskManagementService.recordTaskSuccess(executionLog, "没有需要同步的记录", 0, 0, 0);
            return new SyncResult(0, 0, 0);
        }

        logger.info("发现{}条未同步的人员宿舍关联关系", unsyncedRelations.size());

        // 分批同步，每批50条
        int batchSize = 50;
        List<List<PersonDormitoryRelation>> batches = partitionList(unsyncedRelations, batchSize);

        int totalSuccess = 0;
        int totalFailed = 0;

        for (int i = 0; i < batches.size(); i++) {
            List<PersonDormitoryRelation> batch = batches.get(i);
            logger.info("开始同步第{}批，共{}条记录", i + 1, batch.size());

            SyncResult result = syncBatchRelations(batch);
            totalSuccess += result.getSuccessCount();
            totalFailed += result.getFailedCount();

            // 批次间休息1秒，避免对EGS平台造成压力
            if (i < batches.size() - 1) {
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    logger.warn("同步被中断");
                    break;
                }
            }
        }

        logger.info("同步完成：成功{}条，失败{}条", totalSuccess, totalFailed);

        // 记录任务执行成功
        String message = String.format("同步完成：成功%d条，失败%d条", totalSuccess, totalFailed);
        taskManagementService.recordTaskSuccess(executionLog, message,
                                              unsyncedRelations.size(), totalSuccess, totalFailed);

        return new SyncResult(totalSuccess, totalFailed, unsyncedRelations.size());
    }

    /**
     * 手动触发同步未同步的关联关系
     * @return 同步结果
     */
    public Map<String, Object> manualSyncUnsyncedRelations() {
        logger.info("手动触发同步未同步的人员宿舍关联关系");
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            List<PersonDormitoryRelation> unsyncedRelations = relationRepository.findByStatusAndSyncFlag(1, 0);
            
            if (unsyncedRelations.isEmpty()) {
                result.put("success", true);
                result.put("message", "没有需要同步的人员宿舍关联关系");
                result.put("totalCount", 0);
                result.put("successCount", 0);
                result.put("failedCount", 0);
                return result;
            }
            
            logger.info("发现{}条未同步的人员宿舍关联关系", unsyncedRelations.size());
            
            // 分批同步
            int batchSize = 50;
            List<List<PersonDormitoryRelation>> batches = partitionList(unsyncedRelations, batchSize);
            
            int totalSuccess = 0;
            int totalFailed = 0;
            
            for (List<PersonDormitoryRelation> batch : batches) {
                SyncResult syncResult = syncBatchRelations(batch);
                totalSuccess += syncResult.getSuccessCount();
                totalFailed += syncResult.getFailedCount();
            }
            
            result.put("success", true);
            result.put("message", String.format("同步完成：成功%d条，失败%d条", totalSuccess, totalFailed));
            result.put("totalCount", unsyncedRelations.size());
            result.put("successCount", totalSuccess);
            result.put("failedCount", totalFailed);
            
            logger.info("手动同步完成：成功{}条，失败{}条", totalSuccess, totalFailed);
            
        } catch (Exception e) {
            logger.error("手动同步人员宿舍关联关系异常", e);
            result.put("success", false);
            result.put("message", "同步异常：" + e.getMessage());
        }
        
        return result;
    }

    /**
     * 同步一批关联关系
     * @param relations 关联关系列表
     * @return 同步结果
     */
    @Transactional
    private SyncResult syncBatchRelations(List<PersonDormitoryRelation> relations) {
        int successCount = 0;
        int failedCount = 0;
        List<String> successIds = new ArrayList<>();

        try {
            // 逐个同步人员
            for (PersonDormitoryRelation relation : relations) {
                try {
                    // 查询人员信息
                    Optional<PersonInfo> personOpt = personRepository.findByPersonCode(relation.getPersonCode());
                    if (!personOpt.isPresent()) {
                        logger.warn("未找到人员信息: {}", relation.getPersonCode());
                        failedCount++;
                        continue;
                    }

                    PersonInfo personInfo = personOpt.get();

                    // 设置宿舍编码作为区域编码
                    personInfo.setAreaCode(relation.getDormitoryCode());

                    // 调用人员同步方法
                    boolean syncSuccess = personService.addPerson(personInfo);

                    if (syncSuccess) {
                        // 同步成功，记录ID
                        successIds.add(relation.getId());
                        successCount++;
                        logger.debug("人员宿舍关联同步成功: {} -> {}",
                                   relation.getPersonCode(), relation.getDormitoryCode());
                    } else {
                        failedCount++;
                        logger.warn("人员宿舍关联同步失败: {} -> {} - addPerson返回false",
                                  relation.getPersonCode(), relation.getDormitoryCode());
                    }

                    // 添加延时避免频繁请求
                    Thread.sleep(100);

                } catch (Exception e) {
                    failedCount++;
                    logger.error("人员宿舍关联同步异常: {} -> {}",
                               relation.getPersonCode(), relation.getDormitoryCode(), e);
                }
            }

            // 批量更新同步标识
            if (!successIds.isEmpty()) {
                relationRepository.updateSyncFlagByIds(successIds, 1);
                logger.info("批量更新同步标识成功：{}条记录", successIds.size());
            }

        } catch (Exception e) {
            logger.error("批量同步异常", e);
            failedCount = relations.size();
        }

        return new SyncResult(successCount, failedCount);
    }

    /**
     * 将列表分割成指定大小的批次
     * @param list 原始列表
     * @param batchSize 批次大小
     * @return 分批后的列表
     */
    private <T> List<List<T>> partitionList(List<T> list, int batchSize) {
        List<List<T>> partitions = new ArrayList<>();
        for (int i = 0; i < list.size(); i += batchSize) {
            partitions.add(list.subList(i, Math.min(i + batchSize, list.size())));
        }
        return partitions;
    }

    /**
     * 获取同步统计信息
     * @return 统计信息
     */
    public Map<String, Object> getSyncStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        try {
            Long totalCount = relationRepository.count();
            Long syncedCount = relationRepository.countSyncedRelations();
            Long unsyncedCount = relationRepository.countUnsyncedRelations();
            
            stats.put("totalCount", totalCount);
            stats.put("syncedCount", syncedCount);
            stats.put("unsyncedCount", unsyncedCount);
            stats.put("syncRate", totalCount > 0 ? (double) syncedCount / totalCount * 100 : 0.0);
            
        } catch (Exception e) {
            logger.error("获取同步统计信息异常", e);
            stats.put("error", e.getMessage());
        }
        
        return stats;
    }

    /**
     * 同步结果内部类
     */
    private static class SyncResult {
        private final int successCount;
        private final int failedCount;
        private final int totalProcessed;

        public SyncResult(int successCount, int failedCount) {
            this.successCount = successCount;
            this.failedCount = failedCount;
            this.totalProcessed = successCount + failedCount;
        }

        public SyncResult(int successCount, int failedCount, int totalProcessed) {
            this.successCount = successCount;
            this.failedCount = failedCount;
            this.totalProcessed = totalProcessed;
        }

        public int getSuccessCount() {
            return successCount;
        }

        public int getFailedCount() {
            return failedCount;
        }

        public int getTotalProcessed() {
            return totalProcessed;
        }
    }
}

package fastgatedemo.demo.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import fastgatedemo.demo.dto.AccessRecordDTO;
import fastgatedemo.demo.dto.DormitoryStatusDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 视图消息处理服务类
 * 负责解析JSON消息并更新学生在寝状态
 */
@Service
public class ViewMessageProcessService {

    private static final Logger logger = LoggerFactory.getLogger(ViewMessageProcessService.class);

    @Autowired
    private StudentStatusCacheService statusCacheService;

    @Autowired
    private AsyncBatchPersistenceService persistenceService;

    @Autowired
    private DashboardWebSocketService webSocketService;

    /**
     * 处理接收到的消息
     * @param messageJson JSON消息字符串
     * @return 处理结果
     */
    public boolean processMessage(String messageJson) {
        try {
            logger.info("开始处理消息: {}", messageJson);
            
            JSONObject jsonObject = JSON.parseObject(messageJson);
            boolean hasProcessedRecord = false;
            
            // 检查是否包含PersonCardObjectList
            if (jsonObject.containsKey("PersonCardObjectList")) {
                JSONArray personCardList = jsonObject.getJSONArray("PersonCardObjectList");
                if (personCardList != null && !personCardList.isEmpty()) {
                    logger.info("发现PersonCardObjectList，包含{}条记录", personCardList.size());
                    for (int i = 0; i < personCardList.size(); i++) {
                        JSONObject personCard = personCardList.getJSONObject(i);
                        if (processPersonCardRecord(personCard)) {
                            hasProcessedRecord = true;
                        }
                    }
                }
            }
            
            // 检查是否包含PersonCheckObjectList  
            if (jsonObject.containsKey("PersonCheckObjectList")) {
                JSONArray personCheckList = jsonObject.getJSONArray("PersonCheckObjectList");
                if (personCheckList != null && !personCheckList.isEmpty()) {
                    logger.info("发现PersonCheckObjectList，包含{}条记录", personCheckList.size());
                    for (int i = 0; i < personCheckList.size(); i++) {
                        JSONObject personCheck = personCheckList.getJSONObject(i);
                        if (processPersonCheckRecord(personCheck)) {
                            hasProcessedRecord = true;
                        }
                    }
                }
            }
            
            // 如果没有找到预期的数据结构，尝试直接解析
            if (!hasProcessedRecord) {
                logger.info("未找到标准数据结构，尝试直接解析消息");
                hasProcessedRecord = processDirectMessage(jsonObject);
            }
            
            return hasProcessedRecord;
            
        } catch (Exception e) {
            logger.error("处理消息时发生错误: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 处理PersonCard记录
     */
    private boolean processPersonCardRecord(JSONObject personCard) {
        try {
            // 提取通行记录信息
            AccessRecordDTO accessRecord = extractAccessRecord(personCard, "PersonCard");
            
            if (accessRecord != null && isValidAccessRecord(accessRecord)) {
                return processAccessRecord(accessRecord);
            }
            
            return false;
            
        } catch (Exception e) {
            logger.error("处理PersonCard记录时发生错误: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 处理PersonCheck记录
     */
    private boolean processPersonCheckRecord(JSONObject personCheck) {
        try {
            // 提取通行记录信息
            AccessRecordDTO accessRecord = extractAccessRecord(personCheck, "PersonCheck");
            
            if (accessRecord != null && isValidAccessRecord(accessRecord)) {
                return processAccessRecord(accessRecord);
            }
            
            return false;
            
        } catch (Exception e) {
            logger.error("处理PersonCheck记录时发生错误: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 直接处理消息
     */
    private boolean processDirectMessage(JSONObject jsonObject) {
        try {
            // 尝试从直接消息中提取通行记录
            AccessRecordDTO accessRecord = extractAccessRecord(jsonObject, "Direct");
            
            if (accessRecord != null && isValidAccessRecord(accessRecord)) {
                return processAccessRecord(accessRecord);
            }
            
            logger.info("直接消息未包含有效的通行记录信息");
            return false;
            
        } catch (Exception e) {
            logger.error("直接处理消息时发生错误: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 从JSON对象中提取通行记录
     * @param jsonObject JSON对象
     * @param recordType 记录类型
     * @return 通行记录DTO
     */
    private AccessRecordDTO extractAccessRecord(JSONObject jsonObject, String recordType) {
        try {
            AccessRecordDTO accessRecord = new AccessRecordDTO();
            
            // 基础信息提取
            accessRecord.setPersonCode(getStringValue(jsonObject, "CodeNo", "PersonCode", "personCode", "person_code"));
            accessRecord.setPersonName(getStringValue(jsonObject, "Name", "name", "PersonName"));
            accessRecord.setDeviceCode(getStringValue(jsonObject, "DeviceID", "deviceId", "device_id"));
            accessRecord.setDeviceName(getStringValue(jsonObject, "DeviceName", "deviceName", "device_name"));
            accessRecord.setPlaceCode(getStringValue(jsonObject, "PlaceCode", "placeCode", "place_code"));
            accessRecord.setPlaceName(getStringValue(jsonObject, "PlaceName", "placeName", "place_name"));
            accessRecord.setAreaCode(getStringValue(jsonObject, "AreaCode", "areaCode", "area_code"));
            accessRecord.setAreaName(getStringValue(jsonObject, "AreaName", "areaName", "area_name"));
            
            // 通行方向 - 关键字段
            Integer inOrOut = getIntegerValue(jsonObject, "DeviceDirection", "InOrOut", "inorout", "Direction", "direction");
            accessRecord.setInOrOut(inOrOut);
            
            // 时间信息提取
            LocalDateTime passTime = extractPassTime(jsonObject);
            accessRecord.setPassTime(passTime);
            
            if (passTime != null) {
                DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm:ss");
                accessRecord.setRecordDate(passTime.format(dateFormatter));
                accessRecord.setRecordTime(passTime.format(timeFormatter));
            }
            
            // 其他信息
            accessRecord.setMatchConfidence(getIntegerValue(jsonObject, "MatchConfidence", "matchConfidence", "match_confidence"));
            accessRecord.setDeviceIp(getStringValue(jsonObject, "DeviceIP", "deviceIp", "device_ip"));
            accessRecord.setTemperature(getDoubleValue(jsonObject, "Temperature", "temperature"));
            
            logger.info("提取通行记录[{}]: personCode={}, personName={}, inOrOut={}, passTime={}, deviceName={}",
                        recordType, accessRecord.getPersonCode(), accessRecord.getPersonName(),
                        accessRecord.getInOrOut(), accessRecord.getPassTime(), accessRecord.getDeviceName());
            
            return accessRecord;
            
        } catch (Exception e) {
            logger.error("提取通行记录失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 提取通行时间
     * @param jsonObject JSON对象
     * @return 通行时间
     */
    private LocalDateTime extractPassTime(JSONObject jsonObject) {
        try {
            // 尝试多种时间字段名称
            String timeStr = getStringValue(jsonObject, "CapTime", "CheckTime", "PassTime", 
                                          "capTime", "checkTime", "passTime", "pass_time");
            
            if (timeStr != null && !timeStr.trim().isEmpty()) {
                // 尝试解析多种时间格式
                return parseDateTime(timeStr.trim());
            }
            
            // 如果没有时间字段，使用当前时间
            return LocalDateTime.now();
            
        } catch (Exception e) {
            logger.warn("解析通行时间失败，使用当前时间: {}", e.getMessage());
            return LocalDateTime.now();
        }
    }

    /**
     * 解析日期时间字符串
     * @param timeStr 时间字符串
     * @return LocalDateTime
     */
    private LocalDateTime parseDateTime(String timeStr) {
        // 特殊处理：14位数字格式 (yyyyMMddHHmmss) + 可选毫秒
        if (timeStr.matches("\\d{14}\\d*")) {
            try {
                // 取前14位作为标准时间格式
                String standardTime = timeStr.substring(0, 14);
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
                return LocalDateTime.parse(standardTime, formatter);
            } catch (Exception e) {
                logger.warn("解析14位数字时间格式失败: {}, error: {}", timeStr, e.getMessage());
            }
        }

        // 常见的时间格式
        String[] patterns = {
            "yyyy-MM-dd HH:mm:ss",
            "yyyy-MM-dd'T'HH:mm:ss",
            "yyyy-MM-dd'T'HH:mm:ss.SSS",
            "yyyy/MM/dd HH:mm:ss",
            "yyyyMMdd HH:mm:ss",
            "yyyyMMddHHmmss"
        };

        for (String pattern : patterns) {
            try {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
                return LocalDateTime.parse(timeStr, formatter);
            } catch (Exception ignored) {
                // 继续尝试下一种格式
            }
        }

        throw new RuntimeException("无法解析时间格式: " + timeStr);
    }

    /**
     * 验证通行记录是否有效
     * @param accessRecord 通行记录
     * @return 是否有效
     */
    private boolean isValidAccessRecord(AccessRecordDTO accessRecord) {
        // 必须有人员编码
        if (accessRecord.getPersonCode() == null || accessRecord.getPersonCode().trim().isEmpty()) {
            logger.warn("通行记录缺少人员编码，忽略处理。记录详情: personName={}, deviceName={}, inOrOut={}",
                       accessRecord.getPersonName(), accessRecord.getDeviceName(), accessRecord.getInOrOut());
            return false;
        }

        // 必须有进出方向
        if (accessRecord.getInOrOut() == null) {
            logger.warn("通行记录缺少进出方向，忽略处理: personCode={}, personName={}, deviceName={}",
                       accessRecord.getPersonCode(), accessRecord.getPersonName(), accessRecord.getDeviceName());
            return false;
        }

        // 进出方向必须是1或2
        if (accessRecord.getInOrOut() != 1 && accessRecord.getInOrOut() != 2) {
            logger.warn("通行记录进出方向无效({}，应为1或2)，忽略处理: personCode={}, personName={}",
                       accessRecord.getInOrOut(), accessRecord.getPersonCode(), accessRecord.getPersonName());
            return false;
        }

        logger.info("通行记录验证通过: personCode={}, personName={}, inOrOut={}",
                   accessRecord.getPersonCode(), accessRecord.getPersonName(), accessRecord.getInOrOut());
        return true;
    }

    /**
     * 处理通行记录 - 核心业务逻辑
     * @param accessRecord 通行记录
     * @return 处理是否成功
     */
    private boolean processAccessRecord(AccessRecordDTO accessRecord) {
        try {
            logger.info("处理通行记录: personCode={}, personName={}, inOrOut={}, passTime={}", 
                       accessRecord.getPersonCode(), accessRecord.getPersonName(), 
                       accessRecord.getInOrOut(), accessRecord.getPassTime());
            
            // 1. 立即更新Redis缓存中的学生状态
            boolean cacheUpdated = statusCacheService.updateStudentStatus(accessRecord);
            if (!cacheUpdated) {
                logger.warn("更新学生状态缓存失败: personCode={}", accessRecord.getPersonCode());
            }
            
            // 2. 添加到异步持久化队列
            boolean queueAdded = persistenceService.addToQueue(accessRecord);
            if (!queueAdded) {
                logger.warn("添加到持久化队列失败: personCode={}", accessRecord.getPersonCode());
            }

            // 3. 触发WebSocket实时推送
            if (cacheUpdated) {
                try {
                    DormitoryStatusDTO pushRecord = convertToDormitoryStatusDTO(accessRecord);
                    webSocketService.pushNewRecord(pushRecord);
                    logger.debug("WebSocket推送成功: personCode={}", accessRecord.getPersonCode());
                } catch (Exception e) {
                    logger.warn("WebSocket推送失败: personCode={}, error={}",
                               accessRecord.getPersonCode(), e.getMessage());
                    // WebSocket推送失败不影响主流程
                }
            }

            // 只要缓存更新成功就认为处理成功（因为缓存是查询的主要数据源）
            if (cacheUpdated) {
                logger.info("通行记录处理成功: personCode={}, 状态={}",
                           accessRecord.getPersonCode(), accessRecord.getInOrOutDescription());
                return true;
            } else {
                logger.error("通行记录处理失败: personCode={}", accessRecord.getPersonCode());
                return false;
            }
            
        } catch (Exception e) {
            logger.error("处理通行记录异常: personCode={}, error={}",
                        accessRecord.getPersonCode(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 将AccessRecordDTO转换为DormitoryStatusDTO用于WebSocket推送
     * @param accessRecord 通行记录DTO
     * @return 寝室状态DTO
     */
    private DormitoryStatusDTO convertToDormitoryStatusDTO(AccessRecordDTO accessRecord) {
        DormitoryStatusDTO dto = new DormitoryStatusDTO();

        // 基本信息
        dto.setPersonCode(accessRecord.getPersonCode());
        dto.setPersonName(accessRecord.getPersonName());
        dto.setLastInOrOut(accessRecord.getInOrOut());
        dto.setLastPassTime(accessRecord.getPassTime());
        dto.setIsInDormitory(accessRecord.getInOrOut() != null && accessRecord.getInOrOut() == 1);

        // 设备和区域信息
        dto.setLastDeviceName(accessRecord.getDeviceName());
        dto.setLastAreaName(accessRecord.getAreaName());

        // 格式化时间字符串
        if (accessRecord.getPassTime() != null) {
            DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm:ss");
            dto.setLastPassTimeStr(accessRecord.getPassTime().format(timeFormatter));
        }

        // 设置状态描述
        dto.setLastInOrOutDesc(accessRecord.getInOrOutDescription());
        dto.setDormitoryStatusDesc(dto.getIsInDormitory() ? "已归寝室" : "未归寝室");

        // 设置查询日期为当前日期
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        dto.setQueryDate(LocalDateTime.now().format(dateFormatter));

        // 补充楼栋信息（用于前端筛选）
        enrichBuildingInfo(dto);

        return dto;
    }

    /**
     * 补充楼栋信息（用于前端楼栋筛选）
     * @param dto 寝室状态DTO
     */
    private void enrichBuildingInfo(DormitoryStatusDTO dto) {
        try {
            // 从区域名称中提取楼栋信息
            String areaName = dto.getLastAreaName();
            if (areaName != null && !areaName.trim().isEmpty()) {
                // 尝试从区域名称中解析楼栋信息
                // 例如："1号楼1层" -> buildingName="1号楼"
                String buildingName = extractBuildingNameFromArea(areaName);
                if (buildingName != null) {
                    dto.setBuildingName(buildingName);
                }
            }

            // 如果区域名称无法提取楼栋信息，尝试从设备名称提取
            if (dto.getBuildingName() == null || dto.getBuildingName().trim().isEmpty()) {
                String deviceName = dto.getLastDeviceName();
                if (deviceName != null && !deviceName.trim().isEmpty()) {
                    String buildingName = extractBuildingNameFromDevice(deviceName);
                    if (buildingName != null) {
                        dto.setBuildingName(buildingName);
                    }
                }
            }

            // 如果仍然没有楼栋信息，设置默认值
            if (dto.getBuildingName() == null || dto.getBuildingName().trim().isEmpty()) {
                dto.setBuildingName("未知楼栋");
            }

        } catch (Exception e) {
            logger.warn("补充楼栋信息失败: personCode={}, error={}",
                       dto.getPersonCode(), e.getMessage());
            dto.setBuildingName("未知楼栋");
        }
    }

    /**
     * 从区域名称中提取楼栋名称
     * @param areaName 区域名称
     * @return 楼栋名称
     */
    private String extractBuildingNameFromArea(String areaName) {
        if (areaName == null || areaName.trim().isEmpty()) {
            return null;
        }

        // 常见模式：1号楼1层、2号楼3层等
        if (areaName.contains("号楼")) {
            int index = areaName.indexOf("号楼");
            if (index > 0) {
                return areaName.substring(0, index + 2); // 包含"号楼"
            }
        }

        // 其他模式可以根据实际数据格式扩展
        return null;
    }

    /**
     * 从设备名称中提取楼栋名称
     * @param deviceName 设备名称
     * @return 楼栋名称
     */
    private String extractBuildingNameFromDevice(String deviceName) {
        if (deviceName == null || deviceName.trim().isEmpty()) {
            return null;
        }

        // 常见模式：1号楼门禁、2号楼闸机等
        if (deviceName.contains("号楼")) {
            int index = deviceName.indexOf("号楼");
            if (index > 0) {
                return deviceName.substring(0, index + 2); // 包含"号楼"
            }
        }

        return null;
    }

    /**
     * 获取字符串值（支持多个可能的字段名）
     */
    private String getStringValue(JSONObject json, String... fieldNames) {
        for (String fieldName : fieldNames) {
            if (json.containsKey(fieldName)) {
                String value = json.getString(fieldName);
                if (value != null && !value.trim().isEmpty()) {
                    return value.trim();
                }
            }
        }
        return null;
    }

    /**
     * 获取整数值（支持多个可能的字段名）
     */
    private Integer getIntegerValue(JSONObject json, String... fieldNames) {
        for (String fieldName : fieldNames) {
            if (json.containsKey(fieldName)) {
                Object value = json.get(fieldName);
                if (value != null) {
                    try {
                        if (value instanceof Number) {
                            return ((Number) value).intValue();
                        } else {
                            return Integer.parseInt(value.toString());
                        }
                    } catch (NumberFormatException e) {
                        logger.warn("无法解析整数值: {}={}", fieldName, value);
                    }
                }
            }
        }
        return null;
    }

    /**
     * 获取双精度值（支持多个可能的字段名）
     */
    private Double getDoubleValue(JSONObject json, String... fieldNames) {
        for (String fieldName : fieldNames) {
            if (json.containsKey(fieldName)) {
                Object value = json.get(fieldName);
                if (value != null) {
                    try {
                        if (value instanceof Number) {
                            return ((Number) value).doubleValue();
                        } else {
                            return Double.parseDouble(value.toString());
                        }
                    } catch (NumberFormatException e) {
                        logger.warn("无法解析双精度值: {}={}", fieldName, value);
                    }
                }
            }
        }
        return null;
    }
}
package fastgatedemo.demo.service;

import fastgatedemo.demo.dto.FacePhotoSyncIssueDto;
import fastgatedemo.demo.model.ScheduledTaskInfo;
import fastgatedemo.demo.model.TaskExecutionLog;
import fastgatedemo.demo.model.PersonInfo;
import fastgatedemo.demo.model.MiddlewareFacePhoto;
import fastgatedemo.demo.repository.FacePhotoRepository;
import fastgatedemo.demo.repository.PersonRepository;
import fastgatedemo.demo.repository.ScheduledTaskInfoRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.PageImpl;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @description 人脸照片同步检查服务
 */
@Service
public class FacePhotoSyncCheckService {

    private static final Logger logger = LoggerFactory.getLogger(FacePhotoSyncCheckService.class);

    @Autowired
    private FacePhotoRepository facePhotoRepository;

    @Autowired
    private PersonRepository personRepository;

    @Autowired
    private PersonService personService;

    @Autowired
    private ScheduledTaskManagementService taskManagementService;

    @Autowired
    private ScheduledTaskInfoRepository taskInfoRepository;

    // 修复功能配置
    @Value("${face.photo.sync.fix.enabled:false}")
    private boolean fixEnabled;

    @Value("${face.photo.sync.fix.batch-size:10}")
    private int batchSize;

    /**
     * 检查任务是否启用
     * @param taskName 任务名称
     * @return 是否启用
     */
    private boolean isTaskEnabled(String taskName) {
        try {
            Optional<ScheduledTaskInfo> taskOpt = taskInfoRepository.findByTaskName(taskName);
            if (taskOpt.isPresent()) {
                Boolean enabled = taskOpt.get().getEnabled();
                return enabled != null && enabled;
            }
            // 如果任务不存在，默认为启用（向后兼容）
            logger.warn("任务 {} 在数据库中不存在，默认为启用状态", taskName);
            return true;
        } catch (Exception e) {
            logger.error("检查任务启用状态失败: {}", taskName, e);
            // 出现异常时默认为启用（避免因为数据库问题导致任务完全停止）
            return true;
        }
    }

    /**
     * 定时检查人脸照片同步问题
     * 每小时执行一次
     */
    @Scheduled(fixedRate = 60000) // 1分钟 = 60000毫秒
    public void checkFacePhotoSyncIssues() {
        String taskName = "FacePhotoSyncCheckTask";

        // 首先检查任务是否启用
        if (!isTaskEnabled(taskName)) {
            logger.debug("任务 {} 已禁用，跳过执行", taskName);
            return;
        }

        TaskExecutionLog executionLog = null;
        try {
            executionLog = taskManagementService.recordTaskStart(taskName);
            logger.info("开始定时检查人脸照片同步问题");
            performCheck(executionLog);
        } catch (Exception e) {
            logger.error("定时检查异常", e);
            
            // 如果是事务相关异常，尝试不记录日志直接执行检查
            if (e.getMessage() != null && 
                (e.getMessage().contains("autoCommit") || e.getMessage().contains("TransactionException"))) {
                logger.warn("检测到事务异常，尝试不记录执行日志直接执行检查");
                try {
                    performCheckWithoutLogging();
                } catch (Exception ex) {
                    logger.error("无日志模式检查也失败", ex);
                }
            } else if (executionLog != null) {
                taskManagementService.recordTaskFailure(executionLog, "定时检查异常: " + e.getMessage());
            }
        }
    }

    /**
     * 手动触发人脸照片同步检查
     * @return 检查结果
     */
    public Map<String, Object> manualCheckFacePhotoSyncIssues() {
        Map<String, Object> result = new HashMap<>();
        String taskName = "ManualFacePhotoSyncCheckTask";
        TaskExecutionLog executionLog = taskManagementService.recordTaskStart(taskName);

        logger.info("手动触发人脸照片同步检查");

        try {
            SyncCheckResult checkResult = performCheck(executionLog);

            result.put("success", true);
            result.put("message", "手动检查完成");
            result.put("totalIssues", checkResult.getTotalIssues());
            result.put("issueTypes", checkResult.getIssueTypes());

            return result;

        } catch (Exception e) {
            logger.error("手动检查异常", e);
            taskManagementService.recordTaskFailure(executionLog, "手动检查异常: " + e.getMessage());

            result.put("success", false);
            result.put("message", "手动检查失败: " + e.getMessage());
            return result;
        }
    }

    /**
     * 无日志模式的检查方法，用于事务异常时的降级处理
     */
    private void performCheckWithoutLogging() {
        logger.info("开始无日志模式的人脸照片同步检查...");
        
        try {
            // 统计问题总数
            Long totalIssues = facePhotoRepository.countPersonsWithFacePhotoIssues();
            
            // 分析问题类型分布
            Map<String, Integer> issueTypes = new HashMap<>();
            int needSyncCount = 0;  // 需要同步的数量（标识不为1的数据）
            
            if (totalIssues > 0) {
                // 分批查询详细数据进行统计
                int pageSize = 100;
                int totalPages = (int) Math.ceil(totalIssues.doubleValue() / pageSize);
                
                for (int page = 0; page < totalPages; page++) {
                    Pageable pageable = PageRequest.of(page, pageSize);
                    Page<Object[]> issuesPage = facePhotoRepository.findPersonsWithFacePhotoIssues(pageable);
                    
                    for (Object[] row : issuesPage.getContent()) {
                        String issueType = (String) row[8]; // issue_type字段
                        issueTypes.put(issueType, issueTypes.getOrDefault(issueType, 0) + 1);
                        
                        // 统计需要同步的数量（有人脸照片但标识不为1的数据）
                        if ("人脸照片未同步".equals(issueType) || "人脸照片同步标识为空".equals(issueType)) {
                            needSyncCount++;
                        }
                    }
                }

                // 改进的日志输出，显示详细统计
                logger.info("发现 {} 条人脸照片同步问题，其中 {} 条需要同步（标识不为1的数据）", totalIssues, needSyncCount);
                for (Map.Entry<String, Integer> entry : issueTypes.entrySet()) {
                    logger.info("  - {}: {} 条", entry.getKey(), entry.getValue());
                }

                // 在无日志模式下不执行修复，只进行检查和统计
                logger.info("无日志模式：仅执行检查统计，不进行修复操作");
            } else {
                logger.info("检查完成：未发现同步问题");
            }

        } catch (Exception e) {
            logger.error("无日志模式检查也失败", e);
        }
    }

    /**
     * 执行检查操作的核心方法
     * @param executionLog 执行日志
     * @return 检查结果
     */
    private SyncCheckResult performCheck(TaskExecutionLog executionLog) {
        logger.info("开始查询人脸照片同步问题...");

        try {
            // 统计问题总数
            Long totalIssues = facePhotoRepository.countPersonsWithFacePhotoIssues();
            
            // 分析问题类型分布
            Map<String, Integer> issueTypes = new HashMap<>();
            int needSyncCount = 0;  // 需要同步的数量（标识不为1的数据）
            
            if (totalIssues > 0) {
                // 分批查询详细数据进行统计
                int pageSize = 100;
                int totalPages = (int) Math.ceil(totalIssues.doubleValue() / pageSize);
                
                for (int page = 0; page < totalPages; page++) {
                    Pageable pageable = PageRequest.of(page, pageSize);
                    Page<Object[]> issuesPage = facePhotoRepository.findPersonsWithFacePhotoIssues(pageable);
                    
                    for (Object[] row : issuesPage.getContent()) {
                        String issueType = (String) row[8]; // issue_type字段
                        issueTypes.put(issueType, issueTypes.getOrDefault(issueType, 0) + 1);
                        
                        // 统计需要同步的数量（有人脸照片但标识不为1的数据）
                        if ("人脸照片未同步".equals(issueType) || "人脸照片同步标识为空".equals(issueType)) {
                            needSyncCount++;
                        }
                    }
                }

                // 改进的日志输出，显示详细统计
                logger.info("发现 {} 条人脸照片同步问题，其中 {} 条需要同步（标识不为1的数据）", totalIssues, needSyncCount);
                for (Map.Entry<String, Integer> entry : issueTypes.entrySet()) {
                    logger.info("  - {}: {} 条", entry.getKey(), entry.getValue());
                }

                // 如果启用了修复功能，则执行修复
                if (fixEnabled) {
                    logger.info("修复功能已启用，开始执行人脸照片同步修复...");
                    SyncFixResult fixResult = performSyncFix(executionLog);
                    
                    String message = String.format("检查并修复完成：发现%d条同步问题（其中%d条需要同步），修复成功%d条，修复失败%d条", 
                                                  totalIssues, needSyncCount, fixResult.getSuccessCount(), fixResult.getFailCount());
                    taskManagementService.recordTaskSuccess(executionLog, message, 
                                                          totalIssues.intValue(), fixResult.getSuccessCount(), fixResult.getFailCount());
                } else {
                    String message = String.format("检查完成：发现%d条同步问题（其中%d条需要同步，修复功能未启用）", totalIssues, needSyncCount);
                    taskManagementService.recordTaskSuccess(executionLog, message, 
                                                          totalIssues.intValue(), 0, 0);
                }
            } else {
                logger.info("检查完成：未发现同步问题");
                String message = "检查完成：未发现同步问题";
                taskManagementService.recordTaskSuccess(executionLog, message, 0, 0, 0);
            }

            return new SyncCheckResult(totalIssues.intValue(), issueTypes);

        } catch (Exception e) {
            logger.error("查询人脸照片同步问题失败", e);
            taskManagementService.recordTaskFailure(executionLog, "查询同步问题失败: " + e.getMessage());
            throw new RuntimeException("查询同步问题失败", e);
        }
    }

    /**
     * 执行人脸照片同步修复
     * @param executionLog 执行日志
     * @return 修复结果
     */
    private SyncFixResult performSyncFix(TaskExecutionLog executionLog) {
        int successCount = 0;
        int failCount = 0;
        List<String> failedPersons = new ArrayList<>();

        try {
            // 一次性获取所有需要修复的人员列表（避免分页修改数据的问题）
            List<Object[]> personsNeedingSync = facePhotoRepository.findAllPersonsNeedingFacePhotoSync();
            int totalCount = personsNeedingSync.size();
            
            logger.info("开始批量修复，需要修复{}条记录，每{}条进行一次进度汇报", totalCount, batchSize);

            int processedCount = 0;
            for (Object[] row : personsNeedingSync) {
                // 检查线程是否被中断
                if (Thread.currentThread().isInterrupted()) {
                    logger.warn("检测到线程中断信号，停止修复操作。已处理 {} 条，成功 {} 条，失败 {} 条", processedCount, successCount, failCount);
                    break;
                }
                
                String personCode = (String) row[0];
                String personName = (String) row[1];
                processedCount++;
                
                // 每处理batchSize条记录输出一次进度
                if (processedCount % batchSize == 0 || processedCount == totalCount) {
                    logger.info("修复进度: {}/{} ({:.1f}%)", processedCount, totalCount, 
                               (processedCount * 100.0 / totalCount));
                }
                
                try {
                    // 进行修复
                    boolean fixSuccess = fixSinglePersonFacePhoto(personCode, personName);
                    
                    if (fixSuccess) {
                        successCount++;
                        logger.debug("修复成功: {} ({})", personName, personCode);
                    } else {
                        failCount++;
                        failedPersons.add(personName + "(" + personCode + ") - EGS平台更新失败");
                        logger.warn("修复失败: {} ({}) - EGS平台更新失败", personName, personCode);
                    }
                    
                    // 添加延时避免频繁请求EGS平台
                    Thread.sleep(100);
                    
                } catch (InterruptedException ie) {
                    // 线程被中断，记录当前进度并优雅退出
                    logger.warn("修复过程被中断，已处理 {} 条，成功 {} 条，失败 {} 条", processedCount, successCount, failCount);
                    Thread.currentThread().interrupt(); // 恢复中断状态
                    break;
                } catch (Exception e) {
                    failCount++;
                    failedPersons.add(personName + "(" + personCode + ") - 异常: " + e.getMessage());
                    logger.error("修复人员异常: {} ({})", personName, personCode, e);
                }
            }

            logger.info("人脸照片同步修复完成: 成功{}条, 失败{}条", successCount, failCount);
            
            if (!failedPersons.isEmpty() && failedPersons.size() <= 10) {
                logger.warn("修复失败的人员: {}", String.join(", ", failedPersons));
            }

            return new SyncFixResult(successCount, failCount, failedPersons);

        } catch (Exception e) {
            logger.error("执行人脸照片同步修复异常", e);
            throw new RuntimeException("修复操作异常: " + e.getMessage(), e);
        }
    }

    /**
     * 修复单个人员的人脸照片同步状态
     * @param personCode 人员编码
     * @param personName 人员姓名
     * @return 修复是否成功
     */
    private boolean fixSinglePersonFacePhoto(String personCode, String personName) {
        try {
            // 查询完整的人员信息
            Optional<PersonInfo> personOpt = personRepository.findByPersonCode(personCode);
            if (!personOpt.isPresent()) {
                logger.warn("未找到人员信息: {} ({})", personName, personCode);
                return false;
            }

            PersonInfo personInfo = personOpt.get();
            
            // 手动处理人脸照片数据转换（参考ensurePersonPicture逻辑）
            logger.info("开始为人员 {} ({}) 处理人脸照片数据同步", personName, personCode);
            
            try {
                // 从face_photo表查询照片数据
                List<MiddlewareFacePhoto> facePhotos = facePhotoRepository.findByPerCode(personCode);
                if (facePhotos.isEmpty()) {
                    logger.warn("人员 {} ({}) 在face_photo表中没有照片数据", personName, personCode);
                    return false;
                }
                
                MiddlewareFacePhoto facePhoto = facePhotos.get(0);
                if (facePhoto.getPhotoData() == null || facePhoto.getPhotoData().trim().isEmpty()) {
                    logger.warn("人员 {} ({}) 的照片数据为空", personName, personCode);
                    return false;
                }
                
                // 处理十六进制格式的照片数据（如 \xffd8ffe0...）
                byte[] photoBytes = convertHexStringToBytes(facePhoto.getPhotoData());
                if (photoBytes == null || photoBytes.length == 0) {
                    logger.warn("人员 {} ({}) 的照片数据转换失败", personName, personCode);
                    return false;
                }
                
                // 设置照片数据到PersonInfo
                personInfo.setPersonPicture(photoBytes);
                logger.info("为人员 {} ({}) 成功转换照片数据 ({}字节)", personName, personCode, photoBytes.length);
                
                // 调用updatePerson进行人脸照片同步
                boolean updateSuccess = personService.updatePerson(personInfo);
                logger.info("人员 {} ({}) EGS平台人脸照片更新API调用完成，结果: {}", 
                           personName, personCode, updateSuccess ? "成功" : "失败");
                
                if (updateSuccess) {
                    // 只有当外部API调用成功后，才更新本地同步标志
                    updateFacePhotoSyncFlag(personCode);
                    logger.info("人员 {} ({}) 人脸照片修复完成", personName, personCode);
                    return true;
                } else {
                    logger.warn("人员 {} ({}) EGS平台更新失败，本地同步标志不会更新", personName, personCode);
                    return false;
                }
                
            } catch (Exception e) {
                logger.error("调用EGS平台更新人员 {} ({}) 人脸照片失败: {}", personName, personCode, e.getMessage());
                // 外部API调用失败，不更新本地同步标志，返回失败
                return false;
            }

        } catch (Exception e) {
            logger.error("修复人员 {} ({}) 人脸照片失败: {}", personName, personCode, e.getMessage());
            return false;
        }
    }
    
    /**
     * 将十六进制字符串转换为字节数组（参考PersonService中的实现）
     * @param hexString 十六进制字符串
     * @return 字节数组
     */
    private byte[] convertHexStringToBytes(String hexString) {
        if (hexString == null || hexString.trim().isEmpty()) {
            return null;
        }
        
        try {
            // 去除可能的前缀 \x
            String cleanHex = hexString.trim();
            if (cleanHex.startsWith("\\x")) {
                cleanHex = cleanHex.substring(2);
            }
            
            // 确保是偶数长度
            if (cleanHex.length() % 2 != 0) {
                logger.warn("十六进制字符串长度不是偶数: {}", hexString);
                return null;
            }
            
            byte[] bytes = new byte[cleanHex.length() / 2];
            for (int i = 0; i < cleanHex.length(); i += 2) {
                String byteString = cleanHex.substring(i, i + 2);
                bytes[i / 2] = (byte) Integer.parseInt(byteString, 16);
            }
            
            return bytes;
            
        } catch (Exception e) {
            logger.error("转换十六进制字符串失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 更新人脸照片的同步标识
     * @param personCode 人员编码
     */
    private void updateFacePhotoSyncFlag(String personCode) {
        int maxRetries = 3;
        int retryCount = 0;
        
        while (retryCount < maxRetries) {
            try {
                facePhotoRepository.updateSyncFlagByPerCode(personCode, 1);
                logger.debug("更新人员 {} 的人脸照片同步标识为已同步", personCode);
                return; // 成功执行，退出重试循环
                
            } catch (Exception e) {
                retryCount++;
                
                // 检查是否为Bean创建异常
                if (e.getMessage() != null && e.getMessage().contains("entityManagerFactory")) {
                    logger.warn("检测到Bean创建异常，人员 {} 同步标识更新失败（重试 {}/{}）: {}", 
                              personCode, retryCount, maxRetries, e.getMessage());
                } else {
                    logger.warn("更新人员 {} 的人脸照片同步标识失败（重试 {}/{}）: {}", 
                              personCode, retryCount, maxRetries, e.getMessage());
                }
                
                if (retryCount < maxRetries) {
                    try {
                        // 指数退避重试
                        Thread.sleep(1000 * retryCount);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        logger.warn("重试等待被中断，停止更新人员 {} 的同步标识", personCode);
                        break;
                    }
                } else {
                    logger.error("更新人员 {} 的人脸照片同步标识最终失败，已重试 {} 次", personCode, maxRetries);
                    // 记录到失败列表，但不抛出异常，因为EGS平台更新成功比本地标识更重要
                }
            }
        }
    }

    /**
     * 获取存在人脸照片同步问题的人员列表
     * @param page 页码
     * @param size 每页大小
     * @return 分页结果
     */
    public Page<FacePhotoSyncIssueDto> getPersonsWithFacePhotoIssues(int page, int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<Object[]> rawResults = facePhotoRepository.findPersonsWithFacePhotoIssues(pageable);
            
            List<FacePhotoSyncIssueDto> dtoList = rawResults.getContent().stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
            
            return new PageImpl<>(dtoList, pageable, rawResults.getTotalElements());
            
        } catch (Exception e) {
            logger.error("获取人脸照片同步问题列表失败", e);
            throw new RuntimeException("获取问题列表失败", e);
        }
    }

    /**
     * 将数据库查询结果转换为DTO对象
     * @param row 数据库行结果
     * @return DTO对象
     */
    private FacePhotoSyncIssueDto convertToDto(Object[] row) {
        FacePhotoSyncIssueDto dto = new FacePhotoSyncIssueDto();
        dto.setPersonCode((String) row[0]);
        dto.setPersonName((String) row[1]);
        dto.setDepartmentCode((String) row[2]);
        dto.setTelephone((String) row[3]);
        dto.setIdcard((String) row[4]);
        dto.setPersonSyncFlag((Integer) row[5]);
        dto.setFacePhotoId((String) row[6]);
        dto.setFaceSyncFlag((Integer) row[7]);
        dto.setIssueType((String) row[8]);
        dto.setIssueDescription((String) row[9]);
        return dto;
    }

    /**
     * 单个人员人脸照片同步（公开方法）
     * @param personCode 人员编码
     * @return 同步结果
     */
    public Map<String, Object> syncSinglePersonFacePhoto(String personCode) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            logger.info("开始单个人员人脸照片同步: personCode={}", personCode);
            
            // 首先查询人员信息
            Optional<PersonInfo> personOpt = personRepository.findByPersonCode(personCode);
            if (!personOpt.isPresent()) {
                logger.warn("未找到人员信息: personCode={}", personCode);
                result.put("success", false);
                result.put("message", "未找到人员信息");
                return result;
            }
            
            PersonInfo personInfo = personOpt.get();
            String personName = personInfo.getPersonName();
            
            // 调用私有的修复方法
            boolean syncSuccess = fixSinglePersonFacePhoto(personCode, personName);
            
            if (syncSuccess) {
                logger.info("单个人员人脸照片同步成功: {} ({})", personName, personCode);
                result.put("success", true);
                result.put("message", "人脸照片同步成功");
            } else {
                logger.warn("单个人员人脸照片同步失败: {} ({})", personName, personCode);
                result.put("success", false);
                result.put("message", "人脸照片同步失败，请检查EGS平台连接或人员数据");
            }
            
        } catch (Exception e) {
            logger.error("单个人员人脸照片同步异常: personCode={}", personCode, e);
            result.put("success", false);
            result.put("message", "同步异常: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 获取同步检查统计信息
     * @return 统计信息
     */
    public Map<String, Object> getSyncCheckStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        try {
            Long totalIssues = facePhotoRepository.countPersonsWithFacePhotoIssues();
            stats.put("totalIssues", totalIssues);
            stats.put("success", true);
            
        } catch (Exception e) {
            logger.error("获取同步检查统计信息失败", e);
            stats.put("success", false);
            stats.put("message", "获取统计信息失败: " + e.getMessage());
        }
        
        return stats;
    }

    /**
     * 同步检查结果内部类
     */
    private static class SyncCheckResult {
        private final int totalIssues;
        private final Map<String, Integer> issueTypes;

        public SyncCheckResult(int totalIssues, Map<String, Integer> issueTypes) {
            this.totalIssues = totalIssues;
            this.issueTypes = issueTypes;
        }

        public int getTotalIssues() {
            return totalIssues;
        }

        public Map<String, Integer> getIssueTypes() {
            return issueTypes;
        }
    }

    /**
     * 修复结果内部类
     */
    private static class SyncFixResult {
        private final int successCount;
        private final int failCount;
        private final List<String> failedPersons;

        public SyncFixResult(int successCount, int failCount, List<String> failedPersons) {
            this.successCount = successCount;
            this.failCount = failCount;
            this.failedPersons = failedPersons;
        }

        public int getSuccessCount() {
            return successCount;
        }

        public int getFailCount() {
            return failCount;
        }

        public List<String> getFailedPersons() {
            return failedPersons;
        }
    }
} 
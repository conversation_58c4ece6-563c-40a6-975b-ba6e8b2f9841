package fastgatedemo.demo.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import fastgatedemo.demo.dto.DormitoryStatusDTO;
import fastgatedemo.demo.dto.StudentStatusCacheDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description 学生宿舍查询服务
 * 专门用于学生在寝情况查看页面
 * 只查询分配了宿舍的学生，根据宿舍关联表和Redis缓存查询学生在寝状态
 * <AUTHOR>
 * @date 2025-01-31
 */
@Service
public class StudentDormitoryQueryService {

    private static final Logger logger = LoggerFactory.getLogger(StudentDormitoryQueryService.class);

    @Autowired
    private StudentStatusCacheService statusCacheService;

    @PersistenceContext
    private EntityManager entityManager;

    /**
     * 分页查询已分配宿舍的学生在寝状态
     * 根据学生宿舍关联表查询学生，然后通过Redis缓存查询是否在寝室
     * 
     * @param pageable 分页参数
     * @param personName 学生姓名筛选（可选）
     * @param buildingCode 宿舍楼代码筛选（可选）
     * @param dormitoryStatus 在寝状态筛选（可选：1=在寝，2=外出，0=无记录）
     * @return 分页的学生状态列表
     */
    public Page<DormitoryStatusDTO> getAssignedStudentsStatus(Pageable pageable, String personName, 
                                                             String buildingCode, Integer dormitoryStatus) {
        long startTime = System.currentTimeMillis();
        
        try {
            logger.info("开始查询已分配宿舍学生状态: personName={}, buildingCode={}, dormitoryStatus={}", 
                       personName, buildingCode, dormitoryStatus);

            // 1. 根据条件查询已分配宿舍的学生列表
            List<String> assignedStudentCodes = getAssignedStudentCodes(personName, buildingCode);
            
            if (assignedStudentCodes.isEmpty()) {
                logger.info("没有找到符合条件的已分配宿舍学生");
                return new PageImpl<>(new ArrayList<>(), pageable, 0);
            }

            logger.info("找到{}名已分配宿舍的学生", assignedStudentCodes.size());

            // 2. 批量从Redis缓存获取学生状态
            Map<String, StudentStatusCacheDTO> studentStatusMap = statusCacheService.batchGetStudentStatus(assignedStudentCodes);

            // 3. 获取学生详细信息（包括宿舍信息）
            Map<String, Map<String, Object>> studentInfoMap = getStudentInfoWithDormitory(assignedStudentCodes);

            // 4. 组装完整的学生状态信息
            List<DormitoryStatusDTO> allStudents = assignedStudentCodes.stream()
                    .map(studentCode -> {
                        StudentStatusCacheDTO cacheStatus = studentStatusMap.get(studentCode);
                        Map<String, Object> studentInfo = studentInfoMap.get(studentCode);
                        return convertToDTO(studentCode, studentInfo, cacheStatus);
                    })
                    .filter(Objects::nonNull) // 过滤掉转换失败的记录
                    .collect(Collectors.toList());

            // 5. 应用状态筛选
            if (dormitoryStatus != null) {
                allStudents = allStudents.stream()
                        .filter(student -> {
                            Integer studentStatus = getStudentStatusCode(student);
                            return dormitoryStatus.equals(studentStatus);
                        })
                        .collect(Collectors.toList());
            }

            // 6. 按最后通行时间排序（最新的在前，无记录的在最后）
            allStudents.sort((a, b) -> {
                if (a.getLastPassTime() == null && b.getLastPassTime() == null) return 0;
                if (a.getLastPassTime() == null) return 1;
                if (b.getLastPassTime() == null) return -1;
                return b.getLastPassTime().compareTo(a.getLastPassTime());
            });

            // 7. 手动分页
            int start = (int) pageable.getOffset();
            int end = Math.min(start + pageable.getPageSize(), allStudents.size());
            
            List<DormitoryStatusDTO> pageContent = start < allStudents.size() ? 
                    allStudents.subList(start, end) : new ArrayList<>();

            long duration = System.currentTimeMillis() - startTime;
            logger.info("查询已分配宿舍学生状态完成: 总数={}, 筛选后={}, 当前页={}, 耗时={}ms",
                       assignedStudentCodes.size(), allStudents.size(), pageContent.size(), duration);

            return new PageImpl<>(pageContent, pageable, allStudents.size());

        } catch (Exception e) {
            logger.error("查询已分配宿舍学生状态失败: {}", e.getMessage(), e);
            return new PageImpl<>(new ArrayList<>(), pageable, 0);
        }
    }

    /**
     * 获取已分配宿舍学生的统计信息
     * 
     * @param buildingCode 宿舍楼代码筛选（可选）
     * @return 统计信息
     */
    public Map<String, Object> getAssignedStudentsStatistics(String buildingCode) {
        long startTime = System.currentTimeMillis();
        
        try {
            logger.info("开始获取已分配宿舍学生统计信息: buildingCode={}", buildingCode);

            // 1. 查询已分配宿舍的学生列表
            List<String> assignedStudentCodes = getAssignedStudentCodes(null, buildingCode);
            long totalPersons = assignedStudentCodes.size();

            if (totalPersons == 0) {
                logger.info("没有找到已分配宿舍的学生");
                return createEmptyStatistics(buildingCode);
            }

            // 2. 批量从Redis缓存获取学生状态并统计
            Map<String, Object> statusCounts = analyzeStudentStatusFromCache(assignedStudentCodes);

            long inDormitoryCount = (Long) statusCounts.get("inDormitoryCount");
            long outDormitoryCount = (Long) statusCounts.get("outDormitoryCount");
            long noRecordCount = (Long) statusCounts.get("noRecordCount");

            // 3. 构建统计结果
            Map<String, Object> statistics = new HashMap<>();
            statistics.put("totalPersons", totalPersons);
            statistics.put("inDormitoryPersons", inDormitoryCount);
            statistics.put("outDormitoryPersons", outDormitoryCount);
            statistics.put("personsWithoutRecords", noRecordCount);
            statistics.put("personsWithRecords", inDormitoryCount + outDormitoryCount);

            double returnRate = totalPersons > 0 ? (double) inDormitoryCount / totalPersons * 100 : 0;
            statistics.put("returnRate", Math.round(returnRate * 100.0) / 100.0);
            statistics.put("buildingCode", buildingCode != null ? buildingCode : "");
            statistics.put("queryMethod", "assigned_dormitory_students_only");
            statistics.put("baseDescription", "仅统计已分配宿舍的学生");

            long duration = System.currentTimeMillis() - startTime;
            statistics.put("queryDuration", duration);

            logger.info("已分配宿舍学生统计完成: 总数={}, 在寝={}, 外出={}, 无记录={}, 归寝率={}%, 耗时={}ms",
                       totalPersons, inDormitoryCount, outDormitoryCount, noRecordCount, returnRate, duration);

            return statistics;

        } catch (Exception e) {
            logger.error("获取已分配宿舍学生统计信息失败: {}", e.getMessage(), e);
            return createEmptyStatistics(buildingCode);
        }
    }

    /**
     * 获取宿舍楼列表
     * 
     * @return 宿舍楼列表
     */
    @DS("master")
    public List<Map<String, Object>> getDormitoryBuildings() {
        try {
            logger.info("获取宿舍楼列表");

            String sql = "SELECT DISTINCT di.building_code, di.building_name " +
                        "FROM dormitory_info di " +
                        "INNER JOIN person_dormitory_relation pdr ON di.dormitory_code = pdr.dormitory_code " +
                        "WHERE di.status = 1 AND pdr.status = 1 " +
                        "AND di.building_code IS NOT NULL AND di.building_name IS NOT NULL " +
                        "ORDER BY di.building_code";

            Query query = entityManager.createNativeQuery(sql);
            @SuppressWarnings("unchecked")
            List<Object[]> results = query.getResultList();

            List<Map<String, Object>> buildings = new ArrayList<>();
            for (Object[] row : results) {
                Map<String, Object> building = new HashMap<>();
                building.put("buildingCode", row[0]);
                building.put("buildingName", row[1]);
                buildings.add(building);
            }

            logger.info("获取到{}个宿舍楼", buildings.size());
            return buildings;

        } catch (Exception e) {
            logger.error("获取宿舍楼列表失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    // ==================== 私有方法 ====================

    /**
     * 根据条件查询已分配宿舍的学生代码列表
     * 
     * @param personName 学生姓名筛选（可选）
     * @param buildingCode 宿舍楼代码筛选（可选）
     * @return 学生代码列表
     */
    @DS("master")
    private List<String> getAssignedStudentCodes(String personName, String buildingCode) {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT DISTINCT pdr.person_code ");
            sql.append("FROM person_dormitory_relation pdr ");
            sql.append("INNER JOIN person_info pi ON pdr.person_code = pi.person_code ");
            sql.append("INNER JOIN dormitory_info di ON pdr.dormitory_code = di.dormitory_code ");
            sql.append("WHERE pdr.status = 1 AND pi.status = 1 AND di.status = 1 ");

            List<Object> parameters = new ArrayList<>();

            // 添加姓名筛选条件
            if (personName != null && !personName.trim().isEmpty()) {
                sql.append("AND pi.person_name LIKE ? ");
                parameters.add("%" + personName.trim() + "%");
            }

            // 添加楼栋筛选条件
            if (buildingCode != null && !buildingCode.trim().isEmpty()) {
                sql.append("AND di.building_code = ? ");
                parameters.add(buildingCode.trim());
            }

            sql.append("ORDER BY pdr.person_code");

            Query query = entityManager.createNativeQuery(sql.toString());
            for (int i = 0; i < parameters.size(); i++) {
                query.setParameter(i + 1, parameters.get(i));
            }

            @SuppressWarnings("unchecked")
            List<String> studentCodes = query.getResultList();

            logger.debug("查询到{}名已分配宿舍的学生: personName={}, buildingCode={}", 
                        studentCodes.size(), personName, buildingCode);

            return studentCodes;

        } catch (Exception e) {
            logger.error("查询已分配宿舍学生代码失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取学生详细信息（包括宿舍信息）
     * 
     * @param studentCodes 学生代码列表
     * @return 学生信息Map，key为学生代码，value为学生信息
     */
    @DS("master")
    private Map<String, Map<String, Object>> getStudentInfoWithDormitory(List<String> studentCodes) {
        try {
            if (studentCodes.isEmpty()) {
                return new HashMap<>();
            }

            // 构建IN条件的占位符
            String placeholders = studentCodes.stream().map(code -> "?").collect(Collectors.joining(","));

            String sql = "SELECT pi.person_code, pi.person_name, pi.gender, pi.telephone, pi.department_code, " +
                        "di.dormitory_code, di.dormitory_name, di.building_name, di.floor, di.room_number " +
                        "FROM person_info pi " +
                        "INNER JOIN person_dormitory_relation pdr ON pi.person_code = pdr.person_code " +
                        "INNER JOIN dormitory_info di ON pdr.dormitory_code = di.dormitory_code " +
                        "WHERE pi.person_code IN (" + placeholders + ") " +
                        "AND pi.status = 1 AND pdr.status = 1 AND di.status = 1";

            Query query = entityManager.createNativeQuery(sql);
            for (int i = 0; i < studentCodes.size(); i++) {
                query.setParameter(i + 1, studentCodes.get(i));
            }

            @SuppressWarnings("unchecked")
            List<Object[]> results = query.getResultList();

            Map<String, Map<String, Object>> studentInfoMap = new HashMap<>();
            for (Object[] row : results) {
                String personCode = (String) row[0];
                Map<String, Object> studentInfo = new HashMap<>();
                studentInfo.put("personCode", row[0]);
                studentInfo.put("personName", row[1]);
                studentInfo.put("gender", row[2]);
                studentInfo.put("telephone", row[3]);
                studentInfo.put("departmentCode", row[4]);
                studentInfo.put("dormitoryCode", row[5]);
                studentInfo.put("dormitoryName", row[6]);
                studentInfo.put("buildingName", row[7]);
                studentInfo.put("floor", row[8]);
                studentInfo.put("roomNumber", row[9]);
                studentInfoMap.put(personCode, studentInfo);
            }

            logger.debug("获取到{}名学生的详细信息", studentInfoMap.size());
            return studentInfoMap;

        } catch (Exception e) {
            logger.error("获取学生详细信息失败: {}", e.getMessage(), e);
            return new HashMap<>();
        }
    }

    /**
     * 分析学生在缓存中的状态分布
     * 
     * @param studentCodes 学生代码列表
     * @return 状态统计信息
     */
    private Map<String, Object> analyzeStudentStatusFromCache(List<String> studentCodes) {
        long inDormitoryCount = 0;
        long outDormitoryCount = 0;
        long noRecordCount = 0;

        // 批量获取学生状态
        Map<String, StudentStatusCacheDTO> statusMap = statusCacheService.batchGetStudentStatus(studentCodes);

        for (String studentCode : studentCodes) {
            StudentStatusCacheDTO status = statusMap.get(studentCode);

            if (status == null || status.getLastPassTime() == null) {
                // 没有缓存记录或没有通行记录
                noRecordCount++;
            } else if (status.isInDormitory()) {
                // 在寝
                inDormitoryCount++;
            } else {
                // 外出
                outDormitoryCount++;
            }
        }

        Map<String, Object> result = new HashMap<>();
        result.put("inDormitoryCount", inDormitoryCount);
        result.put("outDormitoryCount", outDormitoryCount);
        result.put("noRecordCount", noRecordCount);

        logger.debug("状态分析完成: 在寝={}, 外出={}, 无记录={}", inDormitoryCount, outDormitoryCount, noRecordCount);

        return result;
    }

    /**
     * 转换为DormitoryStatusDTO
     * 
     * @param studentCode 学生代码
     * @param studentInfo 学生信息
     * @param cacheStatus 缓存状态
     * @return DormitoryStatusDTO
     */
    private DormitoryStatusDTO convertToDTO(String studentCode, Map<String, Object> studentInfo, 
                                           StudentStatusCacheDTO cacheStatus) {
        try {
            DormitoryStatusDTO dto = new DormitoryStatusDTO();

            // 基本信息
            dto.setPersonCode(studentCode);
            dto.setPersonName((String) studentInfo.get("personName"));
            dto.setGender((Integer) studentInfo.get("gender"));
            dto.setTelephone((String) studentInfo.get("telephone"));
            dto.setDepartmentCode((String) studentInfo.get("departmentCode"));
            dto.setDepartmentName((String) studentInfo.get("departmentCode")); // 使用部门代码作为名称

            // 宿舍信息
            dto.setDormitoryCode((String) studentInfo.get("dormitoryCode"));
            dto.setDormitoryName((String) studentInfo.get("dormitoryName"));
            dto.setBuildingName((String) studentInfo.get("buildingName"));
            dto.setFloor((String) studentInfo.get("floor"));
            dto.setRoomNumber((String) studentInfo.get("roomNumber"));

            // 通行状态信息
            if (cacheStatus != null && cacheStatus.getLastPassTime() != null) {
                dto.setLastInOrOut(cacheStatus.getLastInOrOut());
                dto.setLastPassTime(cacheStatus.getLastPassTime());
                dto.setIsInDormitory(cacheStatus.isInDormitory());
                dto.setLastDeviceName(cacheStatus.getLastDeviceName());
                dto.setLastAreaName(cacheStatus.getLastAreaName());
                dto.setLastInOrOutDesc(cacheStatus.getInOrOutDescription());
                dto.setDormitoryStatusDesc(cacheStatus.getStatusDescription());

                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                dto.setLastPassTimeStr(cacheStatus.getLastPassTime().format(formatter));
            } else {
                // 无通行记录
                dto.setLastInOrOut(0);
                dto.setLastPassTime(null);
                dto.setIsInDormitory(false);
                dto.setLastDeviceName("无记录");
                dto.setLastAreaName("无记录");
                dto.setLastInOrOutDesc("无通行记录");
                dto.setDormitoryStatusDesc("无记录");
                dto.setLastPassTimeStr("无记录");
            }

            dto.setQueryDate(getCurrentDate());

            return dto;

        } catch (Exception e) {
            logger.error("转换DTO失败: studentCode={}, error={}", studentCode, e.getMessage());
            return null;
        }
    }

    /**
     * 获取学生状态代码
     * 
     * @param student 学生状态DTO
     * @return 状态代码（1=在寝，2=外出，0=无记录）
     */
    private Integer getStudentStatusCode(DormitoryStatusDTO student) {
        if (student.getLastInOrOut() == null || student.getLastInOrOut() == 0) {
            return 0; // 无记录
        } else if (student.getLastInOrOut() == 1) {
            return 1; // 在寝
        } else if (student.getLastInOrOut() == 2) {
            return 2; // 外出
        }
        return 0; // 默认无记录
    }

    /**
     * 创建空的统计信息
     * 
     * @param buildingCode 楼栋代码
     * @return 空统计信息
     */
    private Map<String, Object> createEmptyStatistics(String buildingCode) {
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalPersons", 0L);
        statistics.put("inDormitoryPersons", 0L);
        statistics.put("outDormitoryPersons", 0L);
        statistics.put("personsWithoutRecords", 0L);
        statistics.put("personsWithRecords", 0L);
        statistics.put("returnRate", 0.0);
        statistics.put("buildingCode", buildingCode != null ? buildingCode : "");
        statistics.put("queryMethod", "assigned_dormitory_students_only");
        statistics.put("baseDescription", "无已分配宿舍的学生");
        return statistics;
    }

    /**
     * 获取当前日期
     * 
     * @return 当前日期字符串
     */
    private String getCurrentDate() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
    }
}
package fastgatedemo.demo.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import fastgatedemo.demo.dto.DormitoryStatusDTO;
import fastgatedemo.demo.model.PersonInfo;
import fastgatedemo.demo.model.fastgate.AccessControlRecord;
import fastgatedemo.demo.repository.PersonRepository;
import fastgatedemo.demo.repository.fastgate.AccessControlRecordRepository;
import fastgatedemo.demo.repository.fastgate.TblPersonRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import org.springframework.jdbc.core.JdbcTemplate;

/**
 * @description 寝室归宿状态服务类
 * 基于FastGate通行记录判断人员寝室归宿状态
 * 此服务需要访问两个数据源：主数据源（人员信息）和FastGate数据源（通行记录）
 * 使用@DS注解动态切换数据源，主要操作使用FastGate数据源
 * <AUTHOR>
 * @date 2025-01-25
 */
@Service
@DS("fastgate") // 主要使用FastGate数据源进行通行记录查询
public class DormitoryStatusService {

    private static final Logger logger = LoggerFactory.getLogger(DormitoryStatusService.class);

    @Autowired
    private AccessControlRecordRepository accessControlRecordRepository;

    @Autowired
    private PersonRepository personRepository;

    @Autowired
    private TblPersonRepository tblPersonRepository;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 查询指定日期未归寝室的人员列表
     * @param date 查询日期 (格式: YYYY-MM-DD)
     * @return 未归寝室人员列表
     */
    public List<DormitoryStatusDTO> getNotReturnedPersons(String date) {
        logger.info("查询{}未归寝室人员", date);
        
        try {
            // 从FastGate数据源查询未归寝室的通行记录
            List<AccessControlRecord> notReturnedRecords = accessControlRecordRepository.findNotReturnedPersonsByDate(date);
            logger.info("从FastGate查询到{}条未归寝室记录", notReturnedRecords.size());
            
            // 转换为DTO并补充人员信息
            List<DormitoryStatusDTO> result = convertToDTO(notReturnedRecords, date);
            
            logger.info("成功查询到{}名未归寝室人员", result.size());
            return result;
            
        } catch (Exception e) {
            logger.error("查询未归寝室人员失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 分页查询指定日期未归寝室的人员列表
     * @param date 查询日期
     * @param pageable 分页参数
     * @return 分页的未归寝室人员列表
     */
    public Page<DormitoryStatusDTO> getNotReturnedPersonsWithPage(String date, Pageable pageable) {
        logger.info("分页查询{}未归寝室人员，页码: {}, 大小: {}", date, pageable.getPageNumber(), pageable.getPageSize());
        
        try {
            // 分页查询未归寝室的通行记录
            Page<AccessControlRecord> recordPage = accessControlRecordRepository.findNotReturnedPersonsByDateWithPage(date, pageable);
            
            // 转换为DTO
            List<DormitoryStatusDTO> dtoList = convertToDTO(recordPage.getContent(), date);
            
            return new PageImpl<>(dtoList, pageable, recordPage.getTotalElements());
            
        } catch (Exception e) {
            logger.error("分页查询未归寝室人员失败: {}", e.getMessage(), e);
            return new PageImpl<>(new ArrayList<>(), pageable, 0);
        }
    }

    /**
     * 查询指定日期已归寝室的人员列表
     * @param date 查询日期
     * @return 已归寝室人员列表
     */
    public List<DormitoryStatusDTO> getReturnedPersons(String date) {
        logger.info("查询{}已归寝室人员", date);
        
        try {
            List<AccessControlRecord> returnedRecords = accessControlRecordRepository.findReturnedPersonsByDate(date);
            logger.info("从FastGate查询到{}条已归寝室记录", returnedRecords.size());
            
            List<DormitoryStatusDTO> result = convertToDTO(returnedRecords, date);
            
            logger.info("成功查询到{}名已归寝室人员", result.size());
            return result;
            
        } catch (Exception e) {
            logger.error("查询已归寝室人员失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取指定日期的寝室归宿统计信息
     * 按照业务要求的统计逻辑：
     * 1. 总人数：从人员表（tbl_person）查询有效人员数量（status=1）
     * 2. 在寝人数：从通行记录表中，根据每个人的最新通行记录为"进"的人数
     * 3. 外出人数：从通行记录表中，根据每个人的最新通行记录为"出"的人数
     * 4. 归寝率：在寝人数 / 总人数 × 100%
     * @param date 查询日期（保留参数兼容性，实际按最新记录统计）
     * @return 统计信息Map
     */
    @SuppressWarnings("unchecked")
    public Map<String, Object> getDormitoryStatistics(String date) {
        logger.info("开始获取寝室归宿统计信息（分表优化查询）");
        long startTime = System.currentTimeMillis();

        Map<String, Object> statistics = new HashMap<>();

        try {
            // 优先使用分表查询优化
            Object[] statisticsResult = getShardingStatistics();

            if (statisticsResult == null || statisticsResult.length < 4) {
                logger.warn("分表查询失败，尝试使用原优化查询");
                // 回退到原优化查询
                statisticsResult = accessControlRecordRepository.getPersonTableStatisticsOptimized();
            }

            if (statisticsResult == null || statisticsResult.length < 4) {
                logger.error("分表查询和优化查询都失败，回退到原始查询方式");
                // 回退到原始查询方式
                return getDormitoryStatisticsFallback(date);
            }

            // 解析统计结果
            long totalPersons = ((Number) statisticsResult[0]).longValue();
            long returnedCount = ((Number) statisticsResult[1]).longValue();
            long notReturnedCount = ((Number) statisticsResult[2]).longValue();
            long personsWithoutRecords = ((Number) statisticsResult[3]).longValue();

            logger.info("优化查询结果 - 总人数: {}, 在寝: {}, 外出: {}, 无记录: {}",
                    totalPersons, returnedCount, notReturnedCount, personsWithoutRecords);

            // 5. 验证数据一致性
            long calculatedTotal = returnedCount + notReturnedCount + personsWithoutRecords;
            logger.info("数据验证 - 人员表总数: {}, 计算总数: {}", totalPersons, calculatedTotal);

            // 6. 计算归寝率（在寝人数 / 总人数）
            double returnRate = totalPersons > 0 ? (double) returnedCount / totalPersons * 100 : 0;

            // 7. 组装统计结果
            statistics.put("date", date); // 保留日期参数，但实际统计是最新状态
            statistics.put("totalPersons", totalPersons);
            statistics.put("returnedCount", returnedCount);
            statistics.put("notReturnedCount", notReturnedCount);
            statistics.put("personsWithRecords", returnedCount + notReturnedCount); // 有记录的人员数 = 在寝 + 外出
            statistics.put("personsWithoutRecords", personsWithoutRecords);
            statistics.put("returnRate", Math.round(returnRate * 100.0) / 100.0);
            statistics.put("queryMethod", "sharding_optimized");

            long endTime = System.currentTimeMillis();
            logger.info("分表统计完成 - 总人数: {}, 在寝: {}, 外出: {}, 无记录: {}, 归寝率: {}%, 耗时: {}ms",
                    totalPersons, returnedCount, notReturnedCount, personsWithoutRecords, returnRate, (endTime - startTime));

        } catch (Exception e) {
            logger.error("获取寝室归宿统计信息失败: {}", e.getMessage(), e);
            statistics.put("error", "统计信息获取失败: " + e.getMessage());
        }

        return statistics;
    }

    /**
     * 回退的统计查询方法（当优化查询失败时使用）
     * @param date 查询日期
     * @return 统计信息
     */
    @SuppressWarnings("unchecked")
    private Map<String, Object> getDormitoryStatisticsFallback(String date) {
        Map<String, Object> statistics = new HashMap<>();
        long startTime = System.currentTimeMillis();

        logger.warn("使用回退查询方式获取统计数据");

        try {
            // 1. 从人员表查询总人数（有效人员，status=1）
            long totalPersons = tblPersonRepository.countActivePersons();
            logger.info("回退查询 - 从人员表查询到总人数: {}", totalPersons);

            // 2. 统计人员表中在寝人数（基于最新通行记录）
            long returnedCount = accessControlRecordRepository.countPersonTableReturnedPersons();
            logger.info("回退查询 - 查询到人员表中在寝人数: {}", returnedCount);

            // 3. 统计人员表中外出人数（基于最新通行记录）
            long notReturnedCount = accessControlRecordRepository.countPersonTableNotReturnedPersons();
            logger.info("回退查询 - 查询到人员表中外出人数: {}", notReturnedCount);

            // 4. 统计人员表中无记录人数
            long personsWithoutRecords = accessControlRecordRepository.countPersonTableNoRecordPersons();
            logger.info("回退查询 - 查询到人员表中无记录人数: {}", personsWithoutRecords);

            // 计算归寝率
            double returnRate = totalPersons > 0 ? (double) returnedCount / totalPersons * 100 : 0;

            statistics.put("date", date);
            statistics.put("totalPersons", totalPersons);
            statistics.put("returnedCount", returnedCount);
            statistics.put("notReturnedCount", notReturnedCount);
            statistics.put("personsWithRecords", returnedCount + notReturnedCount);
            statistics.put("personsWithoutRecords", personsWithoutRecords);
            statistics.put("returnRate", Math.round(returnRate * 100.0) / 100.0);
            statistics.put("queryMethod", "fallback_separate");

            long endTime = System.currentTimeMillis();
            logger.info("回退统计完成 - 总人数: {}, 在寝: {}, 外出: {}, 无记录: {}, 归寝率: {}%, 耗时: {}ms",
                    totalPersons, returnedCount, notReturnedCount, personsWithoutRecords, returnRate, (endTime - startTime));

        } catch (Exception e) {
            logger.error("回退查询也失败: {}", e.getMessage(), e);
            statistics.put("error", "统计信息获取失败: " + e.getMessage());
        }

        return statistics;
    }

    /**
     * 优化的统计查询方法
     * @param date 查询日期
     * @return 统计信息
     */
    private Map<String, Object> getOptimizedStatistics(String date) {
        Map<String, Object> statistics = new HashMap<>();

        try {
            // 尝试使用优化的单次查询
            Object[] result = accessControlRecordRepository.getOptimizedStatisticsByDate(date);

            if (result != null && result.length >= 3) {
                long notReturnedCount = ((Number) result[0]).longValue();
                long returnedCount = ((Number) result[1]).longValue();
                long totalRecords = ((Number) result[2]).longValue();

                long totalPersons = notReturnedCount + returnedCount;
                double returnRate = totalPersons > 0 ? (double) returnedCount / totalPersons * 100 : 0;

                statistics.put("date", date);
                statistics.put("notReturnedCount", notReturnedCount);
                statistics.put("returnedCount", returnedCount);
                statistics.put("totalPersons", totalPersons);
                statistics.put("returnRate", Math.round(returnRate * 100.0) / 100.0);
                statistics.put("totalRecords", totalRecords);
                statistics.put("queryMethod", "optimized");

                logger.debug("使用优化查询获取统计信息成功");

            } else {
                throw new RuntimeException("优化查询返回结果格式错误");
            }

        } catch (Exception e) {
            logger.warn("优化查询失败，回退到传统查询方法: {}", e.getMessage());

            // 回退到传统查询方法
            statistics = getFallbackStatistics(date);
        }

        return statistics;
    }

    /**
     * 回退的统计查询方法
     * @param date 查询日期
     * @return 统计信息
     */
    private Map<String, Object> getFallbackStatistics(String date) {
        Map<String, Object> statistics = new HashMap<>();

        // 使用优化的原生SQL查询
        long notReturnedCount = accessControlRecordRepository.countNotReturnedPersonsByDateOptimized(date);
        long returnedCount = accessControlRecordRepository.countReturnedPersonsByDateOptimized(date);
        long totalRecords = accessControlRecordRepository.countByRecordDate(date);

        long totalPersons = notReturnedCount + returnedCount;
        double returnRate = totalPersons > 0 ? (double) returnedCount / totalPersons * 100 : 0;

        statistics.put("date", date);
        statistics.put("notReturnedCount", notReturnedCount);
        statistics.put("returnedCount", returnedCount);
        statistics.put("totalPersons", totalPersons);
        statistics.put("returnRate", Math.round(returnRate * 100.0) / 100.0);
        statistics.put("totalRecords", totalRecords);
        statistics.put("queryMethod", "fallback");

        logger.debug("使用回退查询获取统计信息成功");

        return statistics;
    }

    /**
     * 根据人员编码查询指定日期的归宿状态
     * @param personCode 人员编码
     * @param date 查询日期
     * @return 归宿状态DTO
     */
    public Optional<DormitoryStatusDTO> getPersonDormitoryStatus(String personCode, String date) {
        logger.info("查询人员{}在{}的归宿状态", personCode, date);

        try {
            List<AccessControlRecord> records = accessControlRecordRepository.findTopByPersonCodeAndRecordDateOrderByPassTimeDesc(personCode, date);

            if (records.isEmpty()) {
                logger.info("人员{}在{}无通行记录", personCode, date);
                return Optional.empty();
            }

            AccessControlRecord lastRecord = records.get(0);
            List<DormitoryStatusDTO> dtoList = convertToDTO(Arrays.asList(lastRecord), date);

            return dtoList.isEmpty() ? Optional.empty() : Optional.of(dtoList.get(0));

        } catch (Exception e) {
            logger.error("查询人员归宿状态失败: {}", e.getMessage(), e);
            return Optional.empty();
        }
    }

    /**
     * 获取所有学生的在寝状态（分页查询）
     * 基于每个人员的最新通行记录判断当前状态
     * @param pageable 分页参数
     * @param personName 人员姓名筛选（可选）
     * @param dormitoryStatus 在寝状态筛选（可选：1=在寝，2=外出，0=无记录）
     * @return 分页的学生状态列表
     */
    public Page<DormitoryStatusDTO> getAllStudentsStatus(Pageable pageable, String personName, Integer dormitoryStatus) {
        logger.info("分页查询所有学生状态，页码: {}, 大小: {}, 姓名筛选: {}, 状态筛选: {}",
                pageable.getPageNumber(), pageable.getPageSize(), personName, dormitoryStatus);

        try {
            // 合并已归寝和未归寝的记录
            List<DormitoryStatusDTO> returnedPersons = getReturnedPersons(getCurrentDate());
            List<DormitoryStatusDTO> notReturnedPersons = getNotReturnedPersons(getCurrentDate());

            // 获取所有有效人员，查找无记录的人员
            List<DormitoryStatusDTO> personsWithoutRecords = getPersonsWithoutRecords();

            // 合并所有记录
            List<DormitoryStatusDTO> allStudents = new ArrayList<>();
            allStudents.addAll(returnedPersons);
            allStudents.addAll(notReturnedPersons);
            allStudents.addAll(personsWithoutRecords);

            // 应用筛选条件
            List<DormitoryStatusDTO> filteredStudents = allStudents.stream()
                    .filter(student -> {
                        // 姓名筛选
                        if (personName != null && !personName.trim().isEmpty()) {
                            if (student.getPersonName() == null ||
                                !student.getPersonName().contains(personName.trim())) {
                                return false;
                            }
                        }

                        // 状态筛选
                        if (dormitoryStatus != null) {
                            Integer studentStatus = getStudentStatusCode(student);
                            if (!dormitoryStatus.equals(studentStatus)) {
                                return false;
                            }
                        }

                        return true;
                    })
                    .sorted((a, b) -> {
                        // 按最后通行时间排序，最新的在前，无记录的在最后
                        if (a.getLastPassTime() == null && b.getLastPassTime() == null) return 0;
                        if (a.getLastPassTime() == null) return 1;
                        if (b.getLastPassTime() == null) return -1;
                        return b.getLastPassTime().compareTo(a.getLastPassTime());
                    })
                    .collect(Collectors.toList());

            // 手动分页
            int start = (int) pageable.getOffset();
            int end = Math.min(start + pageable.getPageSize(), filteredStudents.size());

            List<DormitoryStatusDTO> pageContent = start < filteredStudents.size() ?
                    filteredStudents.subList(start, end) : new ArrayList<>();

            logger.info("查询所有学生状态成功，总数: {}, 筛选后: {}, 当前页: {}",
                    allStudents.size(), filteredStudents.size(), pageContent.size());

            return new PageImpl<>(pageContent, pageable, filteredStudents.size());

        } catch (Exception e) {
            logger.error("查询所有学生状态失败: {}", e.getMessage(), e);
            return new PageImpl<>(new ArrayList<>(), pageable, 0);
        }
    }

    /**
     * 获取无通行记录的人员列表
     * @return 无记录人员列表
     */
    private List<DormitoryStatusDTO> getPersonsWithoutRecords() {
        try {
            // 查询所有有效人员
            List<fastgatedemo.demo.model.fastgate.TblPerson> allPersons = tblPersonRepository.findActivePersons();

            // 查询有通行记录的人员编码
            List<String> personsWithRecords = accessControlRecordRepository.findDistinctPersonCodes();
            Set<String> recordedPersonCodes = new HashSet<>(personsWithRecords);

            // 筛选出无记录的人员
            List<DormitoryStatusDTO> personsWithoutRecords = allPersons.stream()
                    .filter(person -> !recordedPersonCodes.contains(person.getCode()))
                    .map(person -> {
                        DormitoryStatusDTO dto = new DormitoryStatusDTO();
                        dto.setPersonCode(person.getCode());
                        dto.setPersonName(person.getName());
                        dto.setLastInOrOut(0); // 无记录
                        dto.setLastInOrOutDesc("无通行记录");
                        dto.setIsInDormitory(false);
                        dto.setDormitoryStatusDesc("无记录");
                        dto.setLastPassTime(null);
                        dto.setLastPassTimeStr("无记录");
                        dto.setLastDeviceName("无记录");
                        dto.setLastAreaName("无记录");
                        return dto;
                    })
                    .collect(Collectors.toList());

            logger.info("查询到{}名无通行记录的人员", personsWithoutRecords.size());
            return personsWithoutRecords;

        } catch (Exception e) {
            logger.error("查询无记录人员失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取学生状态代码
     * @param student 学生状态DTO
     * @return 状态代码（1=在寝，2=外出，0=无记录）
     */
    private Integer getStudentStatusCode(DormitoryStatusDTO student) {
        if (student.getLastInOrOut() == null || student.getLastInOrOut() == 0) {
            return 0; // 无记录
        } else if (student.getLastInOrOut() == 1) {
            return 1; // 在寝
        } else if (student.getLastInOrOut() == 2) {
            return 2; // 外出
        }
        return 0; // 默认无记录
    }

    /**
     * 高性能获取所有学生的在寝状态（数据库级分页查询）
     * 使用优化的SQL查询，避免内存中的大量数据处理
     * @param pageable 分页参数
     * @param personName 人员姓名筛选（可选）
     * @param dormitoryStatus 在寝状态筛选（可选：1=在寝，2=外出，0=无记录）
     * @return 分页的学生状态列表
     */
    public Page<DormitoryStatusDTO> getAllStudentsStatusOptimized(Pageable pageable, String personName, Integer dormitoryStatus) {
        logger.info("高性能分页查询所有学生状态，页码: {}, 大小: {}, 姓名筛选: {}, 状态筛选: {}",
                pageable.getPageNumber(), pageable.getPageSize(), personName, dormitoryStatus);

        try {
            // 计算分页参数
            int offset = (int) pageable.getOffset();
            int limit = pageable.getPageSize();

            // 处理筛选参数
            String nameFilter = (personName != null && !personName.trim().isEmpty()) ? personName.trim() : null;

            // 并行执行查询和统计
            long startTime = System.currentTimeMillis();

            // 查询数据
            List<Object[]> rawResults = accessControlRecordRepository.findAllStudentsStatusOptimized(
                    nameFilter, dormitoryStatus, offset, limit);

            // 统计总数
            long totalElements = accessControlRecordRepository.countAllStudentsStatusOptimized(
                    nameFilter, dormitoryStatus);

            long queryTime = System.currentTimeMillis() - startTime;

            // 转换结果
            List<DormitoryStatusDTO> studentList = rawResults.stream()
                    .map(this::convertRawResultToDTO)
                    .collect(Collectors.toList());

            logger.info("高性能查询完成，耗时: {}ms, 总数: {}, 当前页: {}",
                    queryTime, totalElements, studentList.size());

            return new PageImpl<>(studentList, pageable, totalElements);

        } catch (Exception e) {
            logger.error("高性能查询所有学生状态失败: {}", e.getMessage(), e);
            // 降级到原有方法
            logger.warn("降级使用原有查询方法");
            return getAllStudentsStatus(pageable, personName, dormitoryStatus);
        }
    }

    /**
     * 将原始查询结果转换为DTO
     * @param rawResult 原始查询结果数组
     * @return DormitoryStatusDTO
     */
    private DormitoryStatusDTO convertRawResultToDTO(Object[] rawResult) {
        DormitoryStatusDTO dto = new DormitoryStatusDTO();

        try {
            dto.setPersonCode((String) rawResult[0]);
            dto.setPersonName((String) rawResult[1]);
            dto.setLastInOrOut(rawResult[2] != null ? ((Number) rawResult[2]).intValue() : 0);

            // 处理时间字段
            if (rawResult[3] != null) {
                if (rawResult[3] instanceof java.sql.Timestamp) {
                    dto.setLastPassTime(((java.sql.Timestamp) rawResult[3]).toLocalDateTime());
                } else if (rawResult[3] instanceof LocalDateTime) {
                    dto.setLastPassTime((LocalDateTime) rawResult[3]);
                }
                dto.setLastPassTimeStr(dto.getLastPassTime() != null ?
                        dto.getLastPassTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : "无记录");
            } else {
                dto.setLastPassTime(null);
                dto.setLastPassTimeStr("无记录");
            }

            dto.setLastDeviceName((String) rawResult[4]);
            dto.setLastAreaName((String) rawResult[5]);
            dto.setLastInOrOutDesc((String) rawResult[6]);
            dto.setIsInDormitory(rawResult[7] != null ? (Boolean) rawResult[7] : false);
            dto.setDormitoryStatusDesc((String) rawResult[8]);

            // 设置默认值
            dto.setDormitoryName(""); // 宿舍信息需要额外查询，暂时留空

        } catch (Exception e) {
            logger.error("转换查询结果失败: {}", e.getMessage(), e);
            // 返回默认DTO
            dto.setPersonCode("unknown");
            dto.setPersonName("数据异常");
            dto.setLastInOrOut(0);
            dto.setLastInOrOutDesc("数据异常");
            dto.setIsInDormitory(false);
            dto.setDormitoryStatusDesc("数据异常");
            dto.setLastPassTimeStr("数据异常");
            dto.setLastDeviceName("数据异常");
            dto.setLastAreaName("数据异常");
            dto.setDormitoryName("");
        }

        return dto;
    }

    /**
     * 获取当前日期 (格式: YYYY-MM-DD)
     * @return 当前日期字符串
     */
    public String getCurrentDate() {
        return LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
    }

    /**
     * 验证日期格式
     * @param date 日期字符串
     * @return 是否为有效日期格式
     */
    public boolean isValidDate(String date) {
        try {
            LocalDate.parse(date, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 将通行记录转换为DTO并补充人员信息
     * @param records 通行记录列表
     * @param queryDate 查询日期
     * @return DTO列表
     */
    private List<DormitoryStatusDTO> convertToDTO(List<AccessControlRecord> records, String queryDate) {
        if (records.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 提取人员编码列表
        List<String> personCodes = records.stream()
                .map(AccessControlRecord::getPersonCode)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        
        // 从Transfer数据源查询人员详细信息
        Map<String, PersonInfo> personInfoMap = new HashMap<>();
        try {
            // 切换到主数据源查询人员信息
            List<PersonInfo> personInfos = getPersonInfoFromMaster(personCodes);
            personInfoMap = personInfos.stream()
                    .collect(Collectors.toMap(PersonInfo::getPersonCode, p -> p, (p1, p2) -> p1));
            logger.debug("从Transfer数据源查询到{}条人员信息", personInfos.size());
        } catch (Exception e) {
            logger.warn("查询人员详细信息失败: {}", e.getMessage());
        }
        
        // 转换为DTO
        List<DormitoryStatusDTO> result = new ArrayList<>();
        for (AccessControlRecord record : records) {
            DormitoryStatusDTO dto = new DormitoryStatusDTO();
            
            // 基本信息
            dto.setPersonCode(record.getPersonCode());
            dto.setPersonName(record.getPersonName());
            dto.setQueryDate(queryDate);
            
            // 通行记录信息
            dto.setLastPassTime(record.getPassTime());
            dto.setLastPassTimeStr(record.getRecordTime());
            dto.setLastInOrOut(record.getInOrOut());
            dto.setIsInDormitory(record.getInOrOut() != null && record.getInOrOut() == 1);
            dto.setLastDeviceName(record.getDeviceName());
            dto.setLastAreaName(record.getAreaName());
            
            // 补充人员详细信息
            PersonInfo personInfo = personInfoMap.get(record.getPersonCode());
            if (personInfo != null) {
                dto.setDepartmentCode(personInfo.getDepartmentCode());
                dto.setDepartmentName(personInfo.getDepartmentCode()); // 使用departmentCode作为departmentName
                // 将String类型的personType转换为Integer
                try {
                    if (personInfo.getPersonType() != null) {
                        dto.setPersonType(Integer.parseInt(personInfo.getPersonType()));
                    }
                } catch (NumberFormatException e) {
                    logger.warn("人员类型转换失败: {}, personCode: {}", personInfo.getPersonType(), record.getPersonCode());
                }
                dto.setGender(personInfo.getGender());
                dto.setTelephone(personInfo.getTelephone());
            }
            
            result.add(dto);
        }
        
        return result;
    }

    /**
     * 从主数据源查询人员信息
     * 使用@DS注解切换到master数据源
     * @param personCodes 人员编码列表
     * @return 人员信息列表
     */
    @DS("master")
    private List<PersonInfo> getPersonInfoFromMaster(List<String> personCodes) {
        return personRepository.findByPersonCodeIn(personCodes);
    }

    /**
     * 基于分表的优化统计查询
     * 只查询最近30天的分表，大幅减少数据扫描量
     * @return 统计结果数组 [总人数, 在寝人数, 外出人数, 无记录人数]
     */
    private Object[] getShardingStatistics() {
        try {
            // 获取最近30天的分表名
            List<String> recentTables = getRecentTableNames(30);

            if (recentTables.isEmpty()) {
                logger.warn("没有找到可用的分表，无法执行分表查询");
                return null;
            }

            logger.info("找到{}个可用分表，开始执行分表查询", recentTables.size());

            // 构建UNION ALL查询
            String unionQuery = buildUnionQuery(recentTables);

            String finalQuery = String.format(
                "WITH latest_records AS (" +
                "    SELECT DISTINCT ON (person_code) person_code, inorout " +
                "    FROM (%s) combined " +
                "    ORDER BY person_code, pass_time DESC" +
                ") " +
                "SELECT " +
                "    COUNT(p.code)::bigint as total_persons, " +
                "    COUNT(CASE WHEN lr.inorout = 1 THEN 1 END)::bigint as returned_persons, " +
                "    COUNT(CASE WHEN lr.inorout = 2 THEN 1 END)::bigint as not_returned_persons, " +
                "    COUNT(CASE WHEN lr.inorout IS NULL THEN 1 END)::bigint as no_record_persons " +
                "FROM tbl_person p " +
                "LEFT JOIN latest_records lr ON p.code = lr.person_code " +
                "WHERE p.status = 1",
                unionQuery
            );

            return jdbcTemplate.queryForObject(finalQuery, Object[].class);

        } catch (Exception e) {
            logger.error("分表查询执行失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 获取最近N天的分表名称
     * @param days 天数
     * @return 存在的分表名称列表
     */
    private List<String> getRecentTableNames(int days) {
        try {
            // 直接从数据库查询存在的分表，按时间倒序排列
            String sql = "SELECT table_name FROM information_schema.tables " +
                        "WHERE table_name LIKE 'tbl_access_control_record_%' " +
                        "AND table_schema = 'public' " +
                        "ORDER BY table_name DESC LIMIT ?";

            List<String> allTables = jdbcTemplate.queryForList(sql, String.class, days);

            // 过滤出有数据的表
            List<String> tablesWithData = new ArrayList<>();
            for (String tableName : allTables) {
                if (tableHasData(tableName)) {
                    tablesWithData.add(tableName);
                }
            }

            logger.info("找到{}个分表，其中{}个有数据", allTables.size(), tablesWithData.size());
            return tablesWithData;

        } catch (Exception e) {
            logger.error("获取分表名称失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 检查表是否存在
     * @param tableName 表名
     * @return 是否存在
     */
    private boolean tableExists(String tableName) {
        try {
            String sql = "SELECT COUNT(*) FROM information_schema.tables " +
                        "WHERE table_name = ? AND table_schema = 'public'";
            Integer count = jdbcTemplate.queryForObject(sql, Integer.class, tableName);
            return count != null && count > 0;
        } catch (Exception e) {
            logger.debug("检查表{}是否存在时出错: {}", tableName, e.getMessage());
            return false;
        }
    }

    /**
     * 检查表是否有数据
     * @param tableName 表名
     * @return 是否有数据
     */
    private boolean tableHasData(String tableName) {
        try {
            String sql = String.format("SELECT COUNT(*) FROM %s WHERE person_code IS NOT NULL AND person_code != '-' LIMIT 1", tableName);
            Integer count = jdbcTemplate.queryForObject(sql, Integer.class);
            return count != null && count > 0;
        } catch (Exception e) {
            logger.debug("检查表{}是否有数据时出错: {}", tableName, e.getMessage());
            return false;
        }
    }

    /**
     * 构建UNION ALL查询语句
     * @param tableNames 分表名称列表
     * @return UNION ALL查询字符串
     */
    private String buildUnionQuery(List<String> tableNames) {
        return tableNames.stream()
            .map(tableName -> String.format(
                "SELECT person_code, inorout, pass_time FROM %s " +
                "WHERE person_code IS NOT NULL AND person_code != '-'",
                tableName))
            .collect(Collectors.joining(" UNION ALL "));
    }

}

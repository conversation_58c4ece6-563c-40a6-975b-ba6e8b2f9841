package fastgatedemo.demo.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.dynamic.datasource.annotation.DS;
import fastgatedemo.demo.dto.AccessRecordDTO;
import fastgatedemo.demo.dto.StudentStatusCacheDTO;
import fastgatedemo.demo.model.PersonInfo;
import fastgatedemo.demo.repository.PersonRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @description 学生状态缓存服务
 * 负责管理Redis中学生状态缓存的增删改查操作
 * <AUTHOR>
 * @date 2025-01-29
 */
@Service
public class StudentStatusCacheService {

    private static final Logger logger = LoggerFactory.getLogger(StudentStatusCacheService.class);

    private static final String STUDENT_STATUS_PREFIX = "student:status:";
    private static final String STATS_GLOBAL_KEY = "stats:global";
    private static final String STATS_ASSIGNED_DORMITORY_KEY = "stats:assigned_dormitory"; // 新增：已分配宿舍统计缓存
    private static final String STATS_GROUP_IN_DORMITORY = "stats:group:in_dormitory";
    private static final String STATS_GROUP_OUT_DORMITORY = "stats:group:out_dormitory";
    private static final String STATS_GROUP_NO_RECORD = "stats:group:no_record";
    
    private static final int CACHE_EXPIRE_HOURS = 24; // 缓存过期时间：24小时
    private static final int STATS_EXPIRE_MINUTES = 5; // 统计缓存过期时间：5分钟
    private static final int ASSIGNED_STATS_EXPIRE_MINUTES = 3; // 已分配宿舍统计缓存过期时间：3分钟

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private PersonRepository personRepository;

    /**
     * 更新学生状态缓存
     * @param accessRecord 通行记录
     * @return 更新是否成功
     */
    public boolean updateStudentStatus(AccessRecordDTO accessRecord) {
        try {
            String personCode = accessRecord.getPersonCode();
            String cacheKey = STUDENT_STATUS_PREFIX + personCode;
            
            // 获取现有缓存状态
            StudentStatusCacheDTO oldStatus = getStudentStatus(personCode);
            Integer oldInOrOut = oldStatus != null ? oldStatus.getLastInOrOut() : null;
            
            // 创建新状态
            StudentStatusCacheDTO newStatus = new StudentStatusCacheDTO();
            newStatus.setPersonCode(personCode);
            newStatus.setPersonName(accessRecord.getPersonName());
            newStatus.updateFromAccessRecord(accessRecord);
            
            // 补充人员详细信息
            enrichPersonInfo(newStatus);
            
            // 更新个人状态缓存
            redisTemplate.opsForValue().set(cacheKey, newStatus, CACHE_EXPIRE_HOURS, TimeUnit.HOURS);
            
            // 更新统计缓存
            updateStatisticsCache(personCode, oldInOrOut, accessRecord.getInOrOut());
            
            logger.debug("更新学生状态缓存成功: personCode={}, inOrOut={}", personCode, accessRecord.getInOrOut());
            return true;
            
        } catch (Exception e) {
            logger.error("更新学生状态缓存失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取学生状态
     * @param personCode 人员编码
     * @return 学生状态DTO，如果不存在返回null
     */
    public StudentStatusCacheDTO getStudentStatus(String personCode) {
        try {
            String cacheKey = STUDENT_STATUS_PREFIX + personCode;
            Object cached = redisTemplate.opsForValue().get(cacheKey);
            
            if (cached != null) {
                return (StudentStatusCacheDTO) cached;
            }
            
            return null;
            
        } catch (Exception e) {
            logger.error("获取学生状态缓存失败: personCode={}, error={}", personCode, e.getMessage());
            return null;
        }
    }

    /**
     * 批量获取学生状态
     * @param personCodes 人员编码列表
     * @return 学生状态映射
     */
    public Map<String, StudentStatusCacheDTO> batchGetStudentStatus(List<String> personCodes) {
        Map<String, StudentStatusCacheDTO> result = new HashMap<>();
        
        try {
            List<String> cacheKeys = personCodes.stream()
                    .map(code -> STUDENT_STATUS_PREFIX + code)
                    .collect(Collectors.toList());
            
            List<Object> cachedValues = redisTemplate.opsForValue().multiGet(cacheKeys);
            
            for (int i = 0; i < personCodes.size(); i++) {
                Object cachedValue = cachedValues.get(i);
                if (cachedValue != null) {
                    result.put(personCodes.get(i), (StudentStatusCacheDTO) cachedValue);
                }
            }
            
            logger.debug("批量获取学生状态成功: 请求{}个，命中{}个", personCodes.size(), result.size());
            
        } catch (Exception e) {
            logger.error("批量获取学生状态失败: {}", e.getMessage(), e);
        }
        
        return result;
    }

    /**
     * 获取全局统计信息
     * @return 统计信息映射
     */
    public Map<String, Object> getGlobalStatistics() {
        try {
            Object cached = redisTemplate.opsForValue().get(STATS_GLOBAL_KEY);
            if (cached != null) {
                @SuppressWarnings("unchecked")
                Map<String, Object> stats = (Map<String, Object>) cached;
                logger.debug("从缓存获取全局统计信息成功");
                return stats;
            }
            
            logger.debug("全局统计缓存不存在，需要重新计算");
            return null;
            
        } catch (Exception e) {
            logger.error("获取全局统计缓存失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 更新全局统计缓存
     * @param statistics 统计信息
     */
    public void updateGlobalStatistics(Map<String, Object> statistics) {
        try {
            statistics.put("lastUpdate", System.currentTimeMillis());
            redisTemplate.opsForValue().set(STATS_GLOBAL_KEY, statistics, STATS_EXPIRE_MINUTES, TimeUnit.MINUTES);
            logger.debug("更新全局统计缓存成功");
            
        } catch (Exception e) {
            logger.error("更新全局统计缓存失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 获取已分配宿舍学生统计缓存
     * @return 统计信息映射，如果缓存不存在返回null
     */
    public Map<String, Object> getAssignedDormitoryStatistics() {
        try {
            Object cached = redisTemplate.opsForValue().get(STATS_ASSIGNED_DORMITORY_KEY);
            if (cached != null) {
                @SuppressWarnings("unchecked")
                Map<String, Object> stats = (Map<String, Object>) cached;
                logger.debug("从缓存获取已分配宿舍统计信息成功");
                return stats;
            }
            
            logger.debug("已分配宿舍统计缓存不存在，需要重新计算");
            return null;
            
        } catch (Exception e) {
            logger.error("获取已分配宿舍统计缓存失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 更新已分配宿舍学生统计缓存
     * @param statistics 统计信息
     */
    public void updateAssignedDormitoryStatistics(Map<String, Object> statistics) {
        try {
            statistics.put("lastUpdate", System.currentTimeMillis());
            redisTemplate.opsForValue().set(STATS_ASSIGNED_DORMITORY_KEY, statistics, ASSIGNED_STATS_EXPIRE_MINUTES, TimeUnit.MINUTES);
            logger.debug("更新已分配宿舍统计缓存成功");
            
        } catch (Exception e) {
            logger.error("更新已分配宿舍统计缓存失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 获取指定状态的学生列表
     * @param status 状态 (1=在寝, 2=外出, 0=无记录)
     * @param limit 限制数量
     * @return 学生编码列表
     */
    public List<String> getStudentsByStatus(Integer status, int limit) {
        try {
            String groupKey;
            if (status == null || status == 0) {
                groupKey = STATS_GROUP_NO_RECORD;
            } else if (status == 1) {
                groupKey = STATS_GROUP_IN_DORMITORY;
            } else {
                groupKey = STATS_GROUP_OUT_DORMITORY;
            }
            
            Set<Object> members = redisTemplate.opsForZSet().reverseRange(groupKey, 0, limit - 1);
            if (members != null) {
                return members.stream()
                        .map(Object::toString)
                        .collect(Collectors.toList());
            }
            
        } catch (Exception e) {
            logger.error("获取指定状态学生列表失败: status={}, error={}", status, e.getMessage());
        }
        
        return new ArrayList<>();
    }

    /**
     * 更新统计缓存
     * @param personCode 人员编码
     * @param oldStatus 旧状态
     * @param newStatus 新状态
     */
    private void updateStatisticsCache(String personCode, Integer oldStatus, Integer newStatus) {
        try {
            long currentTime = System.currentTimeMillis();
            
            // 从旧分组中移除
            if (oldStatus != null) {
                String oldGroupKey = getGroupKey(oldStatus);
                redisTemplate.opsForZSet().remove(oldGroupKey, personCode);
            }
            
            // 添加到新分组
            if (newStatus != null) {
                String newGroupKey = getGroupKey(newStatus);
                redisTemplate.opsForZSet().add(newGroupKey, personCode, currentTime);
                redisTemplate.expire(newGroupKey, STATS_EXPIRE_MINUTES, TimeUnit.MINUTES);
            }
            
        } catch (Exception e) {
            logger.error("更新统计缓存失败: personCode={}, error={}", personCode, e.getMessage());
        }
    }

    /**
     * 获取分组Key
     * @param status 状态
     * @return 分组Key
     */
    private String getGroupKey(Integer status) {
        if (status == null || status == 0) {
            return STATS_GROUP_NO_RECORD;
        } else if (status == 1) {
            return STATS_GROUP_IN_DORMITORY;
        } else {
            return STATS_GROUP_OUT_DORMITORY;
        }
    }

    /**
     * 补充人员详细信息
     * @param studentStatus 学生状态DTO
     */
    @DS("master")
    private void enrichPersonInfo(StudentStatusCacheDTO studentStatus) {
        try {
            Optional<PersonInfo> personInfoOpt = personRepository.findByPersonCode(studentStatus.getPersonCode());
            if (personInfoOpt.isPresent()) {
                PersonInfo personInfo = personInfoOpt.get();
                studentStatus.setDepartmentCode(personInfo.getDepartmentCode());
                studentStatus.setDepartmentName(personInfo.getDepartmentCode()); // 使用departmentCode作为name
                studentStatus.setGender(personInfo.getGender());
                studentStatus.setTelephone(personInfo.getTelephone());
            }
        } catch (Exception e) {
            logger.warn("补充人员信息失败: personCode={}, error={}", studentStatus.getPersonCode(), e.getMessage());
        }
    }

    /**
     * 清除学生状态缓存
     * @param personCode 人员编码
     */
    public void removeStudentStatus(String personCode) {
        try {
            String cacheKey = STUDENT_STATUS_PREFIX + personCode;
            redisTemplate.delete(cacheKey);
            logger.debug("清除学生状态缓存成功: personCode={}", personCode);
            
        } catch (Exception e) {
            logger.error("清除学生状态缓存失败: personCode={}, error={}", personCode, e.getMessage());
        }
    }

    /**
     * 清除所有缓存
     */
    public void clearAllCache() {
        try {
            // 清除学生状态缓存
            Set<String> keys = redisTemplate.keys(STUDENT_STATUS_PREFIX + "*");
            if (keys != null && !keys.isEmpty()) {
                redisTemplate.delete(keys);
            }
            
            // 清除统计缓存
            redisTemplate.delete(STATS_GLOBAL_KEY);
            redisTemplate.delete(STATS_GROUP_IN_DORMITORY);
            redisTemplate.delete(STATS_GROUP_OUT_DORMITORY);
            redisTemplate.delete(STATS_GROUP_NO_RECORD);
            
            logger.info("清除所有缓存成功");
            
        } catch (Exception e) {
            logger.error("清除所有缓存失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 获取缓存统计信息
     * @return 缓存统计信息
     */
    public Map<String, Object> getCacheStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        try {
            // 统计学生状态缓存数量
            Set<String> studentKeys = redisTemplate.keys(STUDENT_STATUS_PREFIX + "*");
            stats.put("studentStatusCount", studentKeys != null ? studentKeys.size() : 0);
            
            // 统计各状态分组数量
            Long inDormitoryCount = redisTemplate.opsForZSet().count(STATS_GROUP_IN_DORMITORY, 0, System.currentTimeMillis());
            Long outDormitoryCount = redisTemplate.opsForZSet().count(STATS_GROUP_OUT_DORMITORY, 0, System.currentTimeMillis());
            Long noRecordCount = redisTemplate.opsForZSet().count(STATS_GROUP_NO_RECORD, 0, System.currentTimeMillis());
            
            stats.put("inDormitoryCount", inDormitoryCount);
            stats.put("outDormitoryCount", outDormitoryCount);
            stats.put("noRecordCount", noRecordCount);
            
            // 检查全局统计缓存是否存在
            Boolean globalStatsExists = redisTemplate.hasKey(STATS_GLOBAL_KEY);
            stats.put("globalStatsExists", globalStatsExists);
            
            logger.debug("获取缓存统计信息成功");
            
        } catch (Exception e) {
            logger.error("获取缓存统计信息失败: {}", e.getMessage(), e);
        }
        
        return stats;
    }

    /**
     * 获取单个学生的缓存信息（用于测试页面）
     * @param personCode 人员编码
     * @return 学生缓存信息
     */
    public Map<String, Object> getStudentCacheInfo(String personCode) {
        Map<String, Object> info = new HashMap<>();

        try {
            String cacheKey = STUDENT_STATUS_PREFIX + personCode;

            // 检查缓存是否存在
            Boolean exists = redisTemplate.hasKey(cacheKey);
            info.put("exists", exists);
            info.put("personCode", personCode);
            info.put("cacheKey", cacheKey);

            if (exists) {
                // 获取缓存内容
                Object cached = redisTemplate.opsForValue().get(cacheKey);
                if (cached instanceof StudentStatusCacheDTO) {
                    StudentStatusCacheDTO status = (StudentStatusCacheDTO) cached;
                    info.put("personName", status.getPersonName());
                    info.put("lastInOrOut", status.getLastInOrOut());
                    info.put("lastInOrOutDesc", status.getLastInOrOutDesc());
                    info.put("lastPassTime", status.getLastPassTime());
                    info.put("lastDeviceName", status.getLastDeviceName());
                    info.put("departmentCode", status.getDepartmentCode());
                    info.put("gender", status.getGender());
                    info.put("telephone", status.getTelephone());
                }

                // 获取TTL
                Long ttl = redisTemplate.getExpire(cacheKey, TimeUnit.SECONDS);
                info.put("ttlSeconds", ttl);
                info.put("ttlHours", ttl != null && ttl > 0 ? ttl / 3600.0 : null);
            }

            logger.debug("获取学生缓存信息成功: personCode={}, exists={}", personCode, exists);

        } catch (Exception e) {
            logger.error("获取学生缓存信息失败: personCode={}, error={}", personCode, e.getMessage());
            info.put("error", e.getMessage());
        }

        return info;
    }

    /**
     * 获取所有学生的缓存信息（用于测试页面）
     * @return 所有学生缓存信息
     */
    public Map<String, Object> getAllStudentCacheInfo() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 获取所有学生状态缓存的key
            Set<String> keys = redisTemplate.keys(STUDENT_STATUS_PREFIX + "*");

            if (keys == null || keys.isEmpty()) {
                result.put("totalCount", 0);
                result.put("students", new ArrayList<>());
                return result;
            }

            List<Map<String, Object>> students = new ArrayList<>();

            for (String key : keys) {
                String personCode = key.substring(STUDENT_STATUS_PREFIX.length());
                Map<String, Object> studentInfo = new HashMap<>();

                try {
                    Object cached = redisTemplate.opsForValue().get(key);
                    if (cached instanceof StudentStatusCacheDTO) {
                        StudentStatusCacheDTO status = (StudentStatusCacheDTO) cached;
                        studentInfo.put("personCode", personCode);
                        studentInfo.put("personName", status.getPersonName());
                        studentInfo.put("lastInOrOut", status.getLastInOrOut());
                        studentInfo.put("lastInOrOutDesc", status.getLastInOrOutDesc());
                        studentInfo.put("lastPassTime", status.getLastPassTime());
                        studentInfo.put("lastDeviceName", status.getLastDeviceName());

                        // 获取TTL
                        Long ttl = redisTemplate.getExpire(key, TimeUnit.SECONDS);
                        studentInfo.put("ttlSeconds", ttl);
                        studentInfo.put("ttlHours", ttl != null && ttl > 0 ? ttl / 3600.0 : null);

                        students.add(studentInfo);
                    }
                } catch (Exception e) {
                    logger.warn("获取学生缓存详情失败: key={}, error={}", key, e.getMessage());
                }
            }

            // 按人员编码排序
            students.sort((a, b) -> {
                String codeA = (String) a.get("personCode");
                String codeB = (String) b.get("personCode");
                return codeA.compareTo(codeB);
            });

            result.put("totalCount", students.size());
            result.put("students", students);

            // 添加统计信息
            Map<String, Object> cacheStats = getCacheStatistics();
            result.put("statistics", cacheStats);

            logger.info("获取所有学生缓存信息成功: 共{}条记录", students.size());

        } catch (Exception e) {
            logger.error("获取所有学生缓存信息失败: {}", e.getMessage(), e);
            result.put("error", e.getMessage());
        }

        return result;
    }

    /**
     * 清除单个学生的缓存（用于测试页面）
     * @param personCode 人员编码
     * @return 是否清除成功
     */
    public boolean clearStudentCache(String personCode) {
        try {
            String cacheKey = STUDENT_STATUS_PREFIX + personCode;
            Boolean deleted = redisTemplate.delete(cacheKey);

            if (deleted) {
                // 同时从统计分组中移除
                redisTemplate.opsForZSet().remove(STATS_GROUP_IN_DORMITORY, personCode);
                redisTemplate.opsForZSet().remove(STATS_GROUP_OUT_DORMITORY, personCode);
                redisTemplate.opsForZSet().remove(STATS_GROUP_NO_RECORD, personCode);

                logger.info("清除学生缓存成功: personCode={}", personCode);
                return true;
            } else {
                logger.warn("学生缓存不存在或清除失败: personCode={}", personCode);
                return false;
            }

        } catch (Exception e) {
            logger.error("清除学生缓存失败: personCode={}, error={}", personCode, e.getMessage());
            return false;
        }
    }

    /**
     * 清除所有学生缓存（用于测试页面）
     * @return 清除的记录数量
     */
    public int clearAllStudentCache() {
        try {
            // 获取所有学生状态缓存的key
            Set<String> keys = redisTemplate.keys(STUDENT_STATUS_PREFIX + "*");

            if (keys == null || keys.isEmpty()) {
                logger.info("没有学生缓存需要清除");
                return 0;
            }

            // 删除所有学生状态缓存
            Long deletedCount = redisTemplate.delete(keys);

            // 清除统计分组
            redisTemplate.delete(STATS_GROUP_IN_DORMITORY);
            redisTemplate.delete(STATS_GROUP_OUT_DORMITORY);
            redisTemplate.delete(STATS_GROUP_NO_RECORD);

            // 清除统计缓存
            redisTemplate.delete(STATS_GLOBAL_KEY);
            redisTemplate.delete(STATS_ASSIGNED_DORMITORY_KEY);

            logger.info("清除所有学生缓存成功: 共清除{}条记录", deletedCount);
            return deletedCount != null ? deletedCount.intValue() : 0;

        } catch (Exception e) {
            logger.error("清除所有学生缓存失败: {}", e.getMessage(), e);
            return 0;
        }
    }
}
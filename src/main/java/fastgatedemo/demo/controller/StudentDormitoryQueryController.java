package fastgatedemo.demo.controller;

import fastgatedemo.demo.dto.DormitoryStatusDTO;
import fastgatedemo.demo.service.StudentDormitoryQueryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @description 学生宿舍查询控制器
 * 专门用于学生在寝情况查看页面
 * 只查询分配了宿舍的学生，根据宿舍关联表和Redis缓存查询学生在寝状态
 * <AUTHOR>
 * @date 2025-01-31
 */
@RestController
@RequestMapping("/api/student-dormitory")
public class StudentDormitoryQueryController {

    private static final Logger logger = LoggerFactory.getLogger(StudentDormitoryQueryController.class);

    @Autowired
    private StudentDormitoryQueryService studentDormitoryQueryService;

    /**
     * 分页查询已分配宿舍的学生在寝状态
     * 根据学生宿舍关联表查询学生，然后通过Redis缓存查询是否在寝室
     * GET /api/student-dormitory/assigned-students?page=0&size=20&personName=张三&buildingCode=1&dormitoryStatus=1
     * 
     * @param page 页码（从0开始）
     * @param size 每页大小
     * @param personName 学生姓名筛选（可选）
     * @param buildingCode 宿舍楼代码筛选（可选）
     * @param dormitoryStatus 在寝状态筛选（可选：1=在寝，2=外出，0=无记录）
     */
    @GetMapping("/assigned-students")
    public ResponseEntity<Map<String, Object>> getAssignedStudentsStatus(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String personName,
            @RequestParam(required = false) String buildingCode,
            @RequestParam(required = false) List<String> buildingCodes,
            @RequestParam(required = false) Integer dormitoryStatus) {

        logger.info("查询已分配宿舍学生状态，页码: {}, 大小: {}, 姓名: {}, 楼栋: {}, 楼栋列表: {}, 状态: {}",
                page, size, personName, buildingCode, buildingCodes, dormitoryStatus);

        Map<String, Object> response = new HashMap<>();

        try {
            // 验证分页参数
            if (page < 0) {
                response.put("success", false);
                response.put("message", "页码不能小于0");
                return ResponseEntity.badRequest().body(response);
            }

            if (size <= 0 || size > 100) {
                response.put("success", false);
                response.put("message", "每页大小必须在1-100之间");
                return ResponseEntity.badRequest().body(response);
            }

            // 验证状态筛选参数
            if (dormitoryStatus != null && (dormitoryStatus < 0 || dormitoryStatus > 2)) {
                response.put("success", false);
                response.put("message", "状态筛选参数错误，有效值：0=无记录，1=在寝，2=外出");
                return ResponseEntity.badRequest().body(response);
            }

            Pageable pageable = PageRequest.of(page, size);

            // 处理楼栋参数：优先使用buildingCodes，如果为空则使用buildingCode
            List<String> finalBuildingCodes = null;
            if (buildingCodes != null && !buildingCodes.isEmpty()) {
                finalBuildingCodes = buildingCodes;
            } else if (buildingCode != null && !buildingCode.trim().isEmpty()) {
                finalBuildingCodes = Arrays.asList(buildingCode);
            }

            Page<DormitoryStatusDTO> pageResult = studentDormitoryQueryService.getAssignedStudentsStatus(
                    pageable, personName, finalBuildingCodes, dormitoryStatus);

            response.put("success", true);
            response.put("message", "查询成功");
            response.put("data", pageResult.getContent());
            response.put("totalElements", pageResult.getTotalElements());
            response.put("totalPages", pageResult.getTotalPages());
            response.put("currentPage", pageResult.getNumber());
            response.put("pageSize", pageResult.getSize());
            response.put("hasNext", pageResult.hasNext());
            response.put("hasPrevious", pageResult.hasPrevious());
            response.put("queryMethod", "assigned_dormitory_students_only");
            response.put("baseDescription", "仅查询已分配宿舍的学生");

            // 构建筛选条件信息
            Map<String, Object> filtersMap = new HashMap<>();
            filtersMap.put("personName", personName != null ? personName : "");
            filtersMap.put("buildingCode", buildingCode != null ? buildingCode : "");
            filtersMap.put("dormitoryStatus", dormitoryStatus != null ? dormitoryStatus : "");
            response.put("filters", filtersMap);

            logger.info("查询已分配宿舍学生状态成功，总数: {}, 当前页: {}",
                    pageResult.getTotalElements(), pageResult.getContent().size());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("查询已分配宿舍学生状态失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 查询不在寝室的学生（已分配宿舍但未归寝的学生）
     * GET /api/student-dormitory/not-in-dormitory?page=0&size=20&personName=张三&buildingCode=1
     * 
     * @param page 页码（从0开始）
     * @param size 每页大小
     * @param personName 学生姓名筛选（可选）
     * @param buildingCode 宿舍楼代码筛选（可选）
     */
    @GetMapping("/not-in-dormitory")
    public ResponseEntity<Map<String, Object>> getNotInDormitoryStudents(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String personName,
            @RequestParam(required = false) String buildingCode) {

        logger.info("查询不在寝室的学生，页码: {}, 大小: {}, 姓名: {}, 楼栋: {}",
                page, size, personName, buildingCode);

        Map<String, Object> response = new HashMap<>();

        try {
            // 验证分页参数
            if (page < 0 || size <= 0 || size > 100) {
                response.put("success", false);
                response.put("message", "分页参数错误");
                return ResponseEntity.badRequest().body(response);
            }

            Pageable pageable = PageRequest.of(page, size);
            
            // 调用查询服务，只返回不在寝室的学生（dormitoryStatus=2）
            Page<DormitoryStatusDTO> pageResult = studentDormitoryQueryService.getAssignedStudentsStatus(
                    pageable, personName, buildingCode, 2); // 2=外出

            response.put("success", true);
            response.put("message", "查询成功");
            response.put("data", pageResult.getContent());
            response.put("totalElements", pageResult.getTotalElements());
            response.put("totalPages", pageResult.getTotalPages());
            response.put("currentPage", pageResult.getNumber());
            response.put("pageSize", pageResult.getSize());
            response.put("hasNext", pageResult.hasNext());
            response.put("hasPrevious", pageResult.hasPrevious());
            response.put("queryMethod", "not_in_dormitory_only");
            response.put("baseDescription", "仅查询已分配宿舍但不在寝室的学生");

            // 构建筛选条件信息
            Map<String, Object> filtersMap = new HashMap<>();
            filtersMap.put("personName", personName != null ? personName : "");
            filtersMap.put("buildingCode", buildingCode != null ? buildingCode : "");
            filtersMap.put("dormitoryStatus", 2); // 固定为外出状态
            response.put("filters", filtersMap);

            logger.info("查询不在寝室学生成功，总数: {}, 当前页: {}",
                    pageResult.getTotalElements(), pageResult.getContent().size());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("查询不在寝室学生失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 获取宿舍楼列表（用于筛选）
     * GET /api/student-dormitory/buildings
     */
    @GetMapping("/buildings")
    public ResponseEntity<Map<String, Object>> getDormitoryBuildings() {
        
        logger.info("获取宿舍楼列表");
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            List<Map<String, Object>> buildings = studentDormitoryQueryService.getDormitoryBuildings();
            
            response.put("success", true);
            response.put("message", "查询成功");
            response.put("data", buildings);
            response.put("count", buildings.size());
            
            logger.info("获取宿舍楼列表成功，共{}个楼栋", buildings.size());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取宿舍楼列表失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 获取已分配宿舍学生的统计信息
     * GET /api/student-dormitory/statistics?buildingCode=1
     * 
     * @param buildingCode 宿舍楼代码筛选（可选）
     */
    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getAssignedStudentsStatistics(
            @RequestParam(required = false) String buildingCode) {
        
        logger.info("获取已分配宿舍学生统计信息，楼栋: {}", buildingCode);
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            Map<String, Object> statistics = studentDormitoryQueryService.getAssignedStudentsStatistics(buildingCode);
            
            response.put("success", true);
            response.put("message", "查询成功");
            response.put("data", statistics);
            
            logger.info("获取已分配宿舍学生统计信息成功");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取已分配宿舍学生统计信息失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 健康检查接口
     * GET /api/student-dormitory/health
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        Map<String, Object> response = new HashMap<>();

        try {
            Map<String, Object> statistics = studentDormitoryQueryService.getAssignedStudentsStatistics(null);

            response.put("success", true);
            response.put("message", "学生宿舍查询服务运行正常");
            response.put("statistics", statistics);
            response.put("timestamp", System.currentTimeMillis());
            response.put("serviceVersion", "assigned_dormitory_students_only");

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("健康检查失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "服务异常: " + e.getMessage());
            response.put("timestamp", System.currentTimeMillis());
            return ResponseEntity.status(500).body(response);
        }
    }
}
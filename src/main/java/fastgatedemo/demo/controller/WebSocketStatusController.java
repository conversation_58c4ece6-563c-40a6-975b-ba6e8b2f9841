package fastgatedemo.demo.controller;

import fastgatedemo.demo.service.DashboardWebSocketService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * @description WebSocket状态控制器
 * 用于检查WebSocket连接状态和推送测试
 * <AUTHOR>
 * @date 2025-01-31
 */
@RestController
@RequestMapping("/api/websocket")
@CrossOrigin(origins = "*")
public class WebSocketStatusController {

    private static final Logger logger = LoggerFactory.getLogger(WebSocketStatusController.class);

    @Autowired
    private DashboardWebSocketService webSocketService;

    /**
     * 获取WebSocket连接状态
     * GET /api/websocket/status
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getWebSocketStatus() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            int connectionCount = webSocketService.getConnectionCount();
            
            response.put("success", true);
            response.put("message", "获取WebSocket状态成功");
            response.put("connectionCount", connectionCount);
            response.put("status", connectionCount > 0 ? "有连接" : "无连接");
            response.put("timestamp", System.currentTimeMillis());
            
            logger.info("WebSocket状态查询: 连接数={}", connectionCount);
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取WebSocket状态失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "获取状态失败: " + e.getMessage());
            response.put("connectionCount", 0);
            response.put("status", "错误");
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * WebSocket健康检查
     * GET /api/websocket/health
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        Map<String, Object> response = new HashMap<>();

        try {
            response.put("success", true);
            response.put("message", "WebSocket服务正常");
            response.put("service", "DashboardWebSocketService");
            response.put("timestamp", System.currentTimeMillis());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("WebSocket健康检查失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "WebSocket服务异常: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 推送测试记录
     * POST /api/websocket/test/push
     */
    @PostMapping("/test/push")
    public ResponseEntity<Map<String, Object>> pushTestRecord() {
        Map<String, Object> response = new HashMap<>();

        try {
            webSocketService.pushTestRecord();

            response.put("success", true);
            response.put("message", "测试接口调用成功，但暂无测试数据推送");
            response.put("note", "等待真实数据接入");
            response.put("timestamp", System.currentTimeMillis());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("推送测试记录失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "推送失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 批量推送测试记录
     * POST /api/websocket/test/push/batch?count=5
     */
    @PostMapping("/test/push/batch")
    public ResponseEntity<Map<String, Object>> pushBatchTestRecords(@RequestParam(defaultValue = "5") int count) {
        Map<String, Object> response = new HashMap<>();

        try {
            if (count <= 0 || count > 20) {
                response.put("success", false);
                response.put("message", "记录数量必须在1-20之间");
                return ResponseEntity.badRequest().body(response);
            }

            webSocketService.pushRecentRecords(count);

            response.put("success", true);
            response.put("message", "批量测试接口调用成功，但暂无测试数据推送");
            response.put("note", "等待真实数据接入");
            response.put("count", count);
            response.put("timestamp", System.currentTimeMillis());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("批量推送测试记录失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "批量推送失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }
}

package fastgatedemo.demo.controller;

import fastgatedemo.demo.dto.DormitoryStatusDTO;
import fastgatedemo.demo.service.CachedDormitoryStatsService;
import fastgatedemo.demo.service.DormitoryStatusService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * @description 优化的寝室归宿状态控制器
 * 基于已分配宿舍的学生和Redis缓存提供高性能查询接口
 * 参考大屏算法：学生从宿舍表和宿舍关联表中获取基数（已分配宿舍的学生），然后通过Redis缓存查询是否归寝状态
 * <AUTHOR>  
 * @date 2025-01-31
 */
@RestController
@RequestMapping("/api/optimized-dormitory-status")
public class OptimizedDormitoryStatusController {

    private static final Logger logger = LoggerFactory.getLogger(OptimizedDormitoryStatusController.class);

    @Autowired
    private CachedDormitoryStatsService cachedDormitoryStatsService;

    @Autowired
    private DormitoryStatusService fallbackService;

    /**
     * 获取基于已分配宿舍学生的统计信息（优化版本）
     * 参考大屏算法：学生从宿舍表和宿舍关联表中获取基数（已分配宿舍的学生），然后通过Redis缓存查询是否归寝状态
     * GET /api/optimized-dormitory-status/statistics
     */
    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getOptimizedStatistics() {
        
        logger.info("获取基于已分配宿舍学生的优化统计信息");
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            Map<String, Object> statistics = cachedDormitoryStatsService.getDormitoryStatistics();

            // 转换为前端大屏需要的格式
            Map<String, Object> dashboardData = new HashMap<>();
            dashboardData.put("totalPersons", statistics.getOrDefault("totalPersons", 0));
            dashboardData.put("inDormitoryPersons", statistics.getOrDefault("returnedCount", 0));
            dashboardData.put("outDormitoryPersons", statistics.getOrDefault("notReturnedCount", 0));
            dashboardData.put("returnRate", statistics.getOrDefault("returnRate", 0.0));
            dashboardData.put("queryMethod", statistics.getOrDefault("queryMethod", "optimized_assigned_dormitory_redis"));
            dashboardData.put("baseDescription", statistics.getOrDefault("baseDescription", "基于已分配宿舍学生和Redis缓存统计"));
            dashboardData.put("personsWithoutRecords", statistics.getOrDefault("personsWithoutRecords", 0));
            
            response.put("success", true);
            response.put("message", "查询成功");
            response.put("data", dashboardData);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取优化统计信息失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "统计信息获取失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 分页查询所有学生的在寝状态（优化版本）
     * 基于已分配宿舍的学生和Redis缓存查询
     * GET /api/optimized-dormitory-status/students?page=0&size=20&personName=张三&dormitoryStatus=1
     * @param page 页码（从0开始）
     * @param size 每页大小
     * @param personName 人员姓名筛选（可选）
     * @param dormitoryStatus 在寝状态筛选（可选：1=在寝，2=外出，0=无记录）
     */
    @GetMapping("/students")
    public ResponseEntity<Map<String, Object>> getOptimizedStudentsStatus(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String personName,
            @RequestParam(required = false) Integer dormitoryStatus) {

        logger.info("优化版分页查询学生在寝状态（基于已分配宿舍+Redis缓存），页码: {}, 大小: {}, 姓名筛选: {}, 状态筛选: {}",
                page, size, personName, dormitoryStatus);

        Map<String, Object> response = new HashMap<>();

        try {
            // 验证分页参数
            if (page < 0) {
                response.put("success", false);
                response.put("message", "页码不能小于0");
                return ResponseEntity.badRequest().body(response);
            }

            if (size <= 0 || size > 100) {
                response.put("success", false);
                response.put("message", "每页大小必须在1-100之间");
                return ResponseEntity.badRequest().body(response);
            }

            // 验证状态筛选参数
            if (dormitoryStatus != null && (dormitoryStatus < 0 || dormitoryStatus > 2)) {
                response.put("success", false);
                response.put("message", "状态筛选参数错误，有效值：0=无记录，1=在寝，2=外出");
                return ResponseEntity.badRequest().body(response);
            }

            Pageable pageable = PageRequest.of(page, size);
            Page<DormitoryStatusDTO> pageResult = cachedDormitoryStatsService.getAllStudentsStatus(
                    pageable, personName, dormitoryStatus);

            response.put("success", true);
            response.put("message", "查询成功");
            response.put("data", pageResult.getContent());
            response.put("totalElements", pageResult.getTotalElements());
            response.put("totalPages", pageResult.getTotalPages());
            response.put("currentPage", pageResult.getNumber());
            response.put("pageSize", pageResult.getSize());
            response.put("hasNext", pageResult.hasNext());
            response.put("hasPrevious", pageResult.hasPrevious());
            response.put("queryMethod", "optimized_assigned_dormitory_redis");
            
            // 构建筛选条件信息（Java 8兼容）
            Map<String, Object> filtersMap = new HashMap<>();
            filtersMap.put("personName", personName != null ? personName : "");
            filtersMap.put("dormitoryStatus", dormitoryStatus != null ? dormitoryStatus : "");
            response.put("filters", filtersMap);

            logger.info("优化版查询学生状态成功，总数: {}, 当前页: {}",
                    pageResult.getTotalElements(), pageResult.getContent().size());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("优化版查询学生状态失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 查询指定状态的学生列表（优化版本）
     * GET /api/optimized-dormitory-status/students-by-status?status=1&page=0&size=20&personName=张三
     * @param status 状态 (1=在寝, 2=外出, 0=无记录)
     * @param page 页码
     * @param size 每页大小
     * @param personName 姓名筛选
     */
    @GetMapping("/students-by-status")
    public ResponseEntity<Map<String, Object>> getStudentsByStatus(
            @RequestParam Integer status,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String personName) {

        logger.info("基于缓存查询指定状态学生列表: status={}, page={}, size={}, name={}", 
                   status, page, size, personName);

        Map<String, Object> response = new HashMap<>();

        try {
            // 验证状态参数
            if (status == null || status < 0 || status > 2) {
                response.put("success", false);
                response.put("message", "状态参数错误，有效值：0=无记录，1=在寝，2=外出");
                return ResponseEntity.badRequest().body(response);
            }

            // 验证分页参数
            if (page < 0 || size <= 0 || size > 100) {
                response.put("success", false);
                response.put("message", "分页参数错误");
                return ResponseEntity.badRequest().body(response);
            }

            Pageable pageable = PageRequest.of(page, size);
            Page<DormitoryStatusDTO> pageResult = cachedDormitoryStatsService.getStudentsByStatus(
                    status, pageable, personName);

            response.put("success", true);
            response.put("message", "查询成功");
            response.put("data", pageResult.getContent());
            response.put("totalElements", pageResult.getTotalElements());
            response.put("totalPages", pageResult.getTotalPages());
            response.put("currentPage", pageResult.getNumber());
            response.put("pageSize", pageResult.getSize());
            response.put("hasNext", pageResult.hasNext());
            response.put("hasPrevious", pageResult.hasPrevious());
            response.put("queryMethod", "cache_based_by_status");
            response.put("requestedStatus", status);
            response.put("statusDescription", getStatusDescription(status));

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("基于缓存查询指定状态学生失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 获取单个学生状态（优化版本）
     * GET /api/optimized-dormitory-status/student/{personCode}
     */
    @GetMapping("/student/{personCode}")
    public ResponseEntity<Map<String, Object>> getStudentStatus(@PathVariable String personCode) {
        
        logger.info("查询单个学生状态: personCode={}", personCode);
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            Optional<DormitoryStatusDTO> statusOpt = cachedDormitoryStatsService.getStudentStatus(personCode);
            
            if (statusOpt.isPresent()) {
                response.put("success", true);
                response.put("message", "查询成功");
                response.put("data", statusOpt.get());
                response.put("queryMethod", "cache_based_single");
            } else {
                response.put("success", false);
                response.put("message", "未找到该学生的状态信息");
                response.put("personCode", personCode);
            }
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("查询学生状态失败: personCode={}, error={}", personCode, e.getMessage(), e);
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 获取指定楼栋的统计信息（优化版本）
     * POST /api/optimized-dormitory-status/buildings-statistics
     * Request Body: {"buildingCodes": ["1", "2", "3"]}
     */
    @PostMapping("/buildings-statistics")
    public ResponseEntity<Map<String, Object>> getBuildingsStatistics(
            @RequestBody Map<String, Object> requestBody) {
        
        logger.info("获取指定楼栋统计信息（优化版本）");
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            @SuppressWarnings("unchecked")
            List<String> buildingCodes = (List<String>) requestBody.get("buildingCodes");
            
            if (buildingCodes == null || buildingCodes.isEmpty()) {
                response.put("success", false);
                response.put("message", "楼栋代码列表不能为空");
                return ResponseEntity.badRequest().body(response);
            }
            
            Map<String, Object> statistics = cachedDormitoryStatsService.getDormitoryStatisticsByBuildings(buildingCodes);
            
            response.put("success", true);
            response.put("message", "查询成功");
            response.put("data", statistics);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取楼栋统计信息失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 获取可用楼栋列表
     * GET /api/optimized-dormitory-status/buildings
     */
    @GetMapping("/buildings")
    public ResponseEntity<Map<String, Object>> getAvailableBuildings() {
        
        logger.info("获取可用楼栋列表");
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            List<Map<String, Object>> buildings = cachedDormitoryStatsService.getAvailableBuildings();
            
            response.put("success", true);
            response.put("message", "查询成功");
            response.put("data", buildings);
            response.put("count", buildings.size());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取楼栋列表失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 刷新统计缓存
     * POST /api/optimized-dormitory-status/refresh-cache
     */
    @PostMapping("/refresh-cache")
    public ResponseEntity<Map<String, Object>> refreshCache() {
        
        logger.info("手动刷新统计缓存");
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            Map<String, Object> refreshedStats = cachedDormitoryStatsService.refreshStatisticsCache();
            
            response.put("success", true);
            response.put("message", "缓存刷新成功");
            response.put("refreshedData", refreshedStats);
            response.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("刷新缓存失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "刷新缓存失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 获取缓存统计信息
     * GET /api/optimized-dormitory-status/cache-statistics
     */
    @GetMapping("/cache-statistics")
    public ResponseEntity<Map<String, Object>> getCacheStatistics() {
        
        logger.info("获取缓存统计信息");
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            Map<String, Object> cacheStats = cachedDormitoryStatsService.getCacheStatistics();
            
            response.put("success", true);
            response.put("message", "查询成功");
            response.put("data", cacheStats);
            response.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取缓存统计失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 清除所有缓存
     * DELETE /api/optimized-dormitory-status/cache
     */
    @DeleteMapping("/cache")
    public ResponseEntity<Map<String, Object>> clearCache() {
        
        logger.info("清除所有缓存");
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            cachedDormitoryStatsService.clearAllCache();
            
            response.put("success", true);
            response.put("message", "缓存清除成功");
            response.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("清除缓存失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "清除缓存失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 健康检查接口
     * GET /api/optimized-dormitory-status/health
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        Map<String, Object> response = new HashMap<>();

        try {
            Map<String, Object> statistics = cachedDormitoryStatsService.getDormitoryStatistics();
            Map<String, Object> cacheStats = cachedDormitoryStatsService.getCacheStatistics();

            response.put("success", true);
            response.put("message", "优化版寝室归宿查询服务运行正常");
            response.put("todayStatistics", statistics);
            response.put("cacheStatistics", cacheStats);
            response.put("timestamp", System.currentTimeMillis());
            response.put("serviceVersion", "optimized_assigned_dormitory_redis");

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("健康检查失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "服务异常: " + e.getMessage());
            response.put("timestamp", System.currentTimeMillis());
            return ResponseEntity.status(500).body(response);
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 获取状态描述
     * @param status 状态代码
     * @return 状态描述
     */
    private String getStatusDescription(Integer status) {
        if (status == null) return "未知状态";
        
        switch (status) {
            case 0: return "无通行记录";
            case 1: return "在寝";
            case 2: return "外出";
            default: return "未知状态";
        }
    }
}
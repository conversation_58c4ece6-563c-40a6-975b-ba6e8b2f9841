package fastgatedemo.demo.controller;

import com.alibaba.fastjson.JSON;
import fastgatedemo.demo.service.ViewMessageProcessService;
import fastgatedemo.demo.service.StudentStatusCacheService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * 测试推送控制器
 * 用于模拟推送进出记录消息
 */
@RestController
@RequestMapping("/api/test")
public class TestPushController {

    private static final Logger logger = LoggerFactory.getLogger(TestPushController.class);

    @Autowired
    private ViewMessageProcessService messageProcessService;

    @Autowired
    private StudentStatusCacheService statusCacheService;

    /**
     * 模拟推送进出记录
     */
    @PostMapping("/push-record")
    public ResponseEntity<Map<String, Object>> pushRecord(@RequestBody Map<String, Object> request) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 提取参数
            String personCode = (String) request.get("personCode");
            String personName = (String) request.get("personName");
            Integer deviceDirection = (Integer) request.get("deviceDirection");
            String deviceName = (String) request.get("deviceName");
            String deviceId = (String) request.get("deviceId");
            String deviceIp = (String) request.get("deviceIp");
            
            // 生成时间戳（14位数字格式）
            String checkTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + "000";
            
            // 构建符合真实格式的消息
            Map<String, Object> message = new HashMap<>();
            
            // PersonCardObjectList
            Map<String, Object> personCard = new HashMap<>();
            personCard.put("CapTime", checkTime);
            personCard.put("DeviceID", deviceId);
            message.put("PersonCardObjectList", new Object[]{personCard});
            
            // PersonCheckObjectList
            Map<String, Object> personCheck = new HashMap<>();
            personCheck.put("CardID", "");
            personCheck.put("CardStatus", 0);
            personCheck.put("CheckModeIndex", 256);
            personCheck.put("CheckTime", checkTime);
            personCheck.put("CodeNo", personCode);
            personCheck.put("CodeType", 0);
            personCheck.put("DepartmentCode", "iccsid");
            personCheck.put("DepartmentName", "南昌健康职业技术学院");
            personCheck.put("DeviceDirection", deviceDirection);
            personCheck.put("DeviceID", deviceId);
            personCheck.put("DeviceIP", deviceIp);
            personCheck.put("DeviceName", deviceName);
            personCheck.put("DeviceType", "5");
            personCheck.put("IdentityID", "");
            personCheck.put("LibID", 3);
            personCheck.put("LibType", 1);
            personCheck.put("MatchConfidence", 99);
            personCheck.put("MatchFaceID", 6916);
            personCheck.put("MatchPersonID", 3400);
            personCheck.put("MatchStatus", 1);
            personCheck.put("Name", personName);
            personCheck.put("PersonType", 3);
            personCheck.put("RecordId", System.currentTimeMillis());
            personCheck.put("Respath", "/iccsid");
            personCheck.put("RespathName", "/南昌健康职业技术学院");
            personCheck.put("Sex", 0);
            personCheck.put("Temperature", 0.0);
            personCheck.put("TemperatureFlag", 0);
            
            message.put("PersonCheckObjectList", new Object[]{personCheck});
            message.put("TriggerTime", System.currentTimeMillis());
            
            // 转换为JSON字符串
            String messageJson = JSON.toJSONString(message);
            
            logger.info("模拟推送消息: {}", messageJson);
            
            // 调用消息处理服务
            boolean processed = messageProcessService.processMessage(messageJson);
            
            if (processed) {
                response.put("success", true);
                response.put("message", "推送成功");
                response.put("data", message);
            } else {
                response.put("success", false);
                response.put("message", "推送处理失败");
                response.put("data", message);
            }
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("模拟推送失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "推送失败: " + e.getMessage());
            return ResponseEntity.ok(response);
        }
    }

    /**
     * 获取学生列表（用于下拉选择）
     */
    @GetMapping("/students")
    public ResponseEntity<Map<String, Object>> getStudents() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 这里可以调用数据库查询学生列表
            // 为了简化，直接返回我们添加的测试学生
            Map<String, Object>[] students = new Map[]{
                createStudent("SG000011", "周腊妹"),
                createStudent("SG000012", "张小明"),
                createStudent("SG000013", "李小红"),
                createStudent("SG000014", "王小强"),
                createStudent("SG000015", "刘小美")
            };
            
            response.put("success", true);
            response.put("data", students);
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取学生列表失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "获取学生列表失败: " + e.getMessage());
            return ResponseEntity.ok(response);
        }
    }
    
    /**
     * 查询Redis中的学生状态缓存
     */
    @GetMapping("/redis-cache")
    public ResponseEntity<Map<String, Object>> getRedisCache(@RequestParam(required = false) String personCode) {
        Map<String, Object> response = new HashMap<>();

        try {
            if (personCode != null && !personCode.trim().isEmpty()) {
                // 查询单个学生的缓存状态
                Map<String, Object> studentCache = statusCacheService.getStudentCacheInfo(personCode.trim());
                if (studentCache != null && !studentCache.isEmpty()) {
                    response.put("success", true);
                    response.put("data", studentCache);
                    response.put("message", "查询成功");
                } else {
                    response.put("success", false);
                    response.put("message", "未找到该学生的缓存数据");
                }
            } else {
                // 查询所有学生的缓存状态
                Map<String, Object> allCache = statusCacheService.getAllStudentCacheInfo();
                response.put("success", true);
                response.put("data", allCache);
                response.put("message", "查询成功，共" + allCache.size() + "条缓存记录");
            }

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("查询Redis缓存失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "查询Redis缓存失败: " + e.getMessage());
            return ResponseEntity.ok(response);
        }
    }

    /**
     * 清除Redis中的学生状态缓存
     */
    @DeleteMapping("/redis-cache")
    public ResponseEntity<Map<String, Object>> clearRedisCache(@RequestParam(required = false) String personCode) {
        Map<String, Object> response = new HashMap<>();

        try {
            if (personCode != null && !personCode.trim().isEmpty()) {
                // 清除单个学生的缓存
                boolean cleared = statusCacheService.clearStudentCache(personCode.trim());
                if (cleared) {
                    response.put("success", true);
                    response.put("message", "清除学生缓存成功: " + personCode);
                } else {
                    response.put("success", false);
                    response.put("message", "清除学生缓存失败或缓存不存在: " + personCode);
                }
            } else {
                // 清除所有学生缓存
                int clearedCount = statusCacheService.clearAllStudentCache();
                response.put("success", true);
                response.put("message", "清除所有学生缓存成功，共清除" + clearedCount + "条记录");
                response.put("clearedCount", clearedCount);
            }

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("清除Redis缓存失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "清除Redis缓存失败: " + e.getMessage());
            return ResponseEntity.ok(response);
        }
    }

    private Map<String, Object> createStudent(String code, String name) {
        Map<String, Object> student = new HashMap<>();
        student.put("personCode", code);
        student.put("personName", name);
        return student;
    }
}

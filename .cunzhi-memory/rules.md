# 开发规范和规则

- 用户要求优化dashboard后台接口查询逻辑：总人数从fastgate数据源的人员表中查询所有有效人员，在寝人数根据出入记录表中每个人最后一条记录为"进"的人数，外出人数为最后一条记录为"出"的人数。需要基于fastgate数据源进行统计。
- 用户要求在寝状态应该按照最新的时间来判断，而不是某一天的记录。需要修改统计逻辑，查询每个人的最新一条出入记录来判断当前是否在寝。
- 大屏统计接口已修改为新的业务逻辑：总人数从人员表（tbl_person）查询有效人员（status=1），在寝人数和外出人数根据出入记录表中每个人的最后一条记录判断，以人员表为主，出入记录只是依据。修改了DormitoryStatusService.getDormitoryStatistics()方法，使用countPersonTableReturnedPersons()和countPersonTableNotReturnedPersons()方法。
- 用户明确要求不要再使用api/health接口，前端报CORS和404错误。需要修改前端代码移除对/api/health的调用，改用/api/dashboard/health或简化健康检查逻辑

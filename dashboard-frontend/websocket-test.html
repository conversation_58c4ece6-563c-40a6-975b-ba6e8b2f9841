<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket连接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.connecting { background-color: #fff3cd; color: #856404; }
        .status.connected { background-color: #d4edda; color: #155724; }
        .status.disconnected { background-color: #f8d7da; color: #721c24; }
        .status.error { background-color: #f8d7da; color: #721c24; }
        
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-danger { background-color: #dc3545; color: white; }
        .btn-info { background-color: #17a2b8; color: white; }
        
        #log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        
        input[type="text"] {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>WebSocket连接测试工具</h1>
        
        <div>
            <label for="wsUrl">WebSocket URL:</label>
            <input type="text" id="wsUrl" value="" />
            <button onclick="switchEndpoint()">切换端点</button>
        </div>
        
        <div id="status" class="status disconnected">状态: 未连接</div>
        
        <div>
            <button class="btn-primary" onclick="connect()">连接</button>
            <button class="btn-danger" onclick="disconnect()">断开</button>
            <button class="btn-success" onclick="sendPing()">发送Ping</button>
            <button class="btn-info" onclick="testBackend()">测试后端</button>
            <button class="btn-info" onclick="clearLog()">清空日志</button>
        </div>

        <div style="margin: 10px 0; padding: 10px; background-color: #fff3cd; border-radius: 4px;">
            <strong>注意：</strong>如果大屏页面正在使用WebSocket，请先关闭大屏页面再测试，避免连接冲突。
        </div>
        
        <h3>连接日志:</h3>
        <div id="log"></div>
    </div>

    <script>
        let websocket = null;
        let statusDiv = document.getElementById('status');
        let logDiv = document.getElementById('log');
        let useNativeEndpoint = true;
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logDiv.textContent += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function updateStatus(status, message) {
            statusDiv.className = `status ${status}`;
            statusDiv.textContent = `状态: ${message}`;
        }
        
        function connect() {
            const url = document.getElementById('wsUrl').value;
            log(`尝试连接到: ${url}`);
            
            try {
                updateStatus('connecting', '连接中...');
                websocket = new WebSocket(url);
                
                websocket.onopen = function(event) {
                    log('WebSocket连接成功!');
                    updateStatus('connected', '已连接');
                };
                
                websocket.onmessage = function(event) {
                    log(`收到消息: ${event.data}`);
                    try {
                        const data = JSON.parse(event.data);
                        log(`解析后的消息: ${JSON.stringify(data, null, 2)}`);
                    } catch (e) {
                        log(`消息解析失败: ${e.message}`);
                    }
                };
                
                websocket.onclose = function(event) {
                    log(`连接关闭: code=${event.code}, reason=${event.reason}, wasClean=${event.wasClean}`);
                    updateStatus('disconnected', '已断开');
                };
                
                websocket.onerror = function(error) {
                    log(`连接错误: ${error}`);
                    updateStatus('error', '连接错误');
                };
                
            } catch (error) {
                log(`创建WebSocket失败: ${error.message}`);
                updateStatus('error', '创建失败');
            }
        }
        
        function disconnect() {
            if (websocket) {
                websocket.close();
                websocket = null;
                log('手动断开连接');
            }
        }
        
        function sendPing() {
            if (websocket && websocket.readyState === WebSocket.OPEN) {
                websocket.send('ping');
                log('发送ping消息');
            } else {
                log('WebSocket未连接，无法发送消息');
            }
        }
        
        async function testBackend() {
            log('测试后端服务...');
            
            // 测试健康检查
            try {
                const healthResponse = await fetch('/api/dashboard/health');
                const healthData = await healthResponse.json();
                log(`后端健康检查: ${healthData.success ? '成功' : '失败'}`);
                log(`健康检查响应: ${JSON.stringify(healthData, null, 2)}`);
            } catch (error) {
                log(`后端健康检查失败: ${error.message}`);
            }
            
            // 测试WebSocket状态
            try {
                const wsResponse = await fetch('/api/websocket/status');
                const wsData = await wsResponse.json();
                log(`WebSocket状态: ${wsData.success ? '成功' : '失败'}`);
                log(`WebSocket状态响应: ${JSON.stringify(wsData, null, 2)}`);
            } catch (error) {
                log(`WebSocket状态检查失败: ${error.message}`);
            }
        }
        
        function clearLog() {
            logDiv.textContent = '';
        }
        
        // 页面加载时的初始化
        window.onload = function() {
            log('WebSocket测试工具已加载');
            log('请点击"连接"按钮开始测试');
            
            // 检测当前环境
            const currentUrl = window.location.href;
            log(`当前页面: ${currentUrl}`);
            
            updateWebSocketUrl();
        };

        function updateWebSocketUrl() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const hostname = window.location.hostname;
            const endpoint = useNativeEndpoint ? '/ws/dashboard/records/native' : '/ws/dashboard/records';

            let url;
            if (window.location.port === '3000') {
                // 开发环境，直接连接后端8080端口
                url = `${protocol}//${hostname}:8080${endpoint}`;
                log(`开发环境，连接后端8080端口: ${endpoint}`);
            } else {
                // 生产环境
                const port = window.location.port ? `:${window.location.port}` : '';
                url = `${protocol}//${hostname}${port}${endpoint}`;
                log(`生产环境: ${endpoint}`);
            }

            document.getElementById('wsUrl').value = url;
            log(`当前URL: ${url}`);
        }

        function switchEndpoint() {
            useNativeEndpoint = !useNativeEndpoint;
            updateWebSocketUrl();
            log(`切换到${useNativeEndpoint ? '原生WebSocket' : 'SockJS'}端点`);
        }
    </script>
</body>
</html>

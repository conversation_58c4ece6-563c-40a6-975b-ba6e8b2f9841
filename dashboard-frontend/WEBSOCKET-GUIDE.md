# WebSocket连接使用指南

## 概述

本项目使用了先进的WebSocket连接池管理系统，确保全局只有一个WebSocket连接，避免资源冲突和连接问题。

## 系统架构

```
应用页面 (大屏、测试页面等)
    ↓
WebSocket连接池 (websocket-pool.js)
    ↓
WebSocket管理器 (websocket.js)
    ↓
后端WebSocket服务
```

## 主要特性

### 1. 连接池管理
- **全局单例**：确保整个应用只有一个WebSocket连接
- **订阅机制**：多个页面可以订阅同一个连接
- **自动清理**：没有订阅者时自动关闭连接

### 2. 智能重连
- **快速重连**：500ms, 1s, 2s 指数退避
- **端点切换**：原生WebSocket失败后自动切换到SockJS
- **连接限制**：最多3次重连，避免无限重试

### 3. 环境适配
- **开发环境**：直连后端8080端口
- **生产环境**：自动适配当前域名和协议
- **配置化**：所有参数可在配置文件中调整

## 使用方法

### 在Vue组件中使用

```javascript
import wsPool from '../utils/websocket-pool.js'

// 获取连接
const connection = wsPool.getConnection({
  onOpen: (event) => {
    console.log('连接成功')
  },
  onMessage: (data) => {
    console.log('收到消息:', data)
  },
  onClose: (event) => {
    console.log('连接关闭')
  },
  onError: (error) => {
    console.error('连接错误:', error)
  }
})

// 保存订阅ID
const subscriberId = connection.subscriberId

// 发送消息
wsPool.send({ type: 'ping' })

// 组件销毁时移除订阅
onUnmounted(() => {
  wsPool.removeSubscriber(subscriberId)
})
```

### 配置修改

编辑 `src/config/websocket.js`：

```javascript
development: {
  host: 'localhost',
  port: 8080,
  options: {
    timeout: 5000,           // 连接超时
    maxReconnectAttempts: 3, // 最大重连次数
    reconnectInterval: 500,  // 重连间隔
  }
}
```

## 最佳实践

### 1. 避免连接冲突
- **一次一个页面**：避免同时打开多个使用WebSocket的页面
- **正确清理**：组件销毁时移除订阅
- **测试隔离**：测试时关闭其他WebSocket页面

### 2. 错误处理
```javascript
const connection = wsPool.getConnection({
  onError: (error) => {
    // 处理连接错误
    ElMessage.error('WebSocket连接失败')
  },
  onClose: (event) => {
    // 处理连接关闭
    if (event.code !== 1000) {
      console.warn('连接异常关闭')
    }
  }
})
```

### 3. 状态监控
```javascript
// 获取连接状态
const status = wsPool.getStatus()
const statusText = wsPool.getStatusText()

// 监控状态变化
const connection = wsPool.getConnection({
  onStatusChange: (status) => {
    console.log('状态变化:', status)
    // 更新UI状态
  }
})
```

## 调试指南

### 1. 控制台调试
```javascript
// 查看连接池状态
wsPool.debug()

// 手动重连
wsPool.reconnect()

// 查看当前状态
console.log('状态:', wsPool.getStatus())
console.log('订阅者:', wsPool.subscribers.size)
```

### 2. 测试页面
访问 `/websocket-test.html` 进行连接测试：
- 测试不同端点的连接速度
- 查看详细的连接日志
- 测试消息发送和接收

### 3. 常见问题

#### Q: 连接超时怎么办？
A: 
1. 检查后端服务是否启动
2. 确认端口配置是否正确
3. 关闭其他WebSocket页面
4. 查看浏览器控制台的详细错误

#### Q: 消息收不到怎么办？
A:
1. 确认连接状态是否为 'connected'
2. 检查消息处理回调是否正确设置
3. 查看后端是否正确推送消息

#### Q: 多个页面冲突怎么办？
A:
1. 一次只打开一个WebSocket页面
2. 确保组件销毁时正确移除订阅
3. 使用连接池的调试功能查看订阅者数量

## 部署说明

### 开发环境
1. 启动后端服务（端口8080）
2. 启动前端服务（端口3000）
3. 系统自动连接到 `ws://localhost:8080`

### 生产环境
1. 确保前后端部署在同一域名下
2. 系统自动适配当前域名和协议
3. HTTPS环境下自动使用WSS协议

### Docker部署
```dockerfile
# 确保前后端容器网络互通
# 端口映射正确
# 环境变量配置（如需要）
```

## 性能优化

### 1. 连接复用
- 多个组件共享同一个WebSocket连接
- 减少服务器连接数和资源消耗
- 提高连接建立速度

### 2. 智能重连
- 快速失败和重连策略
- 自动端点切换
- 避免无效的长时间等待

### 3. 内存管理
- 自动清理无用连接
- 订阅者管理
- 定时器清理

## 监控和日志

系统提供详细的日志输出：
- 连接建立和关闭时间
- 重连策略执行过程
- 订阅者管理操作
- 错误详情和诊断信息

所有日志都带有时间戳和详细的上下文信息，便于问题定位和性能分析。

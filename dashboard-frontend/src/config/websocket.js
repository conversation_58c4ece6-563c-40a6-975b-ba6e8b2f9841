/**
 * WebSocket配置文件
 * 统一管理开发环境和生产环境的WebSocket连接配置
 */

// 环境配置
const config = {
  // 开发环境配置
  development: {
    // WebSocket服务地址
    host: 'localhost',
    port: 8080,
    // 是否使用HTTPS/WSS
    secure: false,
    // WebSocket端点路径
    endpoints: {
      // 原生WebSocket端点（推荐）
      native: '/ws/dashboard/records/native',
      // SockJS端点（备用）
      sockjs: '/ws/dashboard/records'
    },
    // 连接选项
    options: {
      // 连接超时时间（毫秒）- 减少超时时间
      timeout: 5000,
      // 最大重连次数
      maxReconnectAttempts: 3,
      // 重连间隔（毫秒）- 使用指数退避
      reconnectInterval: 500,
      // 心跳间隔（毫秒）
      heartbeatInterval: 30000
    }
  },

  // 生产环境配置
  production: {
    // 生产环境使用相对路径，自动适配当前域名和端口
    host: window.location.hostname,
    port: window.location.port || (window.location.protocol === 'https:' ? 443 : 80),
    secure: window.location.protocol === 'https:',
    endpoints: {
      native: '/ws/dashboard/records/native',
      sockjs: '/ws/dashboard/records'
    },
    options: {
      timeout: 15000,
      maxReconnectAttempts: 3,
      reconnectInterval: 2000,
      heartbeatInterval: 60000
    }
  }
}

/**
 * 获取当前环境
 */
function getEnvironment() {
  // 根据端口和主机判断环境
  const port = window.location.port
  const hostname = window.location.hostname
  
  // 开发环境：localhost或127.0.0.1且端口为3000(Vite开发服务器)
  if ((hostname === 'localhost' || hostname === '127.0.0.1') && port === '3000') {
    return 'development'
  }
  
  // 生产环境：其他所有情况（包括IP访问端口81）
  return 'production'
}

/**
 * 获取当前环境的配置
 */
export function getWebSocketConfig() {
  const env = getEnvironment()
  return {
    ...config[env],
    environment: env
  }
}

/**
 * 构建WebSocket URL
 * @param {string} endpoint - 端点类型 ('native' 或 'sockjs')
 * @returns {string} 完整的WebSocket URL
 */
export function buildWebSocketUrl(endpoint = 'native') {
  const cfg = getWebSocketConfig()
  const protocol = cfg.secure ? 'wss:' : 'ws:'

  // 开发环境智能主机检测
  let host = cfg.host
  if (cfg.environment === 'development') {
    // 如果当前页面不是localhost，但配置是localhost，尝试使用当前主机
    if (cfg.host === 'localhost' && window.location.hostname !== 'localhost') {
      console.warn(`[WebSocket] 配置使用localhost，但当前主机是${window.location.hostname}，尝试使用当前主机`)
      host = window.location.hostname
    }
  }

  const port = cfg.port && cfg.port !== '80' && cfg.port !== '443' ? `:${cfg.port}` : ''
  const path = cfg.endpoints[endpoint] || cfg.endpoints.native

  return `${protocol}//${host}${port}${path}`
}

/**
 * 获取连接选项
 */
export function getConnectionOptions() {
  const cfg = getWebSocketConfig()
  return cfg.options
}

/**
 * 日志输出当前配置（调试用）
 */
export function logCurrentConfig() {
  const cfg = getWebSocketConfig()
  console.log('=== WebSocket配置信息 ===')
  console.log('环境:', cfg.environment)
  console.log('主机:', cfg.host)
  console.log('端口:', cfg.port)
  console.log('安全连接:', cfg.secure)
  console.log('原生WebSocket URL:', buildWebSocketUrl('native'))
  console.log('SockJS URL:', buildWebSocketUrl('sockjs'))
  console.log('连接选项:', cfg.options)
  console.log('========================')
}

export default {
  getWebSocketConfig,
  buildWebSocketUrl,
  getConnectionOptions,
  logCurrentConfig
}

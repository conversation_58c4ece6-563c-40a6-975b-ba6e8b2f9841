import { createRouter, createWebHistory } from 'vue-router'
import DashboardView from '../views/DashboardView.vue'
import StudentStatusView from '../components/StudentStatusView.vue'
import TaskManagementView from '../views/TaskManagementView.vue'
import DormitoryManagementView from '../views/DormitoryManagementView.vue'
import WebSocketTest from '../views/WebSocketTest.vue'

const routes = [
  {
    path: '/',
    name: 'Dashboard',
    component: DashboardView,
    meta: {
      title: '宿舍归寝大屏'
    }
  },
  {
    path: '/students',
    name: 'StudentStatus',
    component: StudentStatusView,
    meta: {
      title: '学生在寝情况'
    }
  },
  {
    path: '/tasks',
    name: 'TaskManagement',
    component: TaskManagementView,
    meta: {
      title: '定时任务管理'
    }
  },
  {
    path: '/dormitories',
    name: 'DormitoryManagement',
    component: DormitoryManagementView,
    meta: {
      title: '寝室管理'
    }
  },
  {
    path: '/websocket-test',
    name: 'WebSocketTest',
    component: WebSocketTest,
    meta: {
      title: 'WebSocket测试'
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫 - 设置页面标题
router.beforeEach((to, from, next) => {
  if (to.meta.title) {
    document.title = to.meta.title + ' - 宿舍管理系统'
  }
  next()
})

export default router

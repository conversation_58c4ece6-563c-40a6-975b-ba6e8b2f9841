/**
 * WebSocket连接池管理器
 * 确保全局只有一个WebSocket连接，避免资源冲突
 */

import { WebSocketManager } from './websocket.js'

class WebSocketPool {
  constructor() {
    this.activeConnection = null
    this.subscribers = new Set()
    this.connectionId = 0
    this.metrics = {
      totalConnections: 0,
      successfulConnections: 0,
      failedConnections: 0,
      totalReconnects: 0,
      averageConnectTime: 0,
      lastConnectTime: null,
      uptime: 0,
      startTime: Date.now()
    }
    this.healthCheckInterval = null
    this.statusChangeCallbacks = new Set()
    this.startHealthCheck()
  }

  /**
   * 获取或创建WebSocket连接
   * @param {Object} callbacks - 事件回调函数
   * @returns {Object} 连接信息
   */
  getConnection(callbacks = {}) {
    const subscriberId = ++this.connectionId

    // 如果已有活跃连接，直接复用
    if (this.activeConnection && this.activeConnection.status === 'connected') {
      this.addSubscriber(subscriberId, callbacks)
      return {
        subscriberId,
        status: 'connected',
        manager: this.activeConnection
      }
    }

    // 如果正在连接中，等待连接完成
    if (this.activeConnection && this.activeConnection.status === 'connecting') {
      this.addSubscriber(subscriberId, callbacks)
      return {
        subscriberId,
        status: 'connecting',
        manager: this.activeConnection
      }
    }

    // 创建新连接
    this.createNewConnection()
    this.addSubscriber(subscriberId, callbacks)

    return {
      subscriberId,
      status: 'connecting',
      manager: this.activeConnection
    }
  }

  /**
   * 创建新的WebSocket连接
   */
  createNewConnection() {
    // 清理旧连接
    if (this.activeConnection) {
      console.log('[WebSocketPool] 清理旧连接')
      this.activeConnection.disconnect()
    }

    // 更新指标
    this.metrics.totalConnections++
    const connectStartTime = performance.now()

    // 创建新连接
    this.activeConnection = new WebSocketManager()

    // 设置连接池级别的事件处理
    this.activeConnection.onStatusChange = (status) => {
      this.updateMetrics(status, connectStartTime)
      this.notifySubscribers('statusChange', status)
      this.notifyStatusChange(status)
    }

    this.activeConnection.onOpen = (event) => {
      const connectTime = performance.now() - connectStartTime
      this.metrics.successfulConnections++
      this.metrics.lastConnectTime = connectTime
      this.updateAverageConnectTime(connectTime)
      this.notifySubscribers('open', event)
    }

    this.activeConnection.onMessage = (data, event) => {
      this.notifySubscribers('message', data, event)
    }

    this.activeConnection.onClose = (event) => {
      this.notifySubscribers('close', event)
    }

    this.activeConnection.onError = (error) => {
      this.metrics.failedConnections++
      this.notifySubscribers('error', error)
    }

    // 开始连接
    this.activeConnection.connect()
  }

  /**
   * 添加订阅者
   */
  addSubscriber(subscriberId, callbacks) {
    this.subscribers.add({
      id: subscriberId,
      callbacks: callbacks || {}
    })
  }

  /**
   * 移除订阅者
   */
  removeSubscriber(subscriberId) {
    this.subscribers = new Set([...this.subscribers].filter(sub => sub.id !== subscriberId))

    // 如果没有订阅者了，关闭连接
    if (this.subscribers.size === 0) {
      if (this.activeConnection) {
        this.activeConnection.disconnect()
        this.activeConnection = null
      }
    }
  }

  /**
   * 通知所有订阅者
   */
  notifySubscribers(eventType, ...args) {
    this.subscribers.forEach(subscriber => {
      const callback = subscriber.callbacks[`on${eventType.charAt(0).toUpperCase() + eventType.slice(1)}`]
      if (callback && typeof callback === 'function') {
        try {
          callback(...args)
        } catch (error) {
          console.error(`[WebSocketPool] 订阅者 ${subscriber.id} 回调错误:`, error)
        }
      }
    })
  }

  /**
   * 发送消息
   */
  send(data) {
    if (this.activeConnection) {
      return this.activeConnection.send(data)
    }
    console.warn('[WebSocketPool] 没有活跃连接，无法发送消息')
    return false
  }

  /**
   * 手动重连
   */
  reconnect() {
    console.log('[WebSocketPool] 手动重连')
    this.metrics.totalReconnects++

    if (this.activeConnection) {
      this.activeConnection.reconnect()
    } else {
      this.createNewConnection()
    }
  }

  /**
   * 强制重置连接
   */
  forceReset() {
    console.log('[WebSocketPool] 强制重置连接')

    // 清理所有状态
    if (this.activeConnection) {
      this.activeConnection.disconnect()
      this.activeConnection = null
    }

    // 重置指标
    this.metrics.totalReconnects++

    // 如果有订阅者，重新创建连接
    if (this.subscribers.size > 0) {
      this.createNewConnection()
    }
  }

  /**
   * 检查连接健康状态
   */
  checkHealth() {
    if (!this.activeConnection) {
      return { healthy: false, reason: '无活跃连接' }
    }

    const status = this.activeConnection.status
    const metrics = this.getMetrics()

    // 健康检查条件
    const isHealthy =
      status === 'connected' &&
      metrics.successRate !== '0%' &&
      this.subscribers.size > 0

    return {
      healthy: isHealthy,
      status: status,
      subscribers: this.subscribers.size,
      successRate: metrics.successRate,
      uptime: metrics.uptime,
      lastConnectTime: metrics.lastConnectTime
    }
  }

  /**
   * 获取连接状态
   */
  getStatus() {
    return this.activeConnection ? this.activeConnection.status : 'disconnected'
  }

  /**
   * 获取状态文本
   */
  getStatusText() {
    return this.activeConnection ? this.activeConnection.getStatusText() : '未连接'
  }

  /**
   * 更新连接指标
   */
  updateMetrics(status, connectStartTime) {
    if (status === 'connected') {
      this.metrics.uptime = Date.now() - this.metrics.startTime
    }
  }

  /**
   * 更新平均连接时间
   */
  updateAverageConnectTime(connectTime) {
    const total = this.metrics.successfulConnections
    this.metrics.averageConnectTime =
      (this.metrics.averageConnectTime * (total - 1) + connectTime) / total
  }

  /**
   * 启动健康检查
   */
  startHealthCheck() {
    this.healthCheckInterval = setInterval(() => {
      if (this.activeConnection && this.activeConnection.status === 'connected') {
        // 发送心跳检查连接健康状态
        this.activeConnection.send('ping')
      }
    }, 30000) // 30秒检查一次
  }

  /**
   * 停止健康检查
   */
  stopHealthCheck() {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval)
      this.healthCheckInterval = null
    }
  }

  /**
   * 获取性能指标
   */
  getMetrics() {
    return {
      ...this.metrics,
      uptime: Date.now() - this.metrics.startTime,
      successRate: this.metrics.totalConnections > 0
        ? (this.metrics.successfulConnections / this.metrics.totalConnections * 100).toFixed(2) + '%'
        : '0%'
    }
  }

  /**
   * 添加状态变化监听器
   */
  onStatusChange(callback) {
    this.statusChangeCallbacks.add(callback)
    return () => this.statusChangeCallbacks.delete(callback)
  }

  /**
   * 通知状态变化
   */
  notifyStatusChange(status) {
    this.statusChangeCallbacks.forEach(callback => {
      try {
        callback(status)
      } catch (error) {
        console.error('状态变化回调错误:', error)
      }
    })
  }

  /**
   * 调试信息
   */
  debug() {
    console.log('=== WebSocket连接池状态 ===')
    console.log('活跃连接:', this.activeConnection)
    console.log('订阅者数量:', this.subscribers.size)
    console.log('订阅者列表:', [...this.subscribers].map(s => s.id))
    console.log('连接状态:', this.getStatus())
    console.log('性能指标:', this.getMetrics())

    if (this.activeConnection) {
      this.activeConnection.debug()
    }
  }
}

// 全局单例
const wsPool = new WebSocketPool()

export default wsPool
export { WebSocketPool }

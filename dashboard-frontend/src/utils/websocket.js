/**
 * WebSocket连接管理器
 * 简化的、统一的WebSocket连接处理
 */

import { buildWebSocketUrl, getConnectionOptions, logCurrentConfig } from '../config/websocket.js'

export class WebSocketManager {
  constructor() {
    this.ws = null
    this.status = 'disconnected' // disconnected, connecting, connected, error
    this.reconnectAttempts = 0
    this.reconnectTimer = null
    this.heartbeatTimer = null
    this.useNativeEndpoint = true
    this.options = getConnectionOptions()
    
    // 事件回调
    this.onOpen = null
    this.onMessage = null
    this.onClose = null
    this.onError = null
    this.onStatusChange = null
  }

  /**
   * 连接WebSocket
   */
  async connect() {
    if (this.ws && this.ws.readyState === WebSocket.CONNECTING) {
      console.log('WebSocket正在连接中，跳过重复连接')
      return
    }

    // 检查是否有其他WebSocket连接
    this.checkExistingConnections()

    this.disconnect() // 确保清理现有连接

    const endpoint = this.useNativeEndpoint ? 'native' : 'sockjs'
    const url = buildWebSocketUrl(endpoint)

    // 连接前网络检测
    const networkOk = await this.checkNetwork(url)
    if (!networkOk) {
      console.warn('网络检测失败，但仍尝试连接WebSocket')
    }

    // 记录连接开始时间
    this.connectStartTime = performance.now()
    this.setStatus('connecting')

    try {
      this.ws = new WebSocket(url)
      this.setupEventHandlers()
      this.setupConnectionTimeout()
    } catch (error) {
      console.error('创建WebSocket连接失败:', error)
      this.setStatus('error')
      this.scheduleReconnect()
    }
  }

  /**
   * 网络连接检测
   */
  async checkNetwork(wsUrl) {
    try {
      // 提取主机和端口信息
      const url = new URL(wsUrl.replace('ws://', 'http://').replace('wss://', 'https://'))
      const testUrl = `${url.protocol}//${url.host}/api/dashboard/health`

      const response = await fetch(testUrl, {
        timeout: 3000,
        signal: AbortSignal.timeout(3000)
      })

      return response.ok
    } catch (error) {
      return false
    }
  }

  /**
   * 检查现有连接
   */
  checkExistingConnections() {
    // 检查全局WebSocket连接数
    const wsConnections = []
    if (window.wsManager && window.wsManager !== this) {
      wsConnections.push('其他WebSocketManager实例')
    }

    if (wsConnections.length > 0) {
      console.warn('检测到其他WebSocket连接:', wsConnections)
    }

    // 将当前实例注册到全局
    window.wsManager = this
  }

  /**
   * 断开连接
   */
  disconnect() {
    this.clearTimers()
    
    if (this.ws) {
      this.ws.onopen = null
      this.ws.onmessage = null
      this.ws.onclose = null
      this.ws.onerror = null
      
      if (this.ws.readyState === WebSocket.OPEN || this.ws.readyState === WebSocket.CONNECTING) {
        this.ws.close(1000, '手动断开')
      }
      this.ws = null
    }
    
    this.setStatus('disconnected')
  }

  /**
   * 发送消息
   */
  send(data) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      const message = typeof data === 'string' ? data : JSON.stringify(data)
      this.ws.send(message)
      return true
    }
    console.warn('WebSocket未连接，无法发送消息:', data)
    return false
  }

  /**
   * 设置事件处理器
   */
  setupEventHandlers() {
    this.ws.onopen = (event) => {
      // 清除连接超时定时器
      if (this.connectionTimeout) {
        clearTimeout(this.connectionTimeout)
        this.connectionTimeout = null
      }

      this.reconnectAttempts = 0
      this.useNativeEndpoint = true // 连接成功后重置为原生端点
      this.setStatus('connected')
      this.startHeartbeat()

      if (this.onOpen) {
        this.onOpen(event)
      }
    }

    this.ws.onmessage = (event) => {
      if (this.onMessage) {
        try {
          const data = JSON.parse(event.data)
          this.onMessage(data, event)
        } catch (error) {
          console.warn('解析WebSocket消息失败:', error, event.data)
          this.onMessage(event.data, event)
        }
      }
    }

    this.ws.onclose = (event) => {
      console.log('WebSocket连接关闭:', event.code, event.reason)
      this.clearTimers()
      this.setStatus('disconnected')
      
      if (this.onClose) {
        this.onClose(event)
      }

      // 非正常关闭时尝试重连
      if (event.code !== 1000 && event.code !== 1001) {
        this.scheduleReconnect()
      }
    }

    this.ws.onerror = (error) => {
      console.error('WebSocket连接错误:', error)
      this.setStatus('error')
      
      if (this.onError) {
        this.onError(error)
      }
    }
  }

  /**
   * 设置连接超时
   */
  setupConnectionTimeout() {
    this.connectionTimeout = setTimeout(() => {
      if (this.ws && this.ws.readyState === WebSocket.CONNECTING) {
        const connectTime = performance.now() - this.connectStartTime
        console.log(`[${new Date().toLocaleTimeString()}] WebSocket连接超时，已等待: ${Math.round(connectTime)}ms`)
        this.ws.close()
      }
    }, this.options.timeout)
  }

  /**
   * 安排重连
   */
  scheduleReconnect() {
    if (this.reconnectAttempts >= this.options.maxReconnectAttempts) {
      console.log('已达到最大重连次数，停止重连')
      this.setStatus('error')
      return
    }

    this.reconnectAttempts++

    // 第2次重连时切换到SockJS端点（更快切换）
    if (this.reconnectAttempts === 2 && this.useNativeEndpoint) {
      console.log('切换到SockJS端点重试')
      this.useNativeEndpoint = false
      this.reconnectAttempts = 1 // 给SockJS一个新的机会
    }

    // 更快的重连间隔：500ms, 1s, 2s
    const delay = Math.min(
      this.options.reconnectInterval * Math.pow(2, this.reconnectAttempts - 1),
      5000 // 最大5秒
    )

    console.log(`${delay/1000}秒后进行第${this.reconnectAttempts}次重连`)

    this.reconnectTimer = setTimeout(() => {
      this.connect()
    }, delay)
  }

  /**
   * 开始心跳
   */
  startHeartbeat() {
    this.heartbeatTimer = setInterval(() => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.send('ping')
      }
    }, this.options.heartbeatInterval)
  }

  /**
   * 清理定时器
   */
  clearTimers() {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }

    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }

    if (this.connectionTimeout) {
      clearTimeout(this.connectionTimeout)
      this.connectionTimeout = null
    }
  }

  /**
   * 设置状态
   */
  setStatus(status) {
    if (this.status !== status) {
      this.status = status
      if (this.onStatusChange) {
        this.onStatusChange(status)
      }
    }
  }

  /**
   * 获取状态文本
   */
  getStatusText() {
    const statusMap = {
      'disconnected': this.reconnectAttempts >= this.options.maxReconnectAttempts ? '连接失败' : '未连接',
      'connecting': this.reconnectAttempts > 0 ? `重连中(${this.reconnectAttempts}/${this.options.maxReconnectAttempts})` : '连接中',
      'connected': '已连接',
      'error': '连接错误'
    }
    return statusMap[this.status] || '未知状态'
  }

  /**
   * 手动重连
   */
  reconnect() {
    console.log('手动重连WebSocket')
    this.reconnectAttempts = 0
    this.useNativeEndpoint = true
    this.connect()
  }

  /**
   * 输出调试信息
   */
  debug() {
    logCurrentConfig()
    console.log('当前状态:', this.status)
    console.log('重连次数:', this.reconnectAttempts)
    console.log('使用原生端点:', this.useNativeEndpoint)
    console.log('WebSocket对象:', this.ws)
    console.log('连接开始时间:', this.connectStartTime)

    // 测试网络连接速度
    this.testNetworkSpeed()
  }

  /**
   * 测试网络连接速度
   */
  async testNetworkSpeed() {
    console.log('=== 网络连接速度测试 ===')

    // 测试HTTP请求速度
    const httpStart = performance.now()
    try {
      await fetch('/api/dashboard/health')
      const httpTime = performance.now() - httpStart
      console.log(`HTTP请求耗时: ${Math.round(httpTime)}ms`)
    } catch (error) {
      console.log('HTTP请求失败:', error.message)
    }

    // 测试WebSocket连接速度（如果未连接）
    if (this.status !== 'connected') {
      console.log('WebSocket未连接，无法测试WebSocket速度')
    } else {
      console.log('WebSocket已连接，连接正常')
    }
  }
}

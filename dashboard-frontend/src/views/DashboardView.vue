<template>
  <div class="dashboard-app" :class="{ fullscreen: isFullscreen }">
    <!-- 头部标题 -->
    <header class="dashboard-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="main-title gradient-text">
            <el-icon><Monitor /></el-icon>
            宿舍归寝情况大屏展示系统
          </h1>
          <div class="subtitle">
            {{ currentDate }} {{ currentTime }}
          </div>
        </div>
        
        <div class="status-section">
          <div class="connection-status">
            <span class="status-indicator" :class="connectionStatus"></span>
            {{ connectionStatus === 'online' ? '系统正常' : '连接异常' }}
          </div>

          <!-- 菜单按钮 -->
          <div class="menu-buttons">
            <el-button
              type="success"
              @click="goToStudentStatus"
              size="small"
              title="学生状态查询"
            >
              <el-icon><User /></el-icon>
            </el-button>

            <el-button
              type="warning"
              @click="goToTaskManagement"
              size="small"
              title="任务管理"
            >
              <el-icon><Timer /></el-icon>
            </el-button>

            <el-button
              type="info"
              @click="goToDormitoryManagement"
              size="small"
              title="寝室管理"
            >
              <el-icon><Connection /></el-icon>
            </el-button>

            <el-button
              type="warning"
              @click="goToWebSocketTest"
              size="small"
              title="WebSocket测试"
            >
              <el-icon><Monitor /></el-icon>
            </el-button>
          </div>

          <!-- 进入大屏模式按钮 -->
          <el-button
            v-if="!isFullscreen"
            type="primary"
            @click="toggleFullscreen"
            size="small"
            circle
            title="进入大屏模式 (F11)"
          >
            <el-icon><Monitor /></el-icon>
          </el-button>

          <!-- 非大屏模式：楼栋选择器 -->
          <div class="building-selector-compact" v-show="!isFullscreen">
            <div class="selector-info">
              <span class="selector-label">楼栋:</span>
              <span class="selected-info">
                {{ getSelectedBuildingsText() }}
              </span>
            </div>

            <div class="selector-actions">
              <!-- 选择按钮 -->
              <el-popover
                placement="bottom-end"
                :width="300"
                trigger="click"
                popper-class="building-selector-popover"
              >
                <template #reference>
                  <el-button
                    type="primary"
                    size="default"
                    :loading="buildingsLoading"
                    class="select-btn"
                  >
                    选择楼栋
                    <el-icon><ArrowDown /></el-icon>
                  </el-button>
                </template>

                <div class="building-options">
                  <div class="option-item" @click="selectAllBuildings">
                    <el-checkbox
                      :model-value="selectedBuildingCodes.length === 0"
                    />
                    <span>全部楼栋</span>
                  </div>

                  <div
                    v-for="building in buildings"
                    :key="building.buildingCode"
                    class="option-item"
                    @click="toggleBuilding(building.buildingCode)"
                  >
                    <el-checkbox
                      :model-value="selectedBuildingCodes.includes(building.buildingCode)"
                    />
                    <span>{{ building.buildingName }}</span>
                  </div>
                </div>
              </el-popover>
            </div>
          </div>

          <!-- 大屏模式：楼栋显示 + 退出按钮 -->
          <div class="building-display" v-show="isFullscreen">
            <span class="building-text">楼栋: {{ getSelectedBuildingsText() }}</span>

            <!-- 大屏模式指示器 -->
            <span class="fullscreen-indicator">
              <el-icon><Monitor /></el-icon>
              大屏模式
            </span>

            <!-- 小的退出大屏按钮 -->
            <el-button
              type="danger"
              @click="toggleFullscreen"
              size="small"
              class="small-exit-btn"
              title="退出大屏模式 (ESC)"
            >
              <el-icon><Close /></el-icon>
            </el-button>
          </div>
        </div>
      </div>
    </header>
    
    <!-- 主要内容区域 -->
    <main class="dashboard-main">
      <!-- 统计面板 -->
      <section class="statistics-section">
        <StatisticsPanel 
          ref="statisticsPanelRef"
          :statistics="statistics" 
        />
      </section>
      
      <!-- 记录面板 -->
      <section class="records-section">
        <RecordsPanel 
          ref="recordsPanelRef"
          :records="allRecords" 
        />
      </section>
    </main>
    
    <!-- 底部信息 -->
    <footer class="dashboard-footer">
      <div class="footer-content">
        <div class="system-info">
          <span>最后更新: {{ lastUpdateTime }}</span>
        </div>
        <div class="copyright">
          © 2025 宿舍管理系统 - 实时数据展示
        </div>
      </div>
    </footer>
    
    <!-- 加载遮罩 -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-content">
        <el-icon class="loading-icon"><Loading /></el-icon>
        <p>正在加载数据...</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Monitor, Loading, User, Refresh, ArrowDown, Connection, Close, Timer } from '@element-plus/icons-vue'
import StatisticsPanel from '../components/StatisticsPanel.vue'
import RecordsPanel from '../components/RecordsPanel.vue'
import { dormitoryAPI } from '../api/dormitory.js'
import wsPool from '../utils/websocket-pool.js'

const router = useRouter()

// 响应式数据
const loading = ref(true)
const connectionStatus = ref('online')
const currentDate = ref('')
const currentTime = ref('')
const lastUpdateTime = ref('')
const refreshInterval = 30000 // 30秒刷新一次
const isFullscreen = ref(false) // 大屏模式状态


// 楼栋选择相关
const buildings = ref([])
const selectedBuildingCodes = ref([]) // 多选楼栋代码数组
const buildingsLoading = ref(false)

// 统计数据
const statistics = reactive({
  totalPersons: 0,
  inDormitoryPersons: 0,
  outDormitoryPersons: 0,
  personsWithoutRecords: 0,
  returnRate: 0,
  date: ''
})

// 记录数据
const allRecords = ref([])
const maxRecordsCount = 50 // 最大显示记录数

// 组件引用
const statisticsPanelRef = ref(null)
const recordsPanelRef = ref(null)

// WebSocket相关
let wsSubscriberId = null
const websocketStatus = ref('disconnected')

// 定时器
let timeTimer = null

// 检查后端服务是否可用（使用dashboard健康检查接口）
const checkBackendService = async (hostname, port) => {
  try {
    const response = await fetch(`http://${hostname}:${port}/api/dashboard/health`, {
      method: 'GET',
      timeout: 3000
    })
    return response.ok
  } catch (error) {
    console.warn('后端服务检查失败:', error.message)
    return false
  }
}

// 导航到学生状态页面
const goToStudentStatus = () => {
  router.push('/students')
}

// 导航方法
const goToTaskManagement = () => {
  router.push('/tasks')
}

const goToDormitoryManagement = () => {
  router.push('/dormitories')
}

const goToWebSocketTest = () => {
  router.push('/websocket-test')
}

// 测试WebSocket推送
const testWebSocketPush = async () => {
  if (wsPool.getStatus() !== 'connected') {
    ElMessage.warning('WebSocket未连接，无法测试推送')
    return
  }

  try {
    console.log('测试WebSocket推送...')
    const response = await fetch('/api/websocket/test/push', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    })

    if (response.ok) {
      const result = await response.json()
      ElMessage.success('WebSocket测试推送成功')
      console.log('WebSocket测试推送结果:', result)
    } else {
      ElMessage.error('WebSocket测试推送失败')
    }
  } catch (error) {
    console.error('WebSocket测试推送错误:', error)
    ElMessage.error('WebSocket测试推送错误: ' + error.message)
  }
}

// 重新连接WebSocket
const reconnectWebSocket = () => {
  console.log('手动重新连接WebSocket...')
  wsPool.reconnect()
  ElMessage.info('正在重新连接WebSocket...')
}

// 强制重置WebSocket连接
const forceResetWebSocket = () => {
  console.log('强制重置WebSocket连接...')
  ElMessageBox.confirm(
    '强制重置将断开所有WebSocket连接并重新建立，确定要继续吗？',
    '确认重置',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    wsPool.forceReset()
    ElMessage.success('WebSocket连接已强制重置')
  }).catch(() => {
    ElMessage.info('已取消重置操作')
  })
}

// WebSocket连接诊断
const diagnoseWebSocket = async () => {
  console.log('=== WebSocket连接诊断 ===')

  // 检查连接健康状态
  const health = wsPool.checkHealth()
  console.log('连接健康状态:', health)

  // 输出性能指标
  const metrics = wsPool.getMetrics()
  console.log('性能指标:', metrics)

  // 输出WebSocket连接池的调试信息
  wsPool.debug()

  console.log('测试后端健康检查:')
  const startTime = performance.now()
  try {
    const healthResponse = await fetch('/api/dashboard/health')
    const healthData = await healthResponse.json()
    const endTime = performance.now()
    console.log('   - 后端健康检查:', healthData.success ? '成功' : '失败', `(${Math.round(endTime - startTime)}ms)`)
  } catch (error) {
    const endTime = performance.now()
    console.log('   - 后端健康检查失败:', error.message, `(${Math.round(endTime - startTime)}ms)`)
  }

  // 如果连接不健康，提供修复建议
  if (!health.healthy) {
    console.log('🔧 修复建议:')
    if (health.reason === '无活跃连接') {
      console.log('   - 尝试手动重连: wsPool.reconnect()')
    } else if (health.status === 'error') {
      console.log('   - 尝试强制重置: wsPool.forceReset()')
    } else if (health.subscribers === 0) {
      console.log('   - 检查是否有页面订阅了连接')
    }
  }

  console.log('=== 诊断完成 ===')
  ElMessage.info('WebSocket诊断信息已输出到控制台')
}



// 获取WebSocket状态文本
const getWebSocketStatusText = () => {
  return wsPool.getStatusText()
}

// 获取选中楼栋的显示文本
const getSelectedBuildingsText = () => {
  if (selectedBuildingCodes.value.length === 0) {
    return '全部楼栋'
  }

  const selectedNames = selectedBuildingCodes.value.map(code => {
    const building = buildings.value.find(b => b.buildingCode === code)
    return building?.buildingName || code
  })

  if (selectedNames.length <= 2) {
    return selectedNames.join('、')
  } else {
    return `${selectedNames.slice(0, 2).join('、')} 等${selectedNames.length}个楼栋`
  }
}

// 加载楼栋列表
const loadBuildings = async () => {
  try {
    buildingsLoading.value = true
    const response = await dormitoryAPI.getDashboardBuildings()

    if (response.success && response.data) {
      buildings.value = response.data
    } else {
      console.warn('大屏楼栋列表响应异常:', response)
      buildings.value = []
    }
  } catch (error) {
    console.error('加载大屏楼栋列表失败:', error)
    buildings.value = []
  } finally {
    buildingsLoading.value = false
  }
}

// 选择全部楼栋
const selectAllBuildings = async () => {
  console.log('选择全部楼栋')
  selectedBuildingCodes.value = []

  // 查询全部楼栋数据
  await queryDashboardDataUnified(null, false)
  ElMessage.success('已切换到全部楼栋')
}

// 切换单个楼栋选择状态
const toggleBuilding = async (buildingCode) => {
  console.log('切换楼栋选择:', buildingCode)

  // 如果当前是全部楼栋状态，先清空选择
  if (selectedBuildingCodes.value.length === 0) {
    selectedBuildingCodes.value = [buildingCode]
  } else {
    const index = selectedBuildingCodes.value.indexOf(buildingCode)
    if (index > -1) {
      // 取消选择
      selectedBuildingCodes.value.splice(index, 1)
    } else {
      // 添加选择
      selectedBuildingCodes.value.push(buildingCode)
    }
  }

  // 自动查询对应楼栋的数据
  await onBuildingSelectionChange()
}

// 楼栋选择变化处理（多选版本）
const onBuildingSelectionChange = async () => {
  const buildingCodes = selectedBuildingCodes.value.length > 0 ? selectedBuildingCodes.value : null

  console.log('楼栋选择变化:', buildingCodes)

  // 查询对应楼栋的数据
  await queryDashboardDataUnified(buildingCodes, false)

  // 显示选择结果
  if (buildingCodes && buildingCodes.length > 0) {
    const selectedNames = buildingCodes.map(code => {
      const building = buildings.value.find(b => b.buildingCode === code)
      return building?.buildingName || code
    })

    if (selectedNames.length <= 2) {
      ElMessage.success(`已切换到 ${selectedNames.join('、')}`)
    } else {
      ElMessage.success(`已切换到 ${selectedNames.slice(0, 2).join('、')} 等${selectedNames.length}个楼栋`)
    }
  } else {
    ElMessage.success('已切换到全部楼栋')
  }
}

// 强制刷新数据（多选版本）
const forceRefreshData = async () => {
  const buildingCodes = selectedBuildingCodes.value.length > 0 ? selectedBuildingCodes.value : null

  let buildingName = '全部楼栋'
  if (buildingCodes && buildingCodes.length > 0) {
    const selectedNames = buildingCodes.map(code => {
      const building = buildings.value.find(b => b.buildingCode === code)
      return building?.buildingName || code
    }).join('、')
    buildingName = selectedNames
  }

  const message = `正在强制刷新${buildingName}数据...`
  
  console.log(message)
  ElMessage.info(message)

  // 使用统一接口进行强制刷新
  await queryDashboardDataUnified(buildingCodes, true)
}
const queryDashboardDataUnified = async (buildingCodes, forceRefresh) => {
  try {
    loading.value = true


    const response = await dormitoryAPI.queryDashboardData(buildingCodes, forceRefresh)

    if (response.success && response.data) {
      // 更新统计数据
      Object.assign(statistics, response.data)

      // 优化：不再查询记录数据，记录通过WebSocket实时推送
      // 记录数据由WebSocket维护，这里不做处理

      lastUpdateTime.value = new Date().toLocaleTimeString('zh-CN')
      connectionStatus.value = 'online'



      // 显示成功消息
      const message = buildingCodes && buildingCodes.length > 0
        ? `查询成功：${buildingCodes.length}个楼栋，共${response.data.totalPersons}人`
        : `查询成功：全部楼栋，共${response.data.totalPersons}人`
      ElMessage.success(message)

    } else {
      console.warn('统一查询响应异常:', response)
      connectionStatus.value = 'offline'
      ElMessage.error('查询失败: ' + (response.message || '未知错误'))
    }
  } catch (error) {
    console.error('统一查询失败:', error)
    connectionStatus.value = 'offline'
    ElMessage.error('网络错误，查询失败')
  } finally {
    loading.value = false
  }
}

// WebSocket连接管理
const initWebSocket = async () => {
  // 先检查后端服务是否可用
  try {
    const response = await fetch('/api/websocket/status', { timeout: 3000 })
    const result = await response.json()
    if (!result.success) {
      console.warn('后端WebSocket服务状态异常:', result)
    }
  } catch (error) {
    // 继续尝试连接，可能是接口不存在但WebSocket服务正常
  }

  // 从连接池获取连接
  const connection = wsPool.getConnection({
    onStatusChange: (status) => {
      websocketStatus.value = status
    },

    onOpen: (event) => {
      ElMessage.success('WebSocket连接成功')
    },

    onMessage: (data, event) => {
      handleWebSocketMessage(data)
    },

    onClose: (event) => {
      // 连接关闭处理
    },

    onError: (error) => {
      console.error('WebSocket连接错误:', error)
    }
  })

  wsSubscriberId = connection.subscriberId
}

// WebSocket消息处理已移至WebSocketManager

const handleWebSocketMessage = (data) => {
  if (data.type === 'new_record' && data.record) {
    // 收到新的进出记录
    addNewRecord(data.record)
  } else if (data.type === 'batch_records' && data.records) {
    // 收到批量记录（初始化时）
    setBatchRecords(data.records)
  } else if (data.type === 'ping') {
    // 心跳消息，静默处理
  } else if (data.type === 'welcome') {
    // 欢迎消息，静默处理
  } else {
    console.log('收到未知类型的WebSocket消息:', data.type)
  }
}

const addNewRecord = (newRecord) => {
  console.log('收到新的进出记录:', newRecord)

  // 楼栋筛选：如果选择了特定楼栋，只显示对应楼栋的记录
  if (selectedBuildingCodes.value.length > 0) {
    const recordBuildingName = newRecord.buildingName
    const selectedBuildingNames = selectedBuildingCodes.value.map(code => {
      const building = buildings.value.find(b => b.buildingCode === code)
      return building?.buildingName
    }).filter(name => name) // 过滤掉undefined

    if (!selectedBuildingNames.includes(recordBuildingName)) {
      console.log('记录不属于选中楼栋，跳过显示:', recordBuildingName, '当前选中:', selectedBuildingNames)
      return
    }
  }

  // 添加到记录列表开头
  allRecords.value.unshift(newRecord)

  // 限制记录数量
  if (allRecords.value.length > maxRecordsCount) {
    allRecords.value = allRecords.value.slice(0, maxRecordsCount)
  }

  // 显示通知（包含楼栋信息）
  const buildingInfo = newRecord.buildingName ? ` (${newRecord.buildingName})` : ''
  ElMessage.success(`新进出记录: ${newRecord.personName} ${newRecord.lastInOrOutDesc}${buildingInfo}`)
}

const setBatchRecords = (records) => {
  allRecords.value = records.slice(0, maxRecordsCount)
}

const disconnectWebSocket = () => {
  if (wsSubscriberId) {
    console.log(`移除WebSocket订阅: ${wsSubscriberId}`)
    wsPool.removeSubscriber(wsSubscriberId)
    wsSubscriberId = null
  }
}

// 更新当前时间
const updateCurrentTime = () => {
  const now = new Date()
  currentDate.value = now.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  })
  currentTime.value = now.toLocaleTimeString('zh-CN', {
    hour12: false
  })
}

// 启动时间更新
const startTimeUpdate = () => {
  updateCurrentTime()
  timeTimer = setInterval(updateCurrentTime, 1000)
}

// 停止时间更新
const stopTimeUpdate = () => {
  if (timeTimer) {
    clearInterval(timeTimer)
    timeTimer = null
  }
}

// 加载实时数据（统计信息+最新记录）- 基于Redis缓存的高性能接口
const loadRealtimeData = async () => {
  try {
    console.log('正在加载实时数据（基于Redis缓存）...', { selectedBuildingCodes: selectedBuildingCodes.value })

    // 如果选择了特定楼栋，使用分别加载的方式（因为实时接口暂不支持楼栋筛选）
    if (selectedBuildingCodes.value.length > 0) {
      return false // 强制使用分别加载方式
    }
    
    const response = await dormitoryAPI.getRealtimeData(30)
    
    if (response.success && response.data) {
      // 更新统计数据
      Object.assign(statistics, response.data.statistics)
      
      // 更新记录数据
      allRecords.value = response.data.records || []
      
      console.log('实时数据加载成功:', {
        statistics: response.data.statistics,
        recordCount: response.data.recordCount,
        queryMethod: response.data.statistics?.queryMethod,
        dataSource: response.data.dataSource,
        queryDuration: response.data.queryDuration + 'ms'
      })
      
      connectionStatus.value = 'online'
      return true
    } else {
      console.warn('实时数据响应异常:', response)
      connectionStatus.value = 'offline'
      return false
    }
  } catch (error) {
    console.error('加载实时数据失败:', error)
    connectionStatus.value = 'offline'
    return false
  }
}

// 加载统计数据（独立接口，支持楼栋筛选）
const loadStatistics = async () => {
  try {
    console.log('正在加载统计数据（基于Redis缓存）...', { selectedBuildingCodes: selectedBuildingCodes.value })

    // 根据是否选择楼栋决定调用哪个接口
    const response = selectedBuildingCodes.value.length > 0
      ? await dormitoryAPI.getStatisticsByBuildings(selectedBuildingCodes.value)
      : await dormitoryAPI.getStatistics()
    
    if (response.success && response.data) {
      Object.assign(statistics, response.data)
      console.log('统计数据加载成功:', statistics)
      connectionStatus.value = 'online'
    } else {
      console.warn('统计数据响应异常:', response)
      connectionStatus.value = 'offline'
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
    connectionStatus.value = 'offline'
  }
}

// 加载进出记录（独立接口）
const loadRecords = async () => {
  try {
    console.log('正在加载进出记录（基于Redis缓存）...')
    const response = await dormitoryAPI.getRecords(30)

    if (response.success && response.data) {
      allRecords.value = response.data
      console.log('进出记录加载成功，共', response.data.length, '条, 数据源:', response.dataSource)
    } else {
      console.warn('进出记录响应异常:', response)
    }

  } catch (error) {
    console.error('加载进出记录失败:', error)
  }
}

// 加载所有数据（基于Redis缓存）
const loadAllData = async () => {
  try {
    loading.value = true
    
    // 优先使用高性能实时接口（一次请求获取所有数据）
    const realtimeSuccess = await loadRealtimeData()
    
    // 如果实时接口失败，分别加载统计和记录数据
    if (!realtimeSuccess) {
      console.warn('实时接口失败，分别加载统计和记录数据')
      await Promise.all([
        loadStatistics(),
        loadRecords()
      ])
    }
    
    lastUpdateTime.value = new Date().toLocaleTimeString('zh-CN')
    console.log('所有数据加载完成（基于Redis缓存）')
    
  } catch (error) {
    console.error('加载数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 初始加载数据（优化版本：先加载数据，再连接WebSocket）
const initializeData = async () => {
  try {
    // 先完成数据加载
    await loadBuildings()
    await queryDashboardDataUnified(null, false)

    // 稍微延迟一下，确保没有其他连接冲突
    await new Promise(resolve => setTimeout(resolve, 500))
    // 数据加载完成后再连接WebSocket，避免资源竞争
    await initWebSocket()
  } catch (error) {
    console.error('数据初始化失败:', error)
    // 即使数据加载失败，也尝试连接WebSocket
    initWebSocket()
  }
}

// 大屏模式切换
const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value

  if (isFullscreen.value) {
    // 进入全屏模式
    try {
      if (document.documentElement.requestFullscreen) {
        document.documentElement.requestFullscreen()
      } else if (document.documentElement.webkitRequestFullscreen) {
        document.documentElement.webkitRequestFullscreen()
      } else if (document.documentElement.msRequestFullscreen) {
        document.documentElement.msRequestFullscreen()
      }

      // 显示提示
      ElMessage({
        message: '已进入大屏模式，按ESC键或点击❌按钮退出',
        type: 'success',
        duration: 4000,
        showClose: true
      })

      // 自动隐藏鼠标指针（5秒后）
      setTimeout(() => {
        if (isFullscreen.value) {
          document.body.style.cursor = 'none'
        }
      }, 5000)

    } catch (error) {
      console.error('进入全屏模式失败:', error)
      isFullscreen.value = false
      ElMessage.error('进入全屏模式失败')
    }
  } else {
    // 退出全屏模式
    try {
      // 恢复鼠标指针
      document.body.style.cursor = 'default'

      if (document.exitFullscreen) {
        document.exitFullscreen()
      } else if (document.webkitExitFullscreen) {
        document.webkitExitFullscreen()
      } else if (document.msExitFullscreen) {
        document.msExitFullscreen()
      }

      ElMessage.info('已退出大屏模式')
    } catch (error) {
      console.error('退出全屏模式失败:', error)
      ElMessage.error('退出全屏模式失败')
    }
  }
}

// 监听全屏状态变化
const handleFullscreenChange = () => {
  const isCurrentlyFullscreen = !!(
    document.fullscreenElement ||
    document.webkitFullscreenElement ||
    document.msFullscreenElement
  )

  if (!isCurrentlyFullscreen && isFullscreen.value) {
    isFullscreen.value = false
    // 恢复鼠标指针
    document.body.style.cursor = 'default'
    clearTimeout(mouseHideTimer)
  }
}

// 鼠标隐藏定时器
let mouseHideTimer = null

// 鼠标移动处理
const handleMouseMove = () => {
  if (isFullscreen.value) {
    // 显示鼠标指针
    document.body.style.cursor = 'default'

    // 清除之前的定时器
    clearTimeout(mouseHideTimer)

    // 5秒后隐藏鼠标指针
    mouseHideTimer = setTimeout(() => {
      if (isFullscreen.value) {
        document.body.style.cursor = 'none'
      }
    }, 5000)
  }
}

// 键盘事件处理
const handleKeyDown = (event) => {
  // ESC键退出大屏模式
  if (event.key === 'Escape' && isFullscreen.value) {
    event.preventDefault()
    toggleFullscreen()
  }

  // F11键切换大屏模式
  if (event.key === 'F11') {
    event.preventDefault()
    toggleFullscreen()
  }
}



// 组件挂载
onMounted(() => {
  // 启动时间更新
  startTimeUpdate()

  // 初始化数据
  initializeData()

  // 监听全屏状态变化
  document.addEventListener('fullscreenchange', handleFullscreenChange)
  document.addEventListener('webkitfullscreenchange', handleFullscreenChange)
  document.addEventListener('msfullscreenchange', handleFullscreenChange)

  // 监听鼠标移动（用于大屏模式下的鼠标隐藏）
  document.addEventListener('mousemove', handleMouseMove)

  // 监听键盘事件（ESC退出大屏，F11切换大屏）
  document.addEventListener('keydown', handleKeyDown)
})

// 组件卸载
onUnmounted(() => {
  stopTimeUpdate()
  disconnectWebSocket()

  // 移除全屏监听器
  document.removeEventListener('fullscreenchange', handleFullscreenChange)
  document.removeEventListener('webkitfullscreenchange', handleFullscreenChange)
  document.removeEventListener('msfullscreenchange', handleFullscreenChange)

  // 移除鼠标移动监听器
  document.removeEventListener('mousemove', handleMouseMove)

  // 移除键盘事件监听器
  document.removeEventListener('keydown', handleKeyDown)

  // 清理定时器
  clearTimeout(mouseHideTimer)

  // 恢复鼠标指针
  document.body.style.cursor = 'default'
})
</script>

<style scoped lang="scss">
.dashboard-app {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, $bg-primary 0%, $bg-secondary 100%);
  overflow: hidden;
}

.dashboard-header {
  padding: $spacing-md $spacing-lg; /* 减小头部高度 */
  border-bottom: 1px solid $border-color;
  background: rgba(255, 255, 255, 0.02);
  backdrop-filter: blur(10px);
  
  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .title-section {
    .main-title {
      font-size: $font-size-title;
      font-weight: 700;
      margin: 0;
      display: flex;
      align-items: center;
      gap: $spacing-md;
      text-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
    }
    
    .subtitle {
      font-size: $font-size-lg;
      color: $text-secondary;
      margin-top: $spacing-sm;
      font-weight: 400;
    }
  }
  
  .status-section {
    display: flex;
    align-items: center;
    gap: $spacing-lg;
    text-align: right;
    flex-wrap: wrap;
    
    .connection-status,
    .websocket-status {
      display: flex;
      align-items: center;
      gap: $spacing-sm;
      font-size: $font-size-md;
      color: $text-secondary;

      .status-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;

        &.online,
        &.connected {
          background-color: $success-color;
          box-shadow: 0 0 8px $success-color;
        }

        &.offline,
        &.disconnected {
          background-color: $danger-color;
          box-shadow: 0 0 8px $danger-color;
        }

        &.connecting {
          background-color: $warning-color;
          box-shadow: 0 0 8px $warning-color;
          animation: pulse 1.5s infinite;
        }

        &.error {
          background-color: $danger-color;
          box-shadow: 0 0 8px $danger-color;
          animation: blink 1s infinite;
        }
      }
    }

    .websocket-status {
      font-size: $font-size-sm;
    }

    .menu-buttons {
      display: flex;
      align-items: center;
      gap: 8px;

      .el-button {
        border: 1px solid rgba(255, 255, 255, 0.3);
        background: rgba(255, 255, 255, 0.1);
        color: white;

        &:hover {
          background: rgba(255, 255, 255, 0.2);
          border-color: rgba(255, 255, 255, 0.5);
        }
      }
    }

    .building-display {
      display: flex;
      align-items: center;
      gap: 12px;

      .building-text {
        color: rgba(255, 255, 255, 0.9);
        font-size: 14px;
        font-weight: 500;
      }

      .fullscreen-indicator {
        display: flex;
        align-items: center;
        gap: 4px;
        color: rgba(64, 158, 255, 0.9);
        font-size: 12px;
        font-weight: 500;
        background: rgba(64, 158, 255, 0.1);
        padding: 4px 8px;
        border-radius: 12px;
        border: 1px solid rgba(64, 158, 255, 0.3);

        .el-icon {
          font-size: 14px;
        }
      }

      .small-exit-btn {
        background: rgba(245, 108, 108, 0.8) !important;
        border: 1px solid rgba(255, 255, 255, 0.6) !important;
        color: white !important;

        &:hover {
          background: rgba(245, 108, 108, 1) !important;
          border-color: white !important;
          transform: scale(1.1);
        }

        &:active {
          transform: scale(0.95);
        }
      }
    }


    
    // 紧凑型楼栋选择器样式
    .building-selector-compact {
      display: flex;
      align-items: center;
      gap: $spacing-md;

      .selector-info {
        display: flex;
        align-items: center;
        gap: $spacing-sm;

        .selector-label {
          font-size: $font-size-md;
          color: $text-secondary;
          white-space: nowrap;
          font-weight: 500;
        }

        .selected-info {
          font-size: $font-size-md;
          color: $text-primary;
          font-weight: 600;
          max-width: 200px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .selector-actions {
        .select-btn {
          height: 36px;
          padding: 0 16px;
          font-size: $font-size-sm;
          border-radius: 6px;
          background: rgba(255, 255, 255, 0.1);
          border: 1px solid rgba(255, 255, 255, 0.2);
          color: $text-primary;

          &:hover {
            background: rgba(255, 255, 255, 0.15);
            border-color: $primary-color;
            transform: translateY(-1px);
          }

          .el-icon {
            margin-left: 4px;
            transition: transform 0.3s ease;
          }
        }
      }
    }

    .action-buttons {
      display: flex;
      gap: $spacing-md;
      align-items: center;

      .el-button.is-circle {
        width: 48px;
        height: 48px;
        
        &:hover {
          transform: scale(1.05);
          transition: transform 0.2s ease;
        }
      }
    }
  }
}

.dashboard-main {
  flex: 1;
  display: grid;
  grid-template-rows: 55% 45%; /* 调整：给统计面板更多空间显示图表 */
  gap: $spacing-md;
  padding: $spacing-lg;
  overflow: hidden;
  min-height: 0; /* 重要：允许grid子项收缩 */
}

.statistics-section,
.records-section {
  min-height: 0; // 重要：允许grid子项收缩
}

.statistics-section {
  overflow: hidden; // 防止内容溢出
}

.dashboard-footer {
  padding: $spacing-sm $spacing-lg; /* 减小底部高度 */
  border-top: 1px solid $border-color;
  background: rgba(255, 255, 255, 0.02);
  
  .footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: $font-size-sm;
    color: $text-muted;
  }
  
  .system-info {
    display: flex;
    gap: $spacing-lg;
  }
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(12, 20, 38, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(5px);
  
  .loading-content {
    text-align: center;
    color: $text-primary;
    
    .loading-icon {
      font-size: 48px;
      color: $primary-color;
      animation: spin 1s linear infinite;
      margin-bottom: $spacing-lg;
    }
    
    p {
      font-size: $font-size-lg;
      margin: 0;
    }
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0.3;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .dashboard-header {
    padding: $spacing-md $spacing-lg;
    
    .main-title {
      font-size: $font-size-xl;
    }
  }
  
  .dashboard-main {
    padding: $spacing-lg;
    gap: $spacing-lg;
  }
}

@media (max-height: 800px) {
  .dashboard-main {
    grid-template-rows: auto 1fr;
  }
  
  .statistics-section {
    // 在小屏幕上进一步限制统计面板高度
    max-height: 50vh;
  }
}

// 楼栋选择弹出框样式（全局）
:deep(.building-selector-popover) {
  background: rgba(30, 41, 59, 0.95) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(10px);

  .building-options {
    .option-item {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 12px;
      cursor: pointer;
      border-radius: 4px;
      transition: background-color 0.2s ease;

      &:hover {
        background: rgba(255, 255, 255, 0.1);
      }

      span {
        color: #e2e8f0;
        font-size: 14px;
      }

      .el-checkbox {
        pointer-events: auto;

        :deep(.el-checkbox__input) {
          .el-checkbox__inner {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.3);

            &:hover {
              border-color: #409eff;
            }
          }

          &.is-checked .el-checkbox__inner {
            background: #409eff;
            border-color: #409eff;
          }
        }
      }
    }
  }
}

// 大屏模式样式
.dashboard-app {
  &.fullscreen {
    .menu-buttons {
      display: none !important;
    }

    .dashboard-header {
      padding: $spacing-sm $spacing-lg;

      .title-section h1 {
        font-size: 2.5rem;
      }
    }

    .dashboard-main {
      padding: $spacing-md;
    }
  }
}

</style>

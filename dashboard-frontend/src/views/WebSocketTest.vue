<template>
  <div class="websocket-test-page">
    <div class="test-header">
      <h1>WebSocket连接测试中心</h1>
      <p>测试和监控WebSocket连接状态</p>
    </div>

    <div class="test-content">
      <!-- 连接状态面板 -->
      <div class="status-panel">
        <h2>连接状态</h2>
        <WebSocketStatus 
          :show-details="true"
          :show-actions="true"
          @reconnect="handleReconnect"
          @force-reset="handleForceReset"
          @diagnose="handleDiagnose"
        />
      </div>

      <!-- 连接控制面板 -->
      <div class="control-panel">
        <h2>连接控制</h2>
        <div class="control-buttons">
          <el-button 
            type="primary" 
            @click="connectWebSocket"
            :disabled="isConnecting"
          >
            {{ isConnecting ? '连接中...' : '连接' }}
          </el-button>
          
          <el-button 
            type="danger" 
            @click="disconnectWebSocket"
            :disabled="!isConnected"
          >
            断开连接
          </el-button>
          
          <el-button 
            type="success" 
            @click="sendTestMessage"
            :disabled="!isConnected"
          >
            发送测试消息
          </el-button>
          
          <el-button 
            type="info" 
            @click="triggerBackendPush"
            :disabled="!isConnected"
          >
            触发后端推送
          </el-button>
        </div>
      </div>

      <!-- 消息日志面板 -->
      <div class="log-panel">
        <div class="log-header">
          <h2>消息日志</h2>
          <div class="log-controls">
            <el-button size="small" @click="clearLogs">清空日志</el-button>
            <el-button size="small" @click="exportLogs">导出日志</el-button>
            <el-switch 
              v-model="autoScroll" 
              active-text="自动滚动"
              size="small"
            />
          </div>
        </div>
        
        <div class="log-content" ref="logContainer">
          <div 
            v-for="(log, index) in logs" 
            :key="index"
            class="log-entry"
            :class="log.type"
          >
            <span class="log-time">{{ log.time }}</span>
            <span class="log-type">{{ log.type.toUpperCase() }}</span>
            <span class="log-message">{{ log.message }}</span>
            <pre v-if="log.data" class="log-data">{{ JSON.stringify(log.data, null, 2) }}</pre>
          </div>
        </div>
      </div>

      <!-- 性能监控面板 -->
      <div class="metrics-panel">
        <h2>性能监控</h2>
        <div class="metrics-grid">
          <div class="metric-card">
            <div class="metric-title">连接延迟</div>
            <div class="metric-value">{{ metrics.latency }}ms</div>
          </div>
          
          <div class="metric-card">
            <div class="metric-title">消息数量</div>
            <div class="metric-value">{{ metrics.messageCount }}</div>
          </div>
          
          <div class="metric-card">
            <div class="metric-title">错误次数</div>
            <div class="metric-value">{{ metrics.errorCount }}</div>
          </div>
          
          <div class="metric-card">
            <div class="metric-title">运行时间</div>
            <div class="metric-value">{{ formatUptime(metrics.uptime) }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, nextTick, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import WebSocketStatus from '../components/WebSocketStatus.vue'
import wsPool from '../utils/websocket-pool.js'

// State
const logs = ref([])
const autoScroll = ref(true)
const logContainer = ref(null)
let subscriberId = null

const metrics = reactive({
  latency: 0,
  messageCount: 0,
  errorCount: 0,
  uptime: 0,
  startTime: Date.now()
})

// Computed
const isConnected = computed(() => wsPool.getStatus() === 'connected')
const isConnecting = computed(() => wsPool.getStatus() === 'connecting')

// Methods
const addLog = (type, message, data = null) => {
  const log = {
    time: new Date().toLocaleTimeString(),
    type,
    message,
    data
  }
  
  logs.value.push(log)
  
  // 限制日志数量
  if (logs.value.length > 1000) {
    logs.value.splice(0, 100)
  }
  
  // 自动滚动
  if (autoScroll.value) {
    nextTick(() => {
      if (logContainer.value) {
        logContainer.value.scrollTop = logContainer.value.scrollHeight
      }
    })
  }
}

const connectWebSocket = () => {
  addLog('info', '开始连接WebSocket...')
  
  const startTime = performance.now()
  
  const connection = wsPool.getConnection({
    onOpen: (event) => {
      const latency = performance.now() - startTime
      metrics.latency = Math.round(latency)
      addLog('success', `WebSocket连接成功，延迟: ${metrics.latency}ms`)
    },
    
    onMessage: (data, event) => {
      metrics.messageCount++
      addLog('message', '收到消息', data)
    },
    
    onClose: (event) => {
      addLog('warning', `连接关闭: code=${event.code}, reason=${event.reason}`)
    },
    
    onError: (error) => {
      metrics.errorCount++
      addLog('error', '连接错误', error)
    },
    
    onStatusChange: (status) => {
      addLog('info', `状态变化: ${status}`)
    }
  })
  
  subscriberId = connection.subscriberId
  addLog('info', `订阅ID: ${subscriberId}`)
}

const disconnectWebSocket = () => {
  if (subscriberId) {
    wsPool.removeSubscriber(subscriberId)
    subscriberId = null
    addLog('info', 'WebSocket连接已断开')
  }
}

const sendTestMessage = () => {
  const message = {
    type: 'test',
    data: 'Hello from WebSocket test page',
    timestamp: Date.now()
  }
  
  const success = wsPool.send(message)
  if (success) {
    addLog('info', '发送测试消息', message)
  } else {
    addLog('error', '发送消息失败：连接未建立')
  }
}

const triggerBackendPush = async () => {
  try {
    addLog('info', '触发后端推送...')
    const response = await fetch('/api/websocket/test/push', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' }
    })
    
    if (response.ok) {
      const result = await response.json()
      addLog('success', '后端推送触发成功', result)
    } else {
      addLog('error', '后端推送触发失败')
    }
  } catch (error) {
    addLog('error', '触发后端推送时出错', error.message)
  }
}

const clearLogs = () => {
  logs.value = []
  addLog('info', '日志已清空')
}

const exportLogs = () => {
  const logText = logs.value.map(log => 
    `[${log.time}] ${log.type.toUpperCase()}: ${log.message}${log.data ? '\n' + JSON.stringify(log.data, null, 2) : ''}`
  ).join('\n')
  
  const blob = new Blob([logText], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `websocket-logs-${new Date().toISOString().slice(0, 19)}.txt`
  a.click()
  URL.revokeObjectURL(url)
  
  addLog('info', '日志已导出')
}

const formatUptime = (uptime) => {
  const seconds = Math.floor(uptime / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  
  if (hours > 0) {
    return `${hours}h ${minutes % 60}m`
  } else if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`
  } else {
    return `${seconds}s`
  }
}

const handleReconnect = () => {
  addLog('info', '手动重连WebSocket...')
  wsPool.reconnect()
}

const handleForceReset = () => {
  addLog('warning', '强制重置WebSocket连接...')
  wsPool.forceReset()
}

const handleDiagnose = () => {
  addLog('info', '开始WebSocket诊断...')
  wsPool.debug()
  addLog('info', '诊断信息已输出到控制台')
}

// 更新运行时间
const updateMetrics = () => {
  metrics.uptime = Date.now() - metrics.startTime
}

let metricsTimer = null

// Lifecycle
onMounted(() => {
  addLog('info', 'WebSocket测试页面已加载')
  
  // 启动指标更新
  metricsTimer = setInterval(updateMetrics, 1000)
})

onUnmounted(() => {
  // 清理连接
  if (subscriberId) {
    wsPool.removeSubscriber(subscriberId)
  }
  
  // 清理定时器
  if (metricsTimer) {
    clearInterval(metricsTimer)
  }
})
</script>

<style scoped>
.websocket-test-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  background: #f5f5f5;
  min-height: 100vh;
}

.test-header {
  text-align: center;
  margin-bottom: 30px;
}

.test-header h1 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.test-header p {
  color: #7f8c8d;
  font-size: 16px;
}

.test-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.status-panel,
.control-panel,
.log-panel,
.metrics-panel {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.log-panel {
  grid-column: 1 / -1;
}

.control-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.log-controls {
  display: flex;
  gap: 10px;
  align-items: center;
}

.log-content {
  height: 400px;
  overflow-y: auto;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 10px;
  background: #fafafa;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.log-entry {
  margin-bottom: 8px;
  padding: 4px;
  border-radius: 3px;
}

.log-entry.success {
  background: #f0f9ff;
  border-left: 3px solid #10b981;
}

.log-entry.error {
  background: #fef2f2;
  border-left: 3px solid #ef4444;
}

.log-entry.warning {
  background: #fffbeb;
  border-left: 3px solid #f59e0b;
}

.log-entry.message {
  background: #f0f9ff;
  border-left: 3px solid #3b82f6;
}

.log-time {
  color: #6b7280;
  margin-right: 8px;
}

.log-type {
  font-weight: bold;
  margin-right: 8px;
  min-width: 60px;
  display: inline-block;
}

.log-data {
  margin-top: 5px;
  padding: 5px;
  background: #f3f4f6;
  border-radius: 3px;
  font-size: 11px;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
}

.metric-card {
  text-align: center;
  padding: 15px;
  background: #f8fafc;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

.metric-title {
  font-size: 12px;
  color: #64748b;
  margin-bottom: 5px;
}

.metric-value {
  font-size: 24px;
  font-weight: bold;
  color: #1e293b;
}
</style>

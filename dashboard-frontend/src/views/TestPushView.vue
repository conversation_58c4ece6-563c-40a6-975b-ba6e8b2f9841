<template>
  <div class="test-push-view">
    <div class="container">
      <div class="header">
        <h1>🧪 模拟推送测试页面</h1>
        <p>用于测试进出记录推送功能</p>
      </div>

      <div class="form-section">
        <el-form :model="form" label-width="120px" size="large">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="选择学生">
                <el-select 
                  v-model="form.selectedStudent" 
                  placeholder="请选择学生"
                  style="width: 100%"
                  @change="onStudentChange"
                >
                  <el-option
                    v-for="student in students"
                    :key="student.personCode"
                    :label="`${student.personName} (${student.personCode})`"
                    :value="student.personCode"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            
            <el-col :span="12">
              <el-form-item label="进出方向">
                <el-select v-model="form.deviceDirection" placeholder="请选择方向" style="width: 100%">
                  <el-option label="进入宿舍 (1)" :value="1" />
                  <el-option label="离开宿舍 (2)" :value="2" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="设备名称">
                <el-input v-model="form.deviceName" placeholder="如: 9-10栋5号门出" />
              </el-form-item>
            </el-col>
            
            <el-col :span="12">
              <el-form-item label="设备ID">
                <el-input v-model="form.deviceId" placeholder="如: ET-5A1B-HM-ZD-R-172-16-102-81" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="设备IP">
                <el-input v-model="form.deviceIp" placeholder="如: *************" />
              </el-form-item>
            </el-col>
            
            <el-col :span="12">
              <el-form-item label="当前时间">
                <el-input :value="currentTime" readonly />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item>
            <el-button 
              type="primary" 
              size="large" 
              @click="pushRecord"
              :loading="loading"
              :disabled="!canPush"
            >
              {{ loading ? '推送中...' : '🚀 推送记录' }}
            </el-button>
            
            <el-button size="large" @click="resetForm">
              🔄 重置表单
            </el-button>
            
            <el-button size="large" @click="loadStudents">
              📋 刷新学生列表
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- Redis缓存查询区域 -->
      <div class="cache-section">
        <h3>🗄️ Redis缓存查询</h3>
        <el-form :model="cacheForm" label-width="120px" size="large">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="学生编码">
                <el-input
                  v-model="cacheForm.personCode"
                  placeholder="留空查询所有学生缓存"
                  clearable
                />
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item>
                <el-button
                  type="info"
                  size="large"
                  @click="queryRedisCache"
                  :loading="cacheLoading"
                >
                  {{ cacheLoading ? '查询中...' : '🔍 查询缓存' }}
                </el-button>

                <el-button
                  type="warning"
                  size="large"
                  @click="clearRedisCache"
                  :loading="clearLoading"
                >
                  {{ clearLoading ? '清除中...' : '🗑️ 清除缓存' }}
                </el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- 缓存查询结果 -->
      <div class="cache-result-section" v-if="cacheResult">
        <h3>🗄️ 缓存查询结果</h3>
        <el-alert
          :title="cacheResult.success ? '查询成功' : '查询失败'"
          :type="cacheResult.success ? 'success' : 'error'"
          :description="cacheResult.message"
          show-icon
          :closable="false"
        />

        <div class="cache-details" v-if="cacheResult.success && cacheResult.data">
          <!-- 单个学生缓存 -->
          <div v-if="cacheResult.data.personCode" class="single-cache">
            <h4>👤 学生缓存详情</h4>
            <el-descriptions :column="2" border>
              <el-descriptions-item label="学生编码">{{ cacheResult.data.personCode }}</el-descriptions-item>
              <el-descriptions-item label="缓存状态">
                <el-tag :type="cacheResult.data.exists ? 'success' : 'danger'">
                  {{ cacheResult.data.exists ? '存在' : '不存在' }}
                </el-tag>
              </el-descriptions-item>
              <template v-if="cacheResult.data.exists">
                <el-descriptions-item label="姓名">{{ cacheResult.data.personName }}</el-descriptions-item>
                <el-descriptions-item label="最后状态">
                  <el-tag :type="cacheResult.data.lastInOrOut === 1 ? 'success' : 'warning'">
                    {{ cacheResult.data.lastInOrOutDesc }}
                  </el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="最后通行时间">{{ cacheResult.data.lastPassTime }}</el-descriptions-item>
                <el-descriptions-item label="最后设备">{{ cacheResult.data.lastDeviceName }}</el-descriptions-item>
                <el-descriptions-item label="缓存TTL">{{ formatTTL(cacheResult.data.ttlSeconds) }}</el-descriptions-item>
                <el-descriptions-item label="缓存Key">{{ cacheResult.data.cacheKey }}</el-descriptions-item>
              </template>
            </el-descriptions>
          </div>

          <!-- 所有学生缓存 -->
          <div v-else class="all-cache">
            <h4>👥 所有学生缓存 ({{ cacheResult.data.totalCount }}条)</h4>

            <!-- 统计信息 -->
            <div v-if="cacheResult.data.statistics" class="cache-stats">
              <el-row :gutter="20">
                <el-col :span="6">
                  <el-statistic title="在寝人数" :value="cacheResult.data.statistics.inDormitoryCount" />
                </el-col>
                <el-col :span="6">
                  <el-statistic title="外出人数" :value="cacheResult.data.statistics.outDormitoryCount" />
                </el-col>
                <el-col :span="6">
                  <el-statistic title="无记录人数" :value="cacheResult.data.statistics.noRecordCount" />
                </el-col>
                <el-col :span="6">
                  <el-statistic title="缓存总数" :value="cacheResult.data.statistics.studentStatusCount" />
                </el-col>
              </el-row>
            </div>

            <!-- 学生列表 -->
            <el-table :data="cacheResult.data.students" style="width: 100%" max-height="400">
              <el-table-column prop="personCode" label="学生编码" width="120" />
              <el-table-column prop="personName" label="姓名" width="100" />
              <el-table-column label="状态" width="100">
                <template #default="scope">
                  <el-tag :type="scope.row.lastInOrOut === 1 ? 'success' : 'warning'">
                    {{ scope.row.lastInOrOutDesc }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="lastPassTime" label="最后通行时间" width="180" />
              <el-table-column prop="lastDeviceName" label="最后设备" />
              <el-table-column label="缓存TTL" width="120">
                <template #default="scope">
                  {{ formatTTL(scope.row.ttlSeconds) }}
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>

      <!-- 推送结果显示区域 -->
      <div class="result-section" v-if="lastResult">
        <h3>📊 推送结果</h3>
        <el-alert
          :title="lastResult.success ? '推送成功' : '推送失败'"
          :type="lastResult.success ? 'success' : 'error'"
          :description="lastResult.message"
          show-icon
          :closable="false"
        />

        <div class="result-details" v-if="lastResult.data">
          <h4>📝 推送数据详情</h4>
          <pre>{{ JSON.stringify(lastResult.data, null, 2) }}</pre>
        </div>
      </div>

      <!-- 使用说明 -->
      <div class="help-section">
        <h3>📖 使用说明</h3>
        <ul>
          <li>选择要测试的学生（需要先在数据库中存在）</li>
          <li>选择进出方向：1=进入宿舍，2=离开宿舍</li>
          <li>填写设备信息（可使用默认值）</li>
          <li>点击推送按钮发送模拟消息</li>
          <li>查看推送结果和处理状态</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { testAPI } from '../api/test'

// 响应式数据
const form = reactive({
  selectedStudent: '',
  deviceDirection: 2,
  deviceName: '9-10栋5号门出',
  deviceId: 'ET-5A1B-HM-ZD-R-172-16-102-81',
  deviceIp: '*************'
})

const cacheForm = reactive({
  personCode: ''
})

const students = ref([])
const loading = ref(false)
const cacheLoading = ref(false)
const clearLoading = ref(false)
const lastResult = ref(null)
const cacheResult = ref(null)
const currentTime = ref('')

// 计算属性
const canPush = computed(() => {
  return form.selectedStudent && form.deviceDirection && form.deviceName && form.deviceId
})

const selectedStudentInfo = computed(() => {
  return students.value.find(s => s.personCode === form.selectedStudent)
})

// 更新当前时间
const updateCurrentTime = () => {
  currentTime.value = new Date().toLocaleString('zh-CN')
}

// 学生选择变化
const onStudentChange = () => {
  // 可以根据选择的学生自动填充一些信息
}

// 加载学生列表
const loadStudents = async () => {
  try {
    const response = await testAPI.getStudents()
    if (response.success) {
      students.value = response.data
      ElMessage.success('学生列表加载成功')
    } else {
      ElMessage.error('加载学生列表失败: ' + response.message)
    }
  } catch (error) {
    console.error('加载学生列表失败:', error)
    ElMessage.error('网络错误，请稍后重试')
  }
}

// 推送记录
const pushRecord = async () => {
  if (!canPush.value) {
    ElMessage.warning('请填写完整信息')
    return
  }

  loading.value = true
  lastResult.value = null

  try {
    const studentInfo = selectedStudentInfo.value
    const requestData = {
      personCode: form.selectedStudent,
      personName: studentInfo?.personName || '未知',
      deviceDirection: form.deviceDirection,
      deviceName: form.deviceName,
      deviceId: form.deviceId,
      deviceIp: form.deviceIp
    }

    const response = await testAPI.pushRecord(requestData)
    lastResult.value = response

    if (response.success) {
      ElMessage.success('推送成功！')
    } else {
      ElMessage.error('推送失败: ' + response.message)
    }

  } catch (error) {
    console.error('推送失败:', error)
    lastResult.value = {
      success: false,
      message: '网络错误: ' + error.message
    }
    ElMessage.error('网络错误，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 查询Redis缓存
const queryRedisCache = async () => {
  cacheLoading.value = true
  cacheResult.value = null

  try {
    const response = await testAPI.getRedisCache(cacheForm.personCode || null)
    cacheResult.value = response

    if (response.success) {
      ElMessage.success('缓存查询成功！')
    } else {
      ElMessage.error('缓存查询失败: ' + response.message)
    }

  } catch (error) {
    console.error('缓存查询失败:', error)
    cacheResult.value = {
      success: false,
      message: '网络错误: ' + error.message
    }
    ElMessage.error('网络错误，请稍后重试')
  } finally {
    cacheLoading.value = false
  }
}

// 清除Redis缓存
const clearRedisCache = async () => {
  clearLoading.value = true

  try {
    const response = await testAPI.clearRedisCache(cacheForm.personCode || null)

    if (response.success) {
      ElMessage.success('缓存清除成功！')
      // 清除成功后自动刷新查询结果
      if (cacheResult.value) {
        await queryRedisCache()
      }
    } else {
      ElMessage.error('缓存清除失败: ' + response.message)
    }

  } catch (error) {
    console.error('缓存清除失败:', error)
    ElMessage.error('网络错误，请稍后重试')
  } finally {
    clearLoading.value = false
  }
}

// 格式化TTL显示
const formatTTL = (ttlSeconds) => {
  if (!ttlSeconds || ttlSeconds <= 0) {
    return '已过期'
  }

  const hours = Math.floor(ttlSeconds / 3600)
  const minutes = Math.floor((ttlSeconds % 3600) / 60)
  const seconds = ttlSeconds % 60

  if (hours > 0) {
    return `${hours}h ${minutes}m`
  } else if (minutes > 0) {
    return `${minutes}m ${seconds}s`
  } else {
    return `${seconds}s`
  }
}

// 重置表单
const resetForm = () => {
  form.selectedStudent = ''
  form.deviceDirection = 2
  form.deviceName = '9-10栋5号门出'
  form.deviceId = 'ET-5A1B-HM-ZD-R-172-16-102-81'
  form.deviceIp = '*************'
  lastResult.value = null
  cacheResult.value = null
  cacheForm.personCode = ''
  ElMessage.info('表单已重置')
}

// 组件挂载
onMounted(() => {
  loadStudents()
  updateCurrentTime()
  // 每秒更新时间
  setInterval(updateCurrentTime, 1000)
})
</script>

<style lang="scss" scoped>
.test-push-view {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.header {
  text-align: center;
  margin-bottom: 40px;
  
  h1 {
    color: #2c3e50;
    margin-bottom: 10px;
    font-size: 2.5em;
  }
  
  p {
    color: #7f8c8d;
    font-size: 1.2em;
  }
}

.form-section {
  background: #f8f9fa;
  padding: 30px;
  border-radius: 12px;
  margin-bottom: 30px;
}

.cache-section {
  background: #f0f8ff;
  padding: 30px;
  border-radius: 12px;
  margin-bottom: 30px;
  border-left: 4px solid #409eff;

  h3 {
    color: #409eff;
    margin-bottom: 20px;
  }
}

.cache-result-section {
  background: #f9f9f9;
  padding: 20px;
  border-radius: 12px;
  margin-bottom: 30px;

  h3 {
    color: #2c3e50;
    margin-bottom: 15px;
  }

  .cache-details {
    margin-top: 20px;

    .single-cache, .all-cache {
      h4 {
        color: #34495e;
        margin-bottom: 15px;
      }
    }

    .cache-stats {
      background: #fff;
      padding: 20px;
      border-radius: 8px;
      margin-bottom: 20px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
  }
}

.result-section {
  background: #f1f3f4;
  padding: 20px;
  border-radius: 12px;
  margin-bottom: 30px;
  
  h3 {
    color: #2c3e50;
    margin-bottom: 15px;
  }
  
  .result-details {
    margin-top: 20px;
    
    h4 {
      color: #34495e;
      margin-bottom: 10px;
    }
    
    pre {
      background: #2c3e50;
      color: #ecf0f1;
      padding: 15px;
      border-radius: 8px;
      overflow-x: auto;
      font-size: 12px;
      line-height: 1.4;
    }
  }
}

.help-section {
  background: #e8f4fd;
  padding: 20px;
  border-radius: 12px;
  border-left: 4px solid #3498db;
  
  h3 {
    color: #2980b9;
    margin-bottom: 15px;
  }
  
  ul {
    color: #34495e;
    
    li {
      margin-bottom: 8px;
      line-height: 1.6;
    }
  }
}

:deep(.el-form-item__label) {
  font-weight: 600;
  color: #2c3e50;
}

:deep(.el-button--primary) {
  background: linear-gradient(45deg, #667eea, #764ba2);
  border: none;
  
  &:hover {
    background: linear-gradient(45deg, #5a6fd8, #6a4190);
  }
}
</style>

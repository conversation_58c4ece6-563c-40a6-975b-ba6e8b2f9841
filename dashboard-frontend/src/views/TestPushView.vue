<template>
  <div class="test-push-view">
    <div class="container">
      <div class="header">
        <h1>🧪 模拟推送测试页面</h1>
        <p>用于测试进出记录推送功能</p>
      </div>

      <div class="form-section">
        <el-form :model="form" label-width="120px" size="large">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="选择学生">
                <el-select 
                  v-model="form.selectedStudent" 
                  placeholder="请选择学生"
                  style="width: 100%"
                  @change="onStudentChange"
                >
                  <el-option
                    v-for="student in students"
                    :key="student.personCode"
                    :label="`${student.personName} (${student.personCode})`"
                    :value="student.personCode"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            
            <el-col :span="12">
              <el-form-item label="进出方向">
                <el-select v-model="form.deviceDirection" placeholder="请选择方向" style="width: 100%">
                  <el-option label="进入宿舍 (1)" :value="1" />
                  <el-option label="离开宿舍 (2)" :value="2" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="设备名称">
                <el-input v-model="form.deviceName" placeholder="如: 9-10栋5号门出" />
              </el-form-item>
            </el-col>
            
            <el-col :span="12">
              <el-form-item label="设备ID">
                <el-input v-model="form.deviceId" placeholder="如: ET-5A1B-HM-ZD-R-172-16-102-81" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="设备IP">
                <el-input v-model="form.deviceIp" placeholder="如: *************" />
              </el-form-item>
            </el-col>
            
            <el-col :span="12">
              <el-form-item label="当前时间">
                <el-input :value="currentTime" readonly />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item>
            <el-button 
              type="primary" 
              size="large" 
              @click="pushRecord"
              :loading="loading"
              :disabled="!canPush"
            >
              {{ loading ? '推送中...' : '🚀 推送记录' }}
            </el-button>
            
            <el-button size="large" @click="resetForm">
              🔄 重置表单
            </el-button>
            
            <el-button size="large" @click="loadStudents">
              📋 刷新学生列表
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 结果显示区域 -->
      <div class="result-section" v-if="lastResult">
        <h3>📊 推送结果</h3>
        <el-alert
          :title="lastResult.success ? '推送成功' : '推送失败'"
          :type="lastResult.success ? 'success' : 'error'"
          :description="lastResult.message"
          show-icon
          :closable="false"
        />
        
        <div class="result-details" v-if="lastResult.data">
          <h4>📝 推送数据详情</h4>
          <pre>{{ JSON.stringify(lastResult.data, null, 2) }}</pre>
        </div>
      </div>

      <!-- 使用说明 -->
      <div class="help-section">
        <h3>📖 使用说明</h3>
        <ul>
          <li>选择要测试的学生（需要先在数据库中存在）</li>
          <li>选择进出方向：1=进入宿舍，2=离开宿舍</li>
          <li>填写设备信息（可使用默认值）</li>
          <li>点击推送按钮发送模拟消息</li>
          <li>查看推送结果和处理状态</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { testAPI } from '../api/test'

// 响应式数据
const form = reactive({
  selectedStudent: '',
  deviceDirection: 2,
  deviceName: '9-10栋5号门出',
  deviceId: 'ET-5A1B-HM-ZD-R-172-16-102-81',
  deviceIp: '*************'
})

const students = ref([])
const loading = ref(false)
const lastResult = ref(null)
const currentTime = ref('')

// 计算属性
const canPush = computed(() => {
  return form.selectedStudent && form.deviceDirection && form.deviceName && form.deviceId
})

const selectedStudentInfo = computed(() => {
  return students.value.find(s => s.personCode === form.selectedStudent)
})

// 更新当前时间
const updateCurrentTime = () => {
  currentTime.value = new Date().toLocaleString('zh-CN')
}

// 学生选择变化
const onStudentChange = () => {
  // 可以根据选择的学生自动填充一些信息
}

// 加载学生列表
const loadStudents = async () => {
  try {
    const response = await testAPI.getStudents()
    if (response.success) {
      students.value = response.data
      ElMessage.success('学生列表加载成功')
    } else {
      ElMessage.error('加载学生列表失败: ' + response.message)
    }
  } catch (error) {
    console.error('加载学生列表失败:', error)
    ElMessage.error('网络错误，请稍后重试')
  }
}

// 推送记录
const pushRecord = async () => {
  if (!canPush.value) {
    ElMessage.warning('请填写完整信息')
    return
  }

  loading.value = true
  lastResult.value = null

  try {
    const studentInfo = selectedStudentInfo.value
    const requestData = {
      personCode: form.selectedStudent,
      personName: studentInfo?.personName || '未知',
      deviceDirection: form.deviceDirection,
      deviceName: form.deviceName,
      deviceId: form.deviceId,
      deviceIp: form.deviceIp
    }

    const response = await testAPI.pushRecord(requestData)
    lastResult.value = response

    if (response.success) {
      ElMessage.success('推送成功！')
    } else {
      ElMessage.error('推送失败: ' + response.message)
    }

  } catch (error) {
    console.error('推送失败:', error)
    lastResult.value = {
      success: false,
      message: '网络错误: ' + error.message
    }
    ElMessage.error('网络错误，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 重置表单
const resetForm = () => {
  form.selectedStudent = ''
  form.deviceDirection = 2
  form.deviceName = '9-10栋5号门出'
  form.deviceId = 'ET-5A1B-HM-ZD-R-172-16-102-81'
  form.deviceIp = '*************'
  lastResult.value = null
  ElMessage.info('表单已重置')
}

// 组件挂载
onMounted(() => {
  loadStudents()
  updateCurrentTime()
  // 每秒更新时间
  setInterval(updateCurrentTime, 1000)
})
</script>

<style lang="scss" scoped>
.test-push-view {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.header {
  text-align: center;
  margin-bottom: 40px;
  
  h1 {
    color: #2c3e50;
    margin-bottom: 10px;
    font-size: 2.5em;
  }
  
  p {
    color: #7f8c8d;
    font-size: 1.2em;
  }
}

.form-section {
  background: #f8f9fa;
  padding: 30px;
  border-radius: 12px;
  margin-bottom: 30px;
}

.result-section {
  background: #f1f3f4;
  padding: 20px;
  border-radius: 12px;
  margin-bottom: 30px;
  
  h3 {
    color: #2c3e50;
    margin-bottom: 15px;
  }
  
  .result-details {
    margin-top: 20px;
    
    h4 {
      color: #34495e;
      margin-bottom: 10px;
    }
    
    pre {
      background: #2c3e50;
      color: #ecf0f1;
      padding: 15px;
      border-radius: 8px;
      overflow-x: auto;
      font-size: 12px;
      line-height: 1.4;
    }
  }
}

.help-section {
  background: #e8f4fd;
  padding: 20px;
  border-radius: 12px;
  border-left: 4px solid #3498db;
  
  h3 {
    color: #2980b9;
    margin-bottom: 15px;
  }
  
  ul {
    color: #34495e;
    
    li {
      margin-bottom: 8px;
      line-height: 1.6;
    }
  }
}

:deep(.el-form-item__label) {
  font-weight: 600;
  color: #2c3e50;
}

:deep(.el-button--primary) {
  background: linear-gradient(45deg, #667eea, #764ba2);
  border: none;
  
  &:hover {
    background: linear-gradient(45deg, #5a6fd8, #6a4190);
  }
}
</style>

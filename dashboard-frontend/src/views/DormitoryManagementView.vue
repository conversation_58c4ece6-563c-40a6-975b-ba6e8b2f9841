<template>
  <div class="dormitory-management">
    <!-- 主要内容区域 -->
    <main class="management-main">
      <!-- 面包屑导航 -->
      <div class="breadcrumb-section">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item>
            <a href="#" @click="goToBuildings">楼栋列表</a>
          </el-breadcrumb-item>
          <el-breadcrumb-item v-if="currentBuilding">
            <a href="#" @click="goToFloors">{{ currentBuilding.buildingName }}</a>
          </el-breadcrumb-item>
          <el-breadcrumb-item v-if="currentFloor">
            {{ currentFloor.floorName }}
          </el-breadcrumb-item>
        </el-breadcrumb>
      </div>

      <!-- 楼栋列表视图 -->
      <div v-if="currentView === 'buildings'" class="buildings-view">
        <div class="view-header">
          <h2 class="section-title">
            <el-icon><OfficeBuilding /></el-icon>
            楼栋列表
          </h2>
        </div>
        
        <PerfectScrollbar 
          class="buildings-grid" 
          v-loading="loading"
          :options="{ suppressScrollX: true }"
        >
          <div class="grid-container">
            <div 
              v-for="building in buildings" 
              :key="building.buildingCode"
              class="building-card dashboard-card"
              @click="selectBuilding(building)"
            >
            <div class="card-header">
              <el-icon class="building-icon"><OfficeBuilding /></el-icon>
              <h3>{{ building.buildingName }}</h3>
            </div>
            <div class="card-stats">
              <div class="stat-item">
                <span class="stat-value">{{ building.floorCount }}</span>
                <span class="stat-label">楼层</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">{{ building.dormitoryCount }}</span>
                <span class="stat-label">寝室</span>
              </div>
            </div>
            <div class="card-footer">
              <el-button type="primary" size="small">
                查看详情 <el-icon><ArrowRight /></el-icon>
              </el-button>
            </div>
          </div>
          </div>
        </PerfectScrollbar>
      </div>

      <!-- 楼层列表视图 -->
      <div v-if="currentView === 'floors'" class="floors-view">
        <div class="view-header">
          <h2 class="section-title">
            <el-icon><Goods /></el-icon>
            {{ currentBuilding.buildingName }} - 楼层列表
          </h2>
        </div>
        
        <PerfectScrollbar 
          class="floors-grid" 
          v-loading="loading"
          :options="{ suppressScrollX: true }"
        >
          <div class="grid-container">
            <div 
              v-for="floor in floors" 
              :key="floor.floor"
              class="floor-card dashboard-card"
              @click="selectFloor(floor)"
            >
            <div class="card-header">
              <el-icon class="floor-icon"><Goods /></el-icon>
              <h3>{{ floor.floorName }}</h3>
            </div>
            <div class="card-stats">
              <div class="stat-item">
                <span class="stat-value">{{ floor.dormitoryCount }}</span>
                <span class="stat-label">寝室数量</span>
              </div>
            </div>
            <div class="card-footer">
              <el-button type="primary" size="small">
                查看寝室 <el-icon><ArrowRight /></el-icon>
              </el-button>
            </div>
          </div>
          </div>
        </PerfectScrollbar>
      </div>

      <!-- 寝室列表视图 -->
      <div v-if="currentView === 'dormitories'" class="dormitories-view">
        <div class="view-header">
          <h2 class="section-title">
            <el-icon><House /></el-icon>
            {{ currentBuilding.buildingName }} {{ currentFloor.floorName }} - 寝室列表
          </h2>
        </div>
        
        <PerfectScrollbar 
          class="dormitories-grid" 
          v-loading="loading"
          :options="{ suppressScrollX: true }"
        >
          <div class="grid-container">
            <div 
              v-for="dormitory in dormitories" 
              :key="dormitory.dormitoryCode"
              class="dormitory-card dashboard-card"
            >
            <div class="card-header">
              <div class="room-info">
                <el-icon class="room-icon"><House /></el-icon>
                <h3>{{ dormitory.roomName }}</h3>
                <el-tag 
                  :type="dormitory.genderCode === '1' ? 'primary' : 'danger'" 
                  size="small"
                >
                  {{ dormitory.genderCode === '1' ? '男生' : '女生' }}
                </el-tag>
              </div>
              <div class="occupancy-info">
                <span class="occupancy-text">
                  {{ dormitory.currentPersonCount }}/{{ dormitory.bedCount }}
                </span>
                <el-progress 
                  :percentage="Math.round((dormitory.currentPersonCount / dormitory.bedCount) * 100)"
                  :status="dormitory.currentPersonCount === dormitory.bedCount ? 'success' : ''"
                  :stroke-width="6"
                />
              </div>
            </div>
            
            <div class="card-body">
              <div class="persons-list">
                <div 
                  v-for="person in dormitory.persons" 
                  :key="person.personCode"
                  class="person-item"
                >
                  <div class="person-avatar">
                    <el-icon><User /></el-icon>
                  </div>
                  <div class="person-info">
                    <div class="person-name">{{ person.personName }}</div>
                    <div class="person-details">
                      <span class="bed-info">床位: {{ person.bedNo || '未分配' }}</span>
                      <span class="gender-info">{{ person.genderText }}</span>
                    </div>
                  </div>
                </div>
                
                <!-- 空床位显示 -->
                <div 
                  v-for="i in (dormitory.bedCount - dormitory.currentPersonCount)" 
                  :key="'empty-' + i"
                  class="person-item empty-bed"
                >
                  <div class="person-avatar empty">
                    <el-icon><Plus /></el-icon>
                  </div>
                  <div class="person-info">
                    <div class="person-name">空床位</div>
                    <div class="person-details">
                      <span class="bed-info">待分配</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          </div>
        </PerfectScrollbar>
      </div>
    </main>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { 
  OfficeBuilding, 
  Goods, 
  House, 
  User, 
  Plus,
  ArrowRight,
  HomeFilled
} from '@element-plus/icons-vue'
import { dormitoryAPI } from '../api/dormitory.js'
import { ElMessage } from 'element-plus'
import { PerfectScrollbar } from 'vue3-perfect-scrollbar'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const currentView = ref('buildings') // buildings, floors, dormitories
const buildings = ref([])
const floors = ref([])
const dormitories = ref([])

// 当前选中的数据
const currentBuilding = ref(null)
const currentFloor = ref(null)

// 导航方法（用于面包屑）
const goToBuildings = () => {
  currentView.value = 'buildings'
  currentBuilding.value = null
  currentFloor.value = null
}

const goToFloors = () => {
  if (currentBuilding.value) {
    currentView.value = 'floors'
    currentFloor.value = null
    loadFloors(currentBuilding.value.buildingCode)
  }
}

// 选择楼栋
const selectBuilding = async (building) => {
  currentBuilding.value = building
  currentView.value = 'floors'
  await loadFloors(building.buildingCode)
}

// 选择楼层
const selectFloor = async (floor) => {
  currentFloor.value = floor
  currentView.value = 'dormitories'
  await loadDormitories(currentBuilding.value.buildingCode, floor.floor)
}

// 加载楼栋列表
const loadBuildings = async () => {
  try {
    loading.value = true
    const response = await dormitoryAPI.getBuildings()
    
    if (response.success) {
      buildings.value = response.data
    } else {
      ElMessage.error('加载楼栋列表失败: ' + response.message)
    }
  } catch (error) {
    console.error('加载楼栋列表失败:', error)
    ElMessage.error('加载楼栋列表失败')
  } finally {
    loading.value = false
  }
}

// 加载楼层列表
const loadFloors = async (buildingCode) => {
  try {
    loading.value = true
    const response = await dormitoryAPI.getBuildingFloors(buildingCode)
    
    if (response.success) {
      floors.value = response.data
    } else {
      ElMessage.error('加载楼层列表失败: ' + response.message)
    }
  } catch (error) {
    console.error('加载楼层列表失败:', error)
    ElMessage.error('加载楼层列表失败')
  } finally {
    loading.value = false
  }
}

// 加载寝室列表
const loadDormitories = async (buildingCode, floor) => {
  try {
    loading.value = true
    const response = await dormitoryAPI.getFloorDormitories(buildingCode, floor)
    
    if (response.success) {
      dormitories.value = response.data
    } else {
      ElMessage.error('加载寝室列表失败: ' + response.message)
    }
  } catch (error) {
    console.error('加载寝室列表失败:', error)
    ElMessage.error('加载寝室列表失败')
  } finally {
    loading.value = false
  }
}

// 组件挂载时加载楼栋列表
onMounted(() => {
  loadBuildings()
})
</script>

<style scoped lang="scss">
@use 'sass:color';
.dormitory-management {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, $bg-primary 0%, $bg-secondary 100%);
  overflow: hidden;
}

.management-main {
  flex: 1;
  padding: $spacing-lg;
  overflow: hidden;
  min-height: 0;
  
  .breadcrumb-section {
    margin-bottom: $spacing-lg;
    padding-bottom: $spacing-md;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    
    :deep(.el-breadcrumb__inner) {
      color: $text-secondary;
      
      &.is-link {
        color: $primary-color;
        
        &:hover {
          color: color.adjust($primary-color, $lightness: 10%);
        }
      }
    }
  }
  
  .view-header {
    margin-bottom: $spacing-lg;
    
    .section-title {
      font-size: $font-size-xl;
      font-weight: 500;
      color: $text-primary;
      margin: 0;
      display: flex;
      align-items: center;
      gap: $spacing-md;
    }
  }
}

// 楼栋网格
.buildings-grid {
  height: calc(100vh - 280px);
  
  .grid-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: $spacing-lg;
    padding: $spacing-md 0;
  }
  
  .building-card {
    padding: $spacing-lg;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-4px);
    }
    
    .card-header {
      display: flex;
      align-items: center;
      gap: $spacing-md;
      margin-bottom: $spacing-lg;
      
      .building-icon {
        font-size: 32px;
        color: $primary-color;
      }
      
      h3 {
        font-size: $font-size-lg;
        font-weight: 600;
        color: $text-primary;
        margin: 0;
      }
    }
    
    .card-stats {
      display: flex;
      justify-content: space-around;
      margin-bottom: $spacing-lg;
      
      .stat-item {
        text-align: center;
        
        .stat-value {
          display: block;
          font-size: $font-size-xl;
          font-weight: 700;
          color: $primary-color;
          margin-bottom: $spacing-xs;
        }
        
        .stat-label {
          font-size: $font-size-sm;
          color: $text-secondary;
        }
      }
    }
    
    .card-footer {
      text-align: center;
    }
  }
}

// 楼层网格
.floors-grid {
  height: calc(100vh - 280px);
  
  .grid-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: $spacing-lg;
    padding: $spacing-md 0;
  }
  
  .floor-card {
    padding: $spacing-lg;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-4px);
    }
    
    .card-header {
      display: flex;
      align-items: center;
      gap: $spacing-md;
      margin-bottom: $spacing-lg;
      
      .floor-icon {
        font-size: 28px;
        color: $success-color;
      }
      
      h3 {
        font-size: $font-size-lg;
        font-weight: 600;
        color: $text-primary;
        margin: 0;
      }
    }
    
    .card-stats {
      text-align: center;
      margin-bottom: $spacing-lg;
      
      .stat-item {
        .stat-value {
          display: block;
          font-size: $font-size-xl;
          font-weight: 700;
          color: $success-color;
          margin-bottom: $spacing-xs;
        }
        
        .stat-label {
          font-size: $font-size-sm;
          color: $text-secondary;
        }
      }
    }
    
    .card-footer {
      text-align: center;
    }
  }
}

// 寝室网格
.dormitories-grid {
  height: calc(100vh - 280px);
  
  .grid-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: $spacing-lg;
    padding: $spacing-md 0;
  }
  
  .dormitory-card {
    padding: $spacing-lg;
    
    .card-header {
      margin-bottom: $spacing-lg;
      
      .room-info {
        display: flex;
        align-items: center;
        gap: $spacing-md;
        margin-bottom: $spacing-md;
        
        .room-icon {
          font-size: 24px;
          color: $warning-color;
        }
        
        h3 {
          font-size: $font-size-lg;
          font-weight: 600;
          color: $text-primary;
          margin: 0;
          flex: 1;
        }
      }
      
      .occupancy-info {
        .occupancy-text {
          font-size: $font-size-md;
          color: $text-secondary;
          margin-bottom: $spacing-xs;
          display: block;
        }
      }
    }
    
    .card-body {
      .persons-list {
        display: flex;
        flex-direction: column;
        gap: $spacing-md;
        
        .person-item {
          display: flex;
          align-items: center;
          gap: $spacing-md;
          padding: $spacing-sm;
          border-radius: $border-radius-md;
          background: rgba(255, 255, 255, 0.05);
          
          &.empty-bed {
            opacity: 0.6;
            border: 2px dashed rgba(255, 255, 255, 0.3);
            background: transparent;
          }
          
          .person-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: $primary-color;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            
            &.empty {
              background: transparent;
              border: 2px dashed rgba(255, 255, 255, 0.5);
              color: $text-muted;
            }
          }
          
          .person-info {
            flex: 1;
            
            .person-name {
              font-size: $font-size-md;
              font-weight: 500;
              color: $text-primary;
              margin-bottom: $spacing-xs;
            }
            
            .person-details {
              display: flex;
              gap: $spacing-md;
              font-size: $font-size-sm;
              color: $text-secondary;
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .buildings-grid,
  .floors-grid {
    grid-template-columns: 1fr;
  }
  
  .dormitories-grid {
    grid-template-columns: 1fr;
  }
  
  .management-main {
    padding: $spacing-md;
  }
}
</style> 
<template>
  <div class="task-management-view">
    <!-- 统计卡片 -->
    <div class="stats-section">
      <div class="stat-card dashboard-card">
        <div class="stat-icon total">
          <el-icon><List /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ dashboardData.totalTasks || 0 }}</div>
          <div class="stat-label">总任务数</div>
        </div>
      </div>
      
      <div class="stat-card dashboard-card">
        <div class="stat-icon enabled">
          <el-icon><CircleCheck /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ dashboardData.enabledTasks || 0 }}</div>
          <div class="stat-label">启用任务</div>
        </div>
      </div>
      
      <div class="stat-card dashboard-card">
        <div class="stat-icon running">
          <el-icon><Loading /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ dashboardData.runningTasks || 0 }}</div>
          <div class="stat-label">运行中</div>
        </div>
      </div>
      
      <div class="stat-card dashboard-card">
        <div class="stat-icon failed">
          <el-icon><Warning /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ dashboardData.failedTasks || 0 }}</div>
          <div class="stat-label">异常任务</div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧任务列表 -->
      <div class="task-list-section dashboard-card">
        <div class="section-header">
          <h3>任务列表</h3>
          <div class="header-actions">
            <el-button size="small" :icon="Plus" @click="showCreateTaskDialog">
              新建任务
            </el-button>
            <el-button size="small" :icon="Operation" @click="showBatchOperationDialog">
              批量操作
            </el-button>
          </div>
        </div>
        
        <div class="task-filters">
          <el-input
            v-model="taskFilter"
            placeholder="搜索任务名称或描述"
            :prefix-icon="Search"
            clearable
            @input="filterTasks"
          />
          <el-select v-model="statusFilter" placeholder="任务状态" @change="filterTasks">
            <el-option label="全部" value="" />
            <el-option label="启用" value="enabled" />
            <el-option label="禁用" value="disabled" />
            <el-option label="运行中" value="running" />
            <el-option label="异常" value="failed" />
          </el-select>
        </div>

        <PerfectScrollbar class="task-list">
          <div
            v-for="task in filteredTasks"
            :key="task.taskName"
            class="task-item"
            :class="{ active: selectedTask?.taskName === task.taskName }"
            @click="selectTask(task)"
          >
            <div class="task-info">
              <div class="task-name">{{ task.taskName }}</div>
              <div class="task-description">{{ task.taskDescription }}</div>
              <div class="task-meta">
                <span class="task-schedule">
                  {{ getScheduleText(task) }}
                </span>
                <span class="task-last-run">
                  最后执行: {{ formatTime(task.lastExecutionTime) }}
                </span>
              </div>
            </div>
            
            <div class="task-status">
              <el-tag
                :type="getTaskStatusType(task)"
                :icon="getTaskStatusIcon(task)"
                size="small"
              >
                {{ getTaskStatusText(task) }}
              </el-tag>
            </div>
            
            <div class="task-actions">
              <el-button
                size="small"
                :type="task.enabled ? 'warning' : 'success'"
                :icon="task.enabled ? VideoPause : VideoPlay"
                @click.stop="toggleTask(task)"
              >
                {{ task.enabled ? '禁用' : '启用' }}
              </el-button>
              <el-button
                size="small"
                type="primary"
                :icon="CaretRight"
                @click.stop="executeTask(task)"
                :disabled="!task.enabled"
              >
                执行
              </el-button>
            </div>
          </div>
        </PerfectScrollbar>
      </div>

      <!-- 右侧任务详情 -->
      <div class="task-detail-section dashboard-card">
        <div v-if="!selectedTask" class="no-selection">
          <el-icon class="no-selection-icon"><Select /></el-icon>
          <p>请选择一个任务查看详细信息</p>
        </div>
        
        <PerfectScrollbar v-else class="task-detail">
          <div class="detail-header">
            <h3>{{ selectedTask.taskName }}</h3>
            <div class="detail-actions">
              <el-button :icon="Edit" @click="editTask">编辑</el-button>
              <el-button :icon="Document" @click="exportTaskReport">导出报告</el-button>
            </div>
          </div>
          
          <!-- 任务基本信息 -->
          <div class="detail-section">
            <h4>基本信息</h4>
            <div class="info-grid">
              <div class="info-item">
                <label>任务类:</label>
                <span>{{ selectedTask.taskClass }}</span>
              </div>
              <div class="info-item">
                <label>执行方法:</label>
                <span>{{ selectedTask.taskMethod }}</span>
              </div>
              <div class="info-item">
                <label>调度表达式:</label>
                <span>{{ selectedTask.cronExpression || '固定频率' }}</span>
              </div>
              <div class="info-item">
                <label>执行频率:</label>
                <span>{{ getScheduleText(selectedTask) }}</span>
              </div>
            </div>
          </div>
          
          <!-- 执行统计 -->
          <div class="detail-section">
            <h4>执行统计</h4>
            <div class="stats-grid">
              <div class="stat-item">
                <div class="stat-number">{{ selectedTask.executionCount || 0 }}</div>
                <div class="stat-label">总执行次数</div>
              </div>
              <div class="stat-item">
                <div class="stat-number success">{{ selectedTask.successCount || 0 }}</div>
                <div class="stat-label">成功次数</div>
              </div>
              <div class="stat-item">
                <div class="stat-number failed">{{ selectedTask.failureCount || 0 }}</div>
                <div class="stat-label">失败次数</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">{{ calculateSuccessRate(selectedTask) }}%</div>
                <div class="stat-label">成功率</div>
              </div>
            </div>
          </div>
          
          <!-- 最近执行日志 -->
          <div class="detail-section">
            <h4>最近执行日志</h4>
            <PerfectScrollbar class="log-list">
              <div
                v-for="log in taskLogs"
                :key="log.id"
                class="log-item"
                :class="log.executionStatus.toLowerCase()"
              >
                <div class="log-time">{{ formatTime(log.executionStartTime) }}</div>
                <div class="log-status">
                  <el-tag :type="getLogStatusType(log.executionStatus)" size="small">
                    {{ log.executionStatus }}
                  </el-tag>
                </div>
                <div class="log-duration">{{ log.executionDuration }}ms</div>
                <div class="log-message">{{ log.resultMessage || log.errorMessage }}</div>
              </div>
            </PerfectScrollbar>
          </div>
        </PerfectScrollbar>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import {
  Timer, Monitor, Refresh, List, CircleCheck, Loading, Warning,
  Plus, Operation, Search, VideoPause, VideoPlay, CaretRight, Select,
  Edit, Document, User, Connection
} from '@element-plus/icons-vue'
import { taskAPI } from '../api/tasks.js'
import { ElMessage, ElMessageBox } from 'element-plus'
import { PerfectScrollbar } from 'vue3-perfect-scrollbar'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const dashboardData = reactive({})
const tasks = ref([])
const selectedTask = ref(null)
const taskLogs = ref([])

// 筛选条件
const taskFilter = ref('')
const statusFilter = ref('')

// 筛选后的任务列表
const filteredTasks = computed(() => {
  let filtered = tasks.value
  
  if (taskFilter.value) {
    const keyword = taskFilter.value.toLowerCase()
    filtered = filtered.filter(task => 
      task.taskName.toLowerCase().includes(keyword) ||
      task.taskDescription.toLowerCase().includes(keyword)
    )
  }
  
  if (statusFilter.value) {
    filtered = filtered.filter(task => {
      switch (statusFilter.value) {
        case 'enabled': return task.enabled
        case 'disabled': return !task.enabled
        case 'running': return task.lastExecutionStatus === 'RUNNING'
        case 'failed': return task.failureCount > 0
        default: return true
      }
    })
  }
  
  return filtered
})

// 刷新所有数据
const refreshAll = async () => {
  loading.value = true
  try {
    await Promise.all([
      loadDashboardData(),
      loadTasks()
    ])
    if (selectedTask.value) {
      await loadTaskLogs(selectedTask.value.taskName)
    }
    ElMessage.success('数据刷新成功')
  } catch (error) {
    ElMessage.error('数据刷新失败')
  } finally {
    loading.value = false
  }
}

// 加载仪表板数据
const loadDashboardData = async () => {
  try {
    const response = await taskAPI.getDashboard()
    if (response.success) {
      Object.assign(dashboardData, response.data)
    }
  } catch (error) {
    console.error('加载仪表板数据失败:', error)
  }
}

// 加载任务列表
const loadTasks = async () => {
  try {
    const response = await taskAPI.getTasks(0, 100) // 加载所有任务
    if (response.success) {
      tasks.value = response.data || []
    }
  } catch (error) {
    console.error('加载任务列表失败:', error)
    ElMessage.error('加载任务列表失败')
  }
}

// 选择任务
const selectTask = async (task) => {
  selectedTask.value = task
  await loadTaskLogs(task.taskName)
}

// 加载任务执行日志
const loadTaskLogs = async (taskName) => {
  try {
    const response = await taskAPI.getTaskLogs(taskName, 0, 10)
    if (response.success) {
      taskLogs.value = response.data || []
    }
  } catch (error) {
    console.error('加载任务日志失败:', error)
  }
}

// 切换任务状态
const toggleTask = async (task) => {
  try {
    const action = task.enabled ? '禁用' : '启用'
    await ElMessageBox.confirm(`确定要${action}任务 "${task.taskName}" 吗？`, '确认操作')
    
    const response = await taskAPI.toggleTask(task.taskName)
    if (response.success) {
      ElMessage.success(response.message)
      await loadTasks()
      if (selectedTask.value?.taskName === task.taskName) {
        selectedTask.value = tasks.value.find(t => t.taskName === task.taskName)
      }
    } else {
      ElMessage.error(response.message)
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
    }
  }
}

// 执行任务
const executeTask = async (task) => {
  try {
    await ElMessageBox.confirm(`确定要立即执行任务 "${task.taskName}" 吗？`, '确认执行')
    
    const response = await taskAPI.executeTask(task.taskName)
    if (response.success) {
      ElMessage.success('任务执行成功')
      await refreshAll()
    } else {
      ElMessage.error(response.message)
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('执行失败')
    }
  }
}

// 筛选任务
const filterTasks = () => {
  // 筛选逻辑在computed中处理
}

// 工具函数
const getScheduleText = (task) => {
  if (task.cronExpression) {
    return `Cron: ${task.cronExpression}`
  } else if (task.fixedRate) {
    return `每${Math.floor(task.fixedRate / 1000)}秒`
  } else if (task.fixedDelay) {
    return `间隔${Math.floor(task.fixedDelay / 1000)}秒`
  }
  return '未配置'
}

const getTaskStatusType = (task) => {
  if (!task.enabled) return 'info'
  if (task.lastExecutionStatus === 'RUNNING') return 'warning'
  if (task.failureCount > 0) return 'danger'
  return 'success'
}

const getTaskStatusIcon = (task) => {
  if (!task.enabled) return VideoPause
  if (task.lastExecutionStatus === 'RUNNING') return Loading
  if (task.failureCount > 0) return Warning
  return CircleCheck
}

const getTaskStatusText = (task) => {
  if (!task.enabled) return '已禁用'
  if (task.lastExecutionStatus === 'RUNNING') return '运行中'
  if (task.failureCount > 0) return '有异常'
  return '正常'
}

const getLogStatusType = (status) => {
  switch (status) {
    case 'SUCCESS': return 'success'
    case 'FAILED': return 'danger'
    case 'RUNNING': return 'warning'
    default: return 'info'
  }
}

const formatTime = (time) => {
  if (!time) return '从未执行'
  return new Date(time).toLocaleString('zh-CN')
}

const calculateSuccessRate = (task) => {
  const total = task.executionCount || 0
  const success = task.successCount || 0
  return total > 0 ? Math.round((success / total) * 100) : 0
}

// 占位函数
const showCreateTaskDialog = () => {
  ElMessage.info('新建任务功能开发中')
}

const showBatchOperationDialog = () => {
  ElMessage.info('批量操作功能开发中')
}

const editTask = () => {
  ElMessage.info('编辑任务功能开发中')
}

const exportTaskReport = () => {
  ElMessage.info('导出报告功能开发中')
}

// 组件挂载时加载数据
onMounted(() => {
  refreshAll()
})
</script>

<style scoped lang="scss">
// 自定义滚动条样式 - 参考大屏首页
* {
  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(64, 158, 255, 0.6);
    border-radius: 4px;
    transition: background 0.3s ease;

    &:hover {
      background: rgba(64, 158, 255, 0.8);
    }
  }

  &::-webkit-scrollbar-corner {
    background: rgba(255, 255, 255, 0.1);
  }
}

.task-management-view {
  padding: $spacing-lg;
  padding-top: calc(80px + #{$spacing-lg});
  background: linear-gradient(135deg, $bg-primary 0%, $bg-secondary 100%);
  height: 100vh;
  display: flex;
  flex-direction: column;
  cursor: default; // 显示正常鼠标指针
  overflow: hidden; // 防止整体页面滚动
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $spacing-lg;
  flex-shrink: 0;
  
  .header-left {
    .page-title {
      font-size: $font-size-title;
      font-weight: 700;
      margin: 0 0 $spacing-sm 0;
      display: flex;
      align-items: center;
      gap: $spacing-md;
      color: $text-primary;
      text-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
    }
    
    .page-subtitle {
      font-size: $font-size-md;
      color: $text-secondary;
    }
  }
  
  .header-right {
    display: flex;
    gap: $spacing-md;
  }
}

.stats-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: $spacing-lg;
  margin-bottom: $spacing-lg;
  flex-shrink: 0;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: $spacing-lg;
  
  .stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: $spacing-lg;
    
    .el-icon {
      font-size: 28px;
      color: white;
    }
    
    &.total {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    
    &.enabled {
      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }
    
    &.running {
      background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }
    
    &.failed {
      background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    }
  }
  
  .stat-content {
    .stat-value {
      font-size: $font-size-xxl;
      font-weight: 700;
      color: $text-primary;
      line-height: 1;
    }
    
    .stat-label {
      font-size: $font-size-sm;
      color: $text-secondary;
      margin-top: $spacing-xs;
    }
  }
}

.main-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: $spacing-lg;
  flex: 1;
  min-height: 0;
  overflow: hidden; // 防止内容溢出
}

.task-list-section,
.task-detail-section {
  display: flex;
  flex-direction: column;
  padding: $spacing-lg;
  height: 100%;
  min-height: 0;
  overflow: hidden; // 防止内容溢出
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $spacing-lg;
  
  h3 {
    margin: 0;
    color: $text-primary;
  }
  
  .header-actions {
    display: flex;
    gap: $spacing-sm;
  }
}

.task-filters {
  display: flex;
  gap: $spacing-md;
  margin-bottom: $spacing-lg;
  
  .el-input {
    flex: 1;
  }
  
  .el-select {
    width: 120px;
  }
}

.task-list {
  flex: 1;
  max-height: calc(100vh - 400px); // 限制最大高度，防止超出屏幕



  .task-item {
    display: flex;
    align-items: center;
    padding: $spacing-md;
    margin-bottom: $spacing-sm;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      background: rgba(64, 158, 255, 0.1);
    }
    
    &.active {
      background: rgba(64, 158, 255, 0.2);
      border: 1px solid $primary-color;
    }
    
    .task-info {
      flex: 1;
      
      .task-name {
        font-weight: 600;
        color: $text-primary;
        margin-bottom: $spacing-xs;
      }
      
      .task-description {
        font-size: $font-size-sm;
        color: $text-secondary;
        margin-bottom: $spacing-xs;
      }
      
      .task-meta {
        display: flex;
        gap: $spacing-md;
        font-size: $font-size-xs;
        color: $text-muted;
      }
    }
    
    .task-status {
      margin: 0 $spacing-md;
    }
    
    .task-actions {
      display: flex;
      gap: $spacing-xs;
    }
  }
}

.task-detail {
  height: 100%;
  max-height: calc(100vh - 400px); // 限制最大高度，防止超出屏幕



  .detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-lg;
    
    h3 {
      margin: 0;
      color: $text-primary;
    }
    
    .detail-actions {
      display: flex;
      gap: $spacing-sm;
    }
  }
  
  .detail-section {
    margin-bottom: $spacing-lg;
    
    h4 {
      color: $text-primary;
      margin-bottom: $spacing-md;
      font-size: $font-size-lg;
    }
  }
  
  .info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: $spacing-md;
    
    .info-item {
      display: flex;
      
      label {
        font-weight: 600;
        color: $text-secondary;
        width: 100px;
        flex-shrink: 0;
      }
      
      span {
        color: $text-primary;
      }
    }
  }
  
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: $spacing-md;
    
    .stat-item {
      text-align: center;
      padding: $spacing-md;
      background: rgba(255, 255, 255, 0.05);
      border-radius: 8px;
      
      .stat-number {
        font-size: $font-size-xl;
        font-weight: 700;
        color: $text-primary;
        
        &.success {
          color: $success-color;
        }
        
        &.failed {
          color: $danger-color;
        }
      }
      
      .stat-label {
        font-size: $font-size-sm;
        color: $text-secondary;
        margin-top: $spacing-xs;
      }
    }
  }
  
  .log-list {
    max-height: 300px;



    .log-item {
      display: grid;
      grid-template-columns: 150px 80px 80px 1fr;
      gap: $spacing-md;
      padding: $spacing-sm;
      margin-bottom: $spacing-xs;
      background: rgba(255, 255, 255, 0.05);
      border-radius: 4px;
      font-size: $font-size-sm;
      
      .log-time {
        color: $text-secondary;
      }
      
      .log-duration {
        color: $text-muted;
      }
      
      .log-message {
        color: $text-primary;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}

.no-selection {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: $text-muted;
  
  .no-selection-icon {
    font-size: 64px;
    margin-bottom: $spacing-lg;
  }
  
  p {
    font-size: $font-size-lg;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .main-content {
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr;
    gap: $spacing-md;
  }

  .task-list {
    max-height: calc(50vh - 200px); // 在小屏幕上调整高度
  }

  .task-detail {
    max-height: calc(50vh - 200px); // 在小屏幕上调整高度
  }

  .stats-section {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .task-management-view {
    padding: $spacing-md;
    padding-top: calc(80px + #{$spacing-md});
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: $spacing-md;
  }
  
  .stats-section {
    grid-template-columns: 1fr;
  }
  
  .task-filters {
    flex-direction: column;
  }
}
</style>

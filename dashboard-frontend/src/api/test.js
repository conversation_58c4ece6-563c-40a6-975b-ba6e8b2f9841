import api from './index'

/**
 * 测试相关API
 */
export const testAPI = {
  /**
   * 模拟推送进出记录
   * @param {Object} data 推送数据
   * @returns {Promise}
   */
  pushRecord(data) {
    return api.post('/test/push-record', data)
  },

  /**
   * 获取学生列表
   * @returns {Promise}
   */
  getStudents() {
    return api.get('/test/students')
  },

  /**
   * 查询Redis缓存状态
   * @param {string} personCode 学生编码（可选）
   * @returns {Promise}
   */
  getRedisCache(personCode) {
    const params = personCode ? { personCode } : {}
    return api.get('/test/redis-cache', { params })
  },

  /**
   * 清除Redis缓存
   * @param {string} personCode 学生编码（可选）
   * @returns {Promise}
   */
  clearRedisCache(personCode) {
    const params = personCode ? { personCode } : {}
    return api.delete('/test/redis-cache', { params })
  }
}

export default testAPI

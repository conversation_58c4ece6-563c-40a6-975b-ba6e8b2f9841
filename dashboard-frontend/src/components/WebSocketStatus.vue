<template>
  <div class="websocket-status-panel">
    <div class="status-header">
      <span class="status-indicator" :class="status"></span>
      <span class="status-text">{{ statusText }}</span>
      <el-button 
        v-if="showDetails" 
        @click="toggleDetails" 
        size="small" 
        type="text"
        :icon="detailsVisible ? ArrowUp : ArrowDown"
      />
    </div>
    
    <div v-if="detailsVisible && showDetails" class="status-details">
      <div class="metrics-grid">
        <div class="metric-item">
          <span class="metric-label">连接状态</span>
          <span class="metric-value" :class="status">{{ statusText }}</span>
        </div>
        
        <div class="metric-item">
          <span class="metric-label">订阅者</span>
          <span class="metric-value">{{ metrics.subscribers || 0 }}</span>
        </div>
        
        <div class="metric-item">
          <span class="metric-label">成功率</span>
          <span class="metric-value">{{ metrics.successRate || '0%' }}</span>
        </div>
        
        <div class="metric-item">
          <span class="metric-label">运行时间</span>
          <span class="metric-value">{{ formatUptime(metrics.uptime) }}</span>
        </div>
        
        <div class="metric-item">
          <span class="metric-label">连接次数</span>
          <span class="metric-value">{{ metrics.totalConnections || 0 }}</span>
        </div>
        
        <div class="metric-item">
          <span class="metric-label">重连次数</span>
          <span class="metric-value">{{ metrics.totalReconnects || 0 }}</span>
        </div>
      </div>
      
      <div class="action-buttons" v-if="showActions">
        <el-button 
          size="small" 
          @click="$emit('reconnect')"
          :disabled="status === 'connecting'"
        >
          重连
        </el-button>
        
        <el-button 
          size="small" 
          type="danger"
          @click="$emit('forceReset')"
          :disabled="status === 'connecting'"
        >
          强制重置
        </el-button>
        
        <el-button 
          size="small" 
          type="info"
          @click="$emit('diagnose')"
        >
          诊断
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { ArrowUp, ArrowDown } from '@element-plus/icons-vue'
import wsPool from '../utils/websocket-pool.js'

// Props
const props = defineProps({
  showDetails: {
    type: Boolean,
    default: true
  },
  showActions: {
    type: Boolean,
    default: true
  },
  autoRefresh: {
    type: Boolean,
    default: true
  },
  refreshInterval: {
    type: Number,
    default: 5000
  }
})

// Emits
const emit = defineEmits(['reconnect', 'forceReset', 'diagnose'])

// State
const detailsVisible = ref(false)
const status = ref('disconnected')
const metrics = ref({})
let refreshTimer = null
let statusUnsubscribe = null

// Computed
const statusText = computed(() => {
  return wsPool.getStatusText()
})

// Methods
const toggleDetails = () => {
  detailsVisible.value = !detailsVisible.value
}

const updateStatus = () => {
  const currentStatus = wsPool.getStatus()
  status.value = currentStatus
  metrics.value = {
    ...wsPool.getMetrics(),
    subscribers: wsPool.subscribers?.size || 0
  }
}

const formatUptime = (uptime) => {
  if (!uptime) return '0s'
  
  const seconds = Math.floor(uptime / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  
  if (hours > 0) {
    return `${hours}h ${minutes % 60}m`
  } else if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`
  } else {
    return `${seconds}s`
  }
}

const startAutoRefresh = () => {
  if (props.autoRefresh && !refreshTimer) {
    refreshTimer = setInterval(updateStatus, props.refreshInterval)
  }
}

const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

// Lifecycle
onMounted(() => {
  // 立即更新状态
  status.value = wsPool.getStatus()
  updateStatus()
  startAutoRefresh()

  // 监听状态变化
  statusUnsubscribe = wsPool.onStatusChange((newStatus) => {
    status.value = newStatus
    updateStatus()
  })
})

onUnmounted(() => {
  stopAutoRefresh()
  if (statusUnsubscribe) {
    statusUnsubscribe()
  }
})
</script>

<style scoped>
.websocket-status-panel {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 12px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.status-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
}

.status-indicator.connected {
  background-color: #67c23a;
  box-shadow: 0 0 6px #67c23a;
}

.status-indicator.connecting {
  background-color: #e6a23c;
  animation: pulse 1.5s infinite;
}

.status-indicator.disconnected {
  background-color: #909399;
}

.status-indicator.error {
  background-color: #f56c6c;
  box-shadow: 0 0 6px #f56c6c;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.status-text {
  color: white;
  font-size: 14px;
  font-weight: 500;
}

.status-details {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 8px;
  margin-bottom: 12px;
}

.metric-item {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.metric-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
}

.metric-value {
  font-size: 14px;
  font-weight: 600;
  color: white;
}

.metric-value.connected {
  color: #67c23a;
}

.metric-value.error {
  color: #f56c6c;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.action-buttons .el-button {
  flex: 1;
  min-width: 80px;
}
</style>

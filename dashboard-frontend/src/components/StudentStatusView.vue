<template>
  <div class="student-status-view">
    <!-- 筛选区域 -->
    <div class="filter-section dashboard-card">
      <div class="filter-row">
        <div class="filter-item">
          <label>学生姓名：</label>
          <el-input
            v-model="filters.personName"
            placeholder="请输入学生姓名"
            clearable
            style="width: 200px"
            @clear="handleSearch"
            @keyup.enter="handleSearch"
          />
        </div>
        
        <div class="filter-item">
          <label>宿舍楼：</label>
          <el-select
            v-model="filters.buildingCode"
            placeholder="请选择楼栋"
            clearable
            style="width: 150px"
            @change="handleSearch"
          >
            <el-option label="全部楼栋" value="" />
            <el-option 
              v-for="building in buildings" 
              :key="building.buildingCode" 
              :label="building.buildingName" 
              :value="building.buildingCode" 
            />
          </el-select>
        </div>
        
        <div class="filter-item">
          <label>在寝状态：</label>
          <el-select
            v-model="filters.dormitoryStatus"
            placeholder="请选择状态"
            clearable
            style="width: 150px"
            @change="handleSearch"
          >
            <el-option label="全部" :value="null" />
            <el-option label="在寝" :value="1" />
            <el-option label="外出" :value="2" />
            <el-option label="无记录" :value="0" />
          </el-select>
        </div>
        
        <div class="filter-item">
          <el-button type="primary" @click="handleSearch" :loading="loading">
            <el-icon><Search /></el-icon>
            查询
          </el-button>
          <el-button @click="resetFilters">
            <el-icon><RefreshLeft /></el-icon>
            重置
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="stats-section">
      <div class="stat-card dashboard-card">
        <div class="stat-icon total">
          <el-icon><UserFilled /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ pagination.totalElements }}</div>
          <div class="stat-label">总学生数</div>
        </div>
      </div>
      
      <div class="stat-card dashboard-card">
        <div class="stat-icon in-dormitory">
          <el-icon><House /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ statusCounts.inDormitory }}</div>
          <div class="stat-label">在寝人数</div>
        </div>
      </div>
      
      <div class="stat-card dashboard-card">
        <div class="stat-icon out-dormitory">
          <el-icon><Position /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ statusCounts.outDormitory }}</div>
          <div class="stat-label">外出人数</div>
        </div>
      </div>
      
      <div class="stat-card dashboard-card">
        <div class="stat-icon no-record">
          <el-icon><QuestionFilled /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ statusCounts.noRecord }}</div>
          <div class="stat-label">无记录</div>
        </div>
      </div>
    </div>

    <!-- 学生列表 -->
    <div class="table-section dashboard-card">
      <el-table
        :data="studentList"
        v-loading="loading"
        :height="tableHeight"
        style="width: 100%"
        :default-sort="{ prop: 'lastPassTime', order: 'descending' }"
        class="dark-table"
      >
        <el-table-column prop="personName" label="学生姓名" min-width="140" fixed="left">
          <template #default="{ row }">
            <div class="name-cell">
              <el-avatar :size="32" class="avatar">
                {{ row.personName.charAt(0) }}
              </el-avatar>
              <span>{{ row.personName }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="personCode" label="学号" min-width="160" />

        <el-table-column prop="dormitoryStatusDesc" label="当前状态" min-width="130">
          <template #default="{ row }">
            <el-tag
              :type="getStatusTagType(row.lastInOrOut)"
              :icon="getStatusIcon(row.lastInOrOut)"
            >
              {{ row.dormitoryStatusDesc }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="lastInOrOutDesc" label="最后操作" min-width="130">
          <template #default="{ row }">
            <span :class="getOperationClass(row.lastInOrOut)">
              {{ row.lastInOrOutDesc }}
            </span>
          </template>
        </el-table-column>

        <el-table-column prop="lastPassTimeStr" label="通行时间" min-width="180" sortable />

        <el-table-column prop="lastDeviceName" label="通行设备" min-width="180" show-overflow-tooltip />

        <el-table-column prop="lastAreaName" label="通行区域" min-width="160" show-overflow-tooltip />

        <el-table-column prop="dormitoryName" label="所属宿舍" min-width="200" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="dormitory-cell">
              <el-icon><House /></el-icon>
              <span v-if="row.dormitoryName && row.dormitoryName !== '未分配宿舍' && row.dormitoryName !== '查询失败'">
                {{ row.getFullDormitoryInfo ? row.getFullDormitoryInfo() : formatDormitoryInfo(row) }}
              </span>
              <span v-else class="no-dormitory">
                {{ row.dormitoryName || '未分配宿舍' }}
              </span>
            </div>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.totalElements"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import {
  User, Refresh, Search, RefreshLeft, UserFilled,
  House, Position, QuestionFilled, Monitor, Connection
} from '@element-plus/icons-vue'
import { dormitoryAPI } from '../api/dormitory.js'
import { ElMessage } from 'element-plus'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const studentList = ref([])

// 筛选条件
const filters = reactive({
  personName: '',
  buildingCode: '',
  dormitoryStatus: null
})

// 宿舍楼列表
const buildings = ref([])

// 分页信息
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  totalElements: 0,
  totalPages: 0
})

// 状态统计
const statusCounts = computed(() => {
  const counts = { inDormitory: 0, outDormitory: 0, noRecord: 0 }
  studentList.value.forEach(student => {
    if (student.lastInOrOut === 1) {
      counts.inDormitory++
    } else if (student.lastInOrOut === 2) {
      counts.outDormitory++
    } else {
      counts.noRecord++
    }
  })
  return counts
})

// 动态计算表格高度
const tableHeight = computed(() => {
  // 计算除表格外其他元素的高度
  const navHeight = 80 // 导航栏高度
  const headerHeight = 120 // 页面头部高度
  const filterHeight = 100 // 筛选区域高度
  const statsHeight = 140 // 统计区域高度
  const paginationHeight = 80 // 分页区域高度
  const padding = 60 // 各种间距和内边距

  const otherElementsHeight = navHeight + headerHeight + filterHeight + statsHeight + paginationHeight + padding
  const availableHeight = window.innerHeight - otherElementsHeight

  // 确保最小高度
  return Math.max(availableHeight, 300)
})

// 获取状态标签类型
const getStatusTagType = (status) => {
  switch (status) {
    case 1: return 'success'  // 在寝
    case 2: return 'warning'  // 外出
    default: return 'info'    // 无记录
  }
}

// 获取状态图标
const getStatusIcon = (status) => {
  switch (status) {
    case 1: return House
    case 2: return Position
    default: return QuestionFilled
  }
}

// 格式化宿舍信息显示
const formatDormitoryInfo = (row) => {
  let info = ''
  if (row.buildingName) {
    info += row.buildingName
  }
  if (row.floor) {
    info += ` ${row.floor}层`
  }
  if (row.roomNumber) {
    info += ` ${row.roomNumber}室`
  }
  if (row.dormitoryName && row.dormitoryName !== info.trim()) {
    info += ` (${row.dormitoryName})`
  }
  return info.trim() || '未分配宿舍'
}

// 获取操作样式类
const getOperationClass = (status) => {
  switch (status) {
    case 1: return 'operation-in'
    case 2: return 'operation-out'
    default: return 'operation-none'
  }
}

// 加载宿舍楼列表
const loadBuildings = async () => {
  try {
    console.log('正在加载宿舍楼列表...')
    const response = await dormitoryAPI.getStudentDormitoryBuildings()
    
    if (response.success) {
      buildings.value = response.data || []
      console.log('宿舍楼列表加载成功:', buildings.value.length, '个楼栋')
    } else {
      console.warn('宿舍楼列表响应异常:', response)
      buildings.value = []
    }
  } catch (error) {
    console.error('加载宿舍楼列表失败:', error)
    buildings.value = []
  }
}

// 加载学生数据
const loadStudentData = async () => {
  loading.value = true
  
  try {
    const response = await dormitoryAPI.getAssignedStudentsStatus(
      pagination.currentPage - 1, // 后端从0开始
      pagination.pageSize,
      filters.personName,
      filters.buildingCode,
      filters.dormitoryStatus
    )
    
    if (response.success) {
      studentList.value = response.data || []
      pagination.totalElements = response.totalElements || 0
      pagination.totalPages = response.totalPages || 0
      
      console.log('学生数据加载成功:', {
        total: pagination.totalElements,
        current: studentList.value.length
      })
    } else {
      ElMessage.error(response.message || '加载学生数据失败')
      studentList.value = []
    }
  } catch (error) {
    console.error('加载学生数据失败:', error)
    ElMessage.error('网络错误，请稍后重试')
    studentList.value = []
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  pagination.currentPage = 1
  loadStudentData()
}

// 重置筛选条件
const resetFilters = () => {
  filters.personName = ''
  filters.buildingCode = ''
  filters.dormitoryStatus = null
  pagination.currentPage = 1
  loadStudentData()
}

// 刷新数据
const refreshData = () => {
  loadStudentData()
}

// 分页大小变化
const handleSizeChange = (newSize) => {
  pagination.pageSize = newSize
  pagination.currentPage = 1
  loadStudentData()
}

// 当前页变化
const handleCurrentChange = (newPage) => {
  pagination.currentPage = newPage
  loadStudentData()
}

// 组件挂载时加载数据
onMounted(async () => {
  // 并行加载宿舍楼列表和学生数据
  await Promise.all([
    loadBuildings(),
    loadStudentData()
  ])
})
</script>

<style scoped lang="scss">
.student-status-view {
  padding: $spacing-lg;
  padding-top: calc(80px + #{$spacing-lg}); // 为导航栏留出空间
  background: linear-gradient(135deg, $bg-primary 0%, $bg-secondary 100%);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  cursor: default; // 显示正常鼠标指针
}

.filter-section {
  margin-bottom: $spacing-lg;
  padding: $spacing-lg;
  flex-shrink: 0;

  .filter-row {
    display: flex;
    align-items: center;
    gap: $spacing-lg;
    flex-wrap: wrap;
  }

  .filter-item {
    display: flex;
    align-items: center;
    gap: $spacing-sm;

    label {
      font-weight: 500;
      color: $text-primary;
      white-space: nowrap;
    }
  }
}

.stats-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: $spacing-lg;
  margin-bottom: $spacing-lg;
  flex-shrink: 0;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: $spacing-lg;
  
  .stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: $spacing-lg;
    
    .el-icon {
      font-size: 28px;
      color: white;
    }
    
    &.total {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    
    &.in-dormitory {
      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }
    
    &.out-dormitory {
      background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }
    
    &.no-record {
      background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    }
  }
  
  .stat-content {
    .stat-value {
      font-size: $font-size-xxl;
      font-weight: 700;
      color: $text-primary;
      line-height: 1;
    }
    
    .stat-label {
      font-size: $font-size-sm;
      color: $text-secondary;
      margin-top: $spacing-xs;
    }
  }
}

.table-section {
  padding: $spacing-lg;
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  width: 100%;
  background: rgba(12, 20, 38, 0.4);
  border: 1px solid rgba(64, 158, 255, 0.2);
  border-radius: 12px;
  overflow: hidden;

  // 确保内部没有额外边框
  * {
    border-bottom: none !important;

    &:last-child {
      border-bottom: none !important;
    }
  }

  :deep(.dark-table) {
    background: transparent !important;
    border: none !important;
    height: 100% !important;
    flex: 1;
    width: 100% !important;
    table-layout: auto !important;

    // 强制移除所有边框
    &::before,
    &::after {
      display: none !important;
    }

    // 特别处理fit模式的伪元素
    &.el-table--fit {
      .el-table__inner-wrapper::before {
        display: none !important;
        width: 0 !important;
        height: 0 !important;
        content: none !important;
      }
    }

    // 表格容器
    .el-table__inner-wrapper {
      background: transparent !important;
      height: 100% !important;
      width: 100% !important;

      // 移除造成白线的伪元素
      &::before {
        display: none !important;
        width: 0 !important;
        height: 0 !important;
      }
    }

    // 表格主体容器
    .el-table__body-wrapper {
      flex: 1 !important;
      height: auto !important;
      max-height: none !important;
      width: 100% !important;
    }

    // 表格本身
    table {
      width: 100% !important;
      table-layout: auto !important;
      border-bottom: none !important;
    }

    // 表格列组
    colgroup {
      width: 100% !important;
    }

    // 表头样式
    .el-table__header-wrapper {
      background: transparent !important;

      .el-table__header {
        background: transparent !important;

        .el-table__cell {
          background: rgba(12, 20, 38, 0.8) !important;
          border-bottom: 1px solid rgba(64, 158, 255, 0.3) !important;
          border-right: 1px solid rgba(64, 158, 255, 0.1) !important;
          color: #ffffff !important;
          font-weight: 600 !important;
          padding: 12px 8px !important;
          width: auto !important;

          .cell {
            color: #ffffff !important;
            width: 100% !important;
          }
        }
      }
    }

    // 表格主体
    .el-table__body {
      background: transparent !important;

      .el-table__row {
        background: rgba(12, 20, 38, 0.6) !important;
        border: none !important;

        &:hover {
          background: rgba(64, 158, 255, 0.15) !important;
        }

        .el-table__cell {
          background: transparent !important;
          border-bottom: 1px solid rgba(64, 158, 255, 0.1) !important;
          border-right: 1px solid rgba(64, 158, 255, 0.05) !important;
          color: #ffffff !important;
          padding: 12px 8px !important;
          width: auto !important;

          .cell {
            color: #ffffff !important;
            width: 100% !important;
          }
        }
      }
    }

    // 空数据样式
    .el-table__empty-block {
      background: rgba(12, 20, 38, 0.6) !important;
      color: #ffffff !important;
    }

    // 加载样式
    .el-loading-mask {
      background: rgba(12, 20, 38, 0.8) !important;
    }

    // 移除所有边框和分隔线
    .el-table__border-left-patch,
    .el-table__border-bottom-patch,
    .el-table--border::after,
    .el-table--border::before,
    .el-table--border,
    .el-table__fixed-right-patch,
    .el-table__fixed-footer-wrapper {
      display: none !important;
      border: none !important;
    }

    // 表格底部
    .el-table__footer-wrapper {
      border-top: none !important;
      border-bottom: none !important;
    }

    // 表格最后一行
    .el-table__row:last-child {
      .el-table__cell {
        border-bottom: none !important;
      }
    }
  }

  .name-cell {
    display: flex;
    align-items: center;
    gap: $spacing-sm;

    .avatar {
      background: $primary-color;
      color: white;
      font-weight: 600;
    }
  }

  .dormitory-cell {
    display: flex;
    align-items: center;
    gap: $spacing-xs;

    .el-icon {
      color: $primary-color;
      font-size: 16px;
    }

    .no-dormitory {
      color: $text-muted;
      font-style: italic;
    }
  }

  .operation-in {
    color: $success-color;
    font-weight: 500;
  }

  .operation-out {
    color: $warning-color;
    font-weight: 500;
  }

  .operation-none {
    color: $text-muted;
  }
}

.pagination-section {
  display: flex;
  justify-content: center;
  margin-top: $spacing-lg;
  padding-top: $spacing-lg;
  border-top: none;
}

// 响应式设计
@media (max-width: 768px) {
  .student-status-view {
    padding: $spacing-md;
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: $spacing-md;
  }
  
  .filter-row {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .stats-section {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>

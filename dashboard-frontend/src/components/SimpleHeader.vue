<template>
  <header class="simple-header">
    <div class="header-content">
      <div class="title-section">
        <h1 class="main-title gradient-text">
          <el-icon v-if="icon"><component :is="icon" /></el-icon>
          {{ title }}
        </h1>
        <div class="subtitle">
          {{ currentDate }} {{ currentTime }}
        </div>
      </div>
      
      <div class="status-section">
        <div class="connection-status">
          <span class="status-indicator" :class="connectionStatus"></span>
          {{ connectionStatus === 'online' ? '系统正常' : '连接异常' }}
        </div>

        <!-- 菜单按钮 -->
        <div class="menu-buttons">
          <el-button
            type="primary"
            @click="goTo('/')"
            size="small"
            title="返回大屏"
            :class="{ active: currentPath === '/' }"
          >
            <el-icon><Monitor /></el-icon>
          </el-button>

          <el-button
            type="success"
            @click="goTo('/students')"
            size="small"
            title="学生状态查询"
            :class="{ active: currentPath === '/students' }"
          >
            <el-icon><User /></el-icon>
          </el-button>

          <el-button
            type="warning"
            @click="goTo('/tasks')"
            size="small"
            title="任务管理"
            :class="{ active: currentPath === '/tasks' }"
          >
            <el-icon><Timer /></el-icon>
          </el-button>

          <el-button
            type="info"
            @click="goTo('/dormitories')"
            size="small"
            title="寝室管理"
            :class="{ active: currentPath === '/dormitories' }"
          >
            <el-icon><Connection /></el-icon>
          </el-button>

          <el-button
            type="warning"
            @click="goTo('/websocket-test')"
            size="small"
            title="WebSocket测试"
            :class="{ active: currentPath === '/websocket-test' }"
          >
            <el-icon><Monitor /></el-icon>
          </el-button>
        </div>

        <!-- 操作按钮区域 -->
        <div class="action-buttons">
          <slot name="actions">
            <!-- 默认刷新按钮 -->
            <el-button
              type="primary"
              @click="$emit('refresh')"
              size="small"
              circle
              title="刷新数据"
              :loading="loading"
            >
              <el-icon><Refresh /></el-icon>
            </el-button>
          </slot>
        </div>
      </div>
    </div>
  </header>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { 
  Monitor, User, Timer, Connection, Refresh
} from '@element-plus/icons-vue'

// 定义props
const props = defineProps({
  title: {
    type: String,
    required: true
  },
  icon: {
    type: Object,
    default: null
  },
  loading: {
    type: Boolean,
    default: false
  },
  connectionStatus: {
    type: String,
    default: 'online'
  }
})

// 定义events
defineEmits(['refresh'])

const router = useRouter()
const route = useRoute()

// 响应式数据
const currentDate = ref('')
const currentTime = ref('')
let timeTimer = null

// 当前路径
const currentPath = computed(() => route.path)

// 导航方法
const goTo = (path) => {
  if (route.path !== path) {
    router.push(path)
  }
}

// 更新当前时间
const updateCurrentTime = () => {
  const now = new Date()
  currentDate.value = now.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  })
  currentTime.value = now.toLocaleTimeString('zh-CN', {
    hour12: false
  })
}

// 启动时间更新
const startTimeUpdate = () => {
  updateCurrentTime()
  timeTimer = setInterval(updateCurrentTime, 1000)
}

// 停止时间更新
const stopTimeUpdate = () => {
  if (timeTimer) {
    clearInterval(timeTimer)
    timeTimer = null
  }
}

// 组件挂载
onMounted(() => {
  startTimeUpdate()
})

// 组件卸载
onUnmounted(() => {
  stopTimeUpdate()
})
</script>

<style scoped lang="scss">
.simple-header {
  padding: $spacing-md $spacing-lg;
  border-bottom: 1px solid $border-color;
  background: rgba(255, 255, 255, 0.02);
  backdrop-filter: blur(10px);
  
  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .title-section {
    .main-title {
      font-size: $font-size-title;
      font-weight: 700;
      margin: 0;
      display: flex;
      align-items: center;
      gap: $spacing-md;
      text-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
    }
    
    .subtitle {
      font-size: $font-size-lg;
      color: $text-secondary;
      margin-top: $spacing-sm;
      font-weight: 400;
    }
  }
  
  .status-section {
    display: flex;
    align-items: center;
    gap: $spacing-lg;
    text-align: right;
    flex-wrap: wrap;
    
    .connection-status {
      display: flex;
      align-items: center;
      gap: $spacing-sm;
      font-size: $font-size-md;
      color: $text-secondary;

      .status-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;

        &.online {
          background-color: $success-color;
          box-shadow: 0 0 8px $success-color;
        }

        &.offline {
          background-color: $danger-color;
          box-shadow: 0 0 8px $danger-color;
        }
      }
    }

    .menu-buttons {
      display: flex;
      align-items: center;
      gap: 8px;

      .el-button {
        border: 1px solid rgba(255, 255, 255, 0.3);
        background: rgba(255, 255, 255, 0.1);
        color: white;

        &:hover,
        &.active {
          background: rgba(255, 255, 255, 0.2);
          border-color: rgba(255, 255, 255, 0.5);
        }
      }
    }

    .action-buttons {
      display: flex;
      align-items: center;
      gap: $spacing-md;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .simple-header {
    padding: $spacing-md $spacing-lg;
    
    .main-title {
      font-size: $font-size-xl;
    }
  }
}
</style>
2025-08-01 17:02:01.591 [SockJS-3] ERROR f.d.s.AsyncBatchPersistenceService 228 批量插入表tbl_access_control_record_2025_08_01失败: PreparedStatementCallback; bad SQL grammar [INSERT INTO tbl_access_control_record_2025_08_01 (person_code, person_name, device_code, device_name, place_code, place_name, area_code, area_name, inorout, pass_time, record_date, record_time, match_confidence, device_ip, temperature, update_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)]; nested exception is org.postgresql.util.PSQLException: ERROR: relation "tbl_access_control_record_2025_08_01" does not exist
  位置：13
2025-08-01 17:02:01.623 [SockJS-3] ERROR f.d.s.AsyncBatchPersistenceService 137 批量写入表tbl_access_control_record_2025_08_01失败: PreparedStatementCallback; bad SQL grammar [INSERT INTO tbl_access_control_record_2025_08_01 (person_code, person_name, device_code, device_name, place_code, place_name, area_code, area_name, inorout, pass_time, record_date, record_time, match_confidence, device_ip, temperature, update_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)]; nested exception is org.postgresql.util.PSQLException: ERROR: relation "tbl_access_control_record_2025_08_01" does not exist
  位置：13
2025-08-01 17:02:31.652 [SockJS-3] ERROR f.d.s.AsyncBatchPersistenceService 228 批量插入表tbl_access_control_record_2025_08_01失败: PreparedStatementCallback; bad SQL grammar [INSERT INTO tbl_access_control_record_2025_08_01 (person_code, person_name, device_code, device_name, place_code, place_name, area_code, area_name, inorout, pass_time, record_date, record_time, match_confidence, device_ip, temperature, update_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)]; nested exception is org.postgresql.util.PSQLException: ERROR: relation "tbl_access_control_record_2025_08_01" does not exist
  位置：13
2025-08-01 17:02:31.657 [SockJS-3] ERROR f.d.s.AsyncBatchPersistenceService 137 批量写入表tbl_access_control_record_2025_08_01失败: PreparedStatementCallback; bad SQL grammar [INSERT INTO tbl_access_control_record_2025_08_01 (person_code, person_name, device_code, device_name, place_code, place_name, area_code, area_name, inorout, pass_time, record_date, record_time, match_confidence, device_ip, temperature, update_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)]; nested exception is org.postgresql.util.PSQLException: ERROR: relation "tbl_access_control_record_2025_08_01" does not exist
  位置：13
2025-08-01 17:02:46.673 [SockJS-3] ERROR f.d.s.AsyncBatchPersistenceService 228 批量插入表tbl_access_control_record_2025_08_01失败: PreparedStatementCallback; bad SQL grammar [INSERT INTO tbl_access_control_record_2025_08_01 (person_code, person_name, device_code, device_name, place_code, place_name, area_code, area_name, inorout, pass_time, record_date, record_time, match_confidence, device_ip, temperature, update_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)]; nested exception is org.postgresql.util.PSQLException: ERROR: relation "tbl_access_control_record_2025_08_01" does not exist
  位置：13
2025-08-01 17:02:46.677 [SockJS-3] ERROR f.d.s.AsyncBatchPersistenceService 137 批量写入表tbl_access_control_record_2025_08_01失败: PreparedStatementCallback; bad SQL grammar [INSERT INTO tbl_access_control_record_2025_08_01 (person_code, person_name, device_code, device_name, place_code, place_name, area_code, area_name, inorout, pass_time, record_date, record_time, match_confidence, device_ip, temperature, update_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)]; nested exception is org.postgresql.util.PSQLException: ERROR: relation "tbl_access_control_record_2025_08_01" does not exist
  位置：13
2025-08-01 17:02:51.691 [SockJS-3] ERROR f.d.s.AsyncBatchPersistenceService 228 批量插入表tbl_access_control_record_2025_08_01失败: PreparedStatementCallback; bad SQL grammar [INSERT INTO tbl_access_control_record_2025_08_01 (person_code, person_name, device_code, device_name, place_code, place_name, area_code, area_name, inorout, pass_time, record_date, record_time, match_confidence, device_ip, temperature, update_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)]; nested exception is org.postgresql.util.PSQLException: ERROR: relation "tbl_access_control_record_2025_08_01" does not exist
  位置：13
2025-08-01 17:02:51.695 [SockJS-3] ERROR f.d.s.AsyncBatchPersistenceService 137 批量写入表tbl_access_control_record_2025_08_01失败: PreparedStatementCallback; bad SQL grammar [INSERT INTO tbl_access_control_record_2025_08_01 (person_code, person_name, device_code, device_name, place_code, place_name, area_code, area_name, inorout, pass_time, record_date, record_time, match_confidence, device_ip, temperature, update_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)]; nested exception is org.postgresql.util.PSQLException: ERROR: relation "tbl_access_control_record_2025_08_01" does not exist
  位置：13
2025-08-01 17:03:43.831 [http-nio-8080-exec-8] ERROR f.d.s.StudentStatusCacheService 103 获取学生状态缓存失败: personCode=SG000015, error=Could not read JSON: Unrecognized field "cacheKey" (class fastgatedemo.demo.dto.StudentStatusCacheDTO), not marked as ignorable (11 known properties: "personName", "gender", "personCode", "lastPassTime", "updateTime", "lastDeviceName", "departmentName", "lastAreaName", "departmentCode", "lastInOrOut", "telephone"])
 at [Source: (byte[])"["fastgatedemo.demo.dto.StudentStatusCacheDTO",{"personCode":"SG000015","personName":"刘小美","lastInOrOut":1,"lastPassTime":"2025-08-01 17:02:48","lastDeviceName":"9-10栋5号门出","lastAreaName":null,"updateTime":"2025-08-01 17:02:48","departmentCode":"130120","departmentName":"130120","gender":2,"telephone":"13800138005","cacheKey":"student:status:SG000015","inDormitory":true,"statusDescription":"已归寝室","inOrOutDescription":"进入寝室","statsGroupKey":"stats:group:in_dormitory""[truncated 2 bytes]; line: 1, column: 344] (through reference chain: fastgatedemo.demo.dto.StudentStatusCacheDTO["cacheKey"]); nested exception is com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException: Unrecognized field "cacheKey" (class fastgatedemo.demo.dto.StudentStatusCacheDTO), not marked as ignorable (11 known properties: "personName", "gender", "personCode", "lastPassTime", "updateTime", "lastDeviceName", "departmentName", "lastAreaName", "departmentCode", "lastInOrOut", "telephone"])
 at [Source: (byte[])"["fastgatedemo.demo.dto.StudentStatusCacheDTO",{"personCode":"SG000015","personName":"刘小美","lastInOrOut":1,"lastPassTime":"2025-08-01 17:02:48","lastDeviceName":"9-10栋5号门出","lastAreaName":null,"updateTime":"2025-08-01 17:02:48","departmentCode":"130120","departmentName":"130120","gender":2,"telephone":"13800138005","cacheKey":"student:status:SG000015","inDormitory":true,"statusDescription":"已归寝室","inOrOutDescription":"进入寝室","statsGroupKey":"stats:group:in_dormitory""[truncated 2 bytes]; line: 1, column: 344] (through reference chain: fastgatedemo.demo.dto.StudentStatusCacheDTO["cacheKey"])
2025-08-01 17:03:45.053 [http-nio-8080-exec-9] ERROR f.d.s.StudentStatusCacheService 103 获取学生状态缓存失败: personCode=SG000015, error=Could not read JSON: Unrecognized field "cacheKey" (class fastgatedemo.demo.dto.StudentStatusCacheDTO), not marked as ignorable (11 known properties: "personName", "gender", "personCode", "lastPassTime", "updateTime", "lastDeviceName", "departmentName", "lastAreaName", "departmentCode", "lastInOrOut", "telephone"])
 at [Source: (byte[])"["fastgatedemo.demo.dto.StudentStatusCacheDTO",{"personCode":"SG000015","personName":"刘小美","lastInOrOut":1,"lastPassTime":"2025-08-01 17:03:43","lastDeviceName":"9-10栋5号门出","lastAreaName":null,"updateTime":"2025-08-01 17:03:43","departmentCode":"130120","departmentName":"130120","gender":2,"telephone":"13800138005","cacheKey":"student:status:SG000015","inDormitory":true,"statusDescription":"已归寝室","inOrOutDescription":"进入寝室","statsGroupKey":"stats:group:in_dormitory""[truncated 2 bytes]; line: 1, column: 344] (through reference chain: fastgatedemo.demo.dto.StudentStatusCacheDTO["cacheKey"]); nested exception is com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException: Unrecognized field "cacheKey" (class fastgatedemo.demo.dto.StudentStatusCacheDTO), not marked as ignorable (11 known properties: "personName", "gender", "personCode", "lastPassTime", "updateTime", "lastDeviceName", "departmentName", "lastAreaName", "departmentCode", "lastInOrOut", "telephone"])
 at [Source: (byte[])"["fastgatedemo.demo.dto.StudentStatusCacheDTO",{"personCode":"SG000015","personName":"刘小美","lastInOrOut":1,"lastPassTime":"2025-08-01 17:03:43","lastDeviceName":"9-10栋5号门出","lastAreaName":null,"updateTime":"2025-08-01 17:03:43","departmentCode":"130120","departmentName":"130120","gender":2,"telephone":"13800138005","cacheKey":"student:status:SG000015","inDormitory":true,"statusDescription":"已归寝室","inOrOutDescription":"进入寝室","statsGroupKey":"stats:group:in_dormitory""[truncated 2 bytes]; line: 1, column: 344] (through reference chain: fastgatedemo.demo.dto.StudentStatusCacheDTO["cacheKey"])
2025-08-01 17:03:45.953 [http-nio-8080-exec-10] ERROR f.d.s.StudentStatusCacheService 103 获取学生状态缓存失败: personCode=SG000015, error=Could not read JSON: Unrecognized field "cacheKey" (class fastgatedemo.demo.dto.StudentStatusCacheDTO), not marked as ignorable (11 known properties: "personName", "gender", "personCode", "lastPassTime", "updateTime", "lastDeviceName", "departmentName", "lastAreaName", "departmentCode", "lastInOrOut", "telephone"])
 at [Source: (byte[])"["fastgatedemo.demo.dto.StudentStatusCacheDTO",{"personCode":"SG000015","personName":"刘小美","lastInOrOut":1,"lastPassTime":"2025-08-01 17:03:45","lastDeviceName":"9-10栋5号门出","lastAreaName":null,"updateTime":"2025-08-01 17:03:45","departmentCode":"130120","departmentName":"130120","gender":2,"telephone":"13800138005","cacheKey":"student:status:SG000015","inDormitory":true,"statusDescription":"已归寝室","inOrOutDescription":"进入寝室","statsGroupKey":"stats:group:in_dormitory""[truncated 2 bytes]; line: 1, column: 344] (through reference chain: fastgatedemo.demo.dto.StudentStatusCacheDTO["cacheKey"]); nested exception is com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException: Unrecognized field "cacheKey" (class fastgatedemo.demo.dto.StudentStatusCacheDTO), not marked as ignorable (11 known properties: "personName", "gender", "personCode", "lastPassTime", "updateTime", "lastDeviceName", "departmentName", "lastAreaName", "departmentCode", "lastInOrOut", "telephone"])
 at [Source: (byte[])"["fastgatedemo.demo.dto.StudentStatusCacheDTO",{"personCode":"SG000015","personName":"刘小美","lastInOrOut":1,"lastPassTime":"2025-08-01 17:03:45","lastDeviceName":"9-10栋5号门出","lastAreaName":null,"updateTime":"2025-08-01 17:03:45","departmentCode":"130120","departmentName":"130120","gender":2,"telephone":"13800138005","cacheKey":"student:status:SG000015","inDormitory":true,"statusDescription":"已归寝室","inOrOutDescription":"进入寝室","statsGroupKey":"stats:group:in_dormitory""[truncated 2 bytes]; line: 1, column: 344] (through reference chain: fastgatedemo.demo.dto.StudentStatusCacheDTO["cacheKey"])
2025-08-01 17:03:46.754 [SockJS-4] ERROR f.d.s.AsyncBatchPersistenceService 228 批量插入表tbl_access_control_record_2025_08_01失败: PreparedStatementCallback; bad SQL grammar [INSERT INTO tbl_access_control_record_2025_08_01 (person_code, person_name, device_code, device_name, place_code, place_name, area_code, area_name, inorout, pass_time, record_date, record_time, match_confidence, device_ip, temperature, update_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)]; nested exception is org.postgresql.util.PSQLException: ERROR: relation "tbl_access_control_record_2025_08_01" does not exist
  位置：13
2025-08-01 17:03:46.757 [SockJS-4] ERROR f.d.s.AsyncBatchPersistenceService 137 批量写入表tbl_access_control_record_2025_08_01失败: PreparedStatementCallback; bad SQL grammar [INSERT INTO tbl_access_control_record_2025_08_01 (person_code, person_name, device_code, device_name, place_code, place_name, area_code, area_name, inorout, pass_time, record_date, record_time, match_confidence, device_ip, temperature, update_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)]; nested exception is org.postgresql.util.PSQLException: ERROR: relation "tbl_access_control_record_2025_08_01" does not exist
  位置：13
2025-08-01 17:03:47.015 [http-nio-8080-exec-1] ERROR f.d.s.StudentStatusCacheService 103 获取学生状态缓存失败: personCode=SG000015, error=Could not read JSON: Unrecognized field "cacheKey" (class fastgatedemo.demo.dto.StudentStatusCacheDTO), not marked as ignorable (11 known properties: "personName", "gender", "personCode", "lastPassTime", "updateTime", "lastDeviceName", "departmentName", "lastAreaName", "departmentCode", "lastInOrOut", "telephone"])
 at [Source: (byte[])"["fastgatedemo.demo.dto.StudentStatusCacheDTO",{"personCode":"SG000015","personName":"刘小美","lastInOrOut":1,"lastPassTime":"2025-08-01 17:03:45","lastDeviceName":"9-10栋5号门出","lastAreaName":null,"updateTime":"2025-08-01 17:03:45","departmentCode":"130120","departmentName":"130120","gender":2,"telephone":"13800138005","cacheKey":"student:status:SG000015","inDormitory":true,"statusDescription":"已归寝室","inOrOutDescription":"进入寝室","statsGroupKey":"stats:group:in_dormitory""[truncated 2 bytes]; line: 1, column: 344] (through reference chain: fastgatedemo.demo.dto.StudentStatusCacheDTO["cacheKey"]); nested exception is com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException: Unrecognized field "cacheKey" (class fastgatedemo.demo.dto.StudentStatusCacheDTO), not marked as ignorable (11 known properties: "personName", "gender", "personCode", "lastPassTime", "updateTime", "lastDeviceName", "departmentName", "lastAreaName", "departmentCode", "lastInOrOut", "telephone"])
 at [Source: (byte[])"["fastgatedemo.demo.dto.StudentStatusCacheDTO",{"personCode":"SG000015","personName":"刘小美","lastInOrOut":1,"lastPassTime":"2025-08-01 17:03:45","lastDeviceName":"9-10栋5号门出","lastAreaName":null,"updateTime":"2025-08-01 17:03:45","departmentCode":"130120","departmentName":"130120","gender":2,"telephone":"13800138005","cacheKey":"student:status:SG000015","inDormitory":true,"statusDescription":"已归寝室","inOrOutDescription":"进入寝室","statsGroupKey":"stats:group:in_dormitory""[truncated 2 bytes]; line: 1, column: 344] (through reference chain: fastgatedemo.demo.dto.StudentStatusCacheDTO["cacheKey"])
2025-08-01 17:03:51.767 [SockJS-4] ERROR f.d.s.AsyncBatchPersistenceService 228 批量插入表tbl_access_control_record_2025_08_01失败: PreparedStatementCallback; bad SQL grammar [INSERT INTO tbl_access_control_record_2025_08_01 (person_code, person_name, device_code, device_name, place_code, place_name, area_code, area_name, inorout, pass_time, record_date, record_time, match_confidence, device_ip, temperature, update_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)]; nested exception is org.postgresql.util.PSQLException: ERROR: relation "tbl_access_control_record_2025_08_01" does not exist
  位置：13
2025-08-01 17:03:51.771 [SockJS-4] ERROR f.d.s.AsyncBatchPersistenceService 137 批量写入表tbl_access_control_record_2025_08_01失败: PreparedStatementCallback; bad SQL grammar [INSERT INTO tbl_access_control_record_2025_08_01 (person_code, person_name, device_code, device_name, place_code, place_name, area_code, area_name, inorout, pass_time, record_date, record_time, match_confidence, device_ip, temperature, update_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)]; nested exception is org.postgresql.util.PSQLException: ERROR: relation "tbl_access_control_record_2025_08_01" does not exist
  位置：13
2025-08-01 17:03:52.827 [http-nio-8080-exec-3] ERROR f.d.s.StudentStatusCacheService 103 获取学生状态缓存失败: personCode=SG000014, error=Could not read JSON: Unrecognized field "cacheKey" (class fastgatedemo.demo.dto.StudentStatusCacheDTO), not marked as ignorable (11 known properties: "personName", "gender", "personCode", "lastPassTime", "updateTime", "lastDeviceName", "departmentName", "lastAreaName", "departmentCode", "lastInOrOut", "telephone"])
 at [Source: (byte[])"["fastgatedemo.demo.dto.StudentStatusCacheDTO",{"personCode":"SG000014","personName":"王小强","lastInOrOut":1,"lastPassTime":"2025-08-01 17:02:47","lastDeviceName":"9-10栋5号门出","lastAreaName":null,"updateTime":"2025-08-01 17:02:47","departmentCode":"130120","departmentName":"130120","gender":1,"telephone":"13800138004","cacheKey":"student:status:SG000014","inDormitory":true,"statusDescription":"已归寝室","inOrOutDescription":"进入寝室","statsGroupKey":"stats:group:in_dormitory""[truncated 2 bytes]; line: 1, column: 344] (through reference chain: fastgatedemo.demo.dto.StudentStatusCacheDTO["cacheKey"]); nested exception is com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException: Unrecognized field "cacheKey" (class fastgatedemo.demo.dto.StudentStatusCacheDTO), not marked as ignorable (11 known properties: "personName", "gender", "personCode", "lastPassTime", "updateTime", "lastDeviceName", "departmentName", "lastAreaName", "departmentCode", "lastInOrOut", "telephone"])
 at [Source: (byte[])"["fastgatedemo.demo.dto.StudentStatusCacheDTO",{"personCode":"SG000014","personName":"王小强","lastInOrOut":1,"lastPassTime":"2025-08-01 17:02:47","lastDeviceName":"9-10栋5号门出","lastAreaName":null,"updateTime":"2025-08-01 17:02:47","departmentCode":"130120","departmentName":"130120","gender":1,"telephone":"13800138004","cacheKey":"student:status:SG000014","inDormitory":true,"statusDescription":"已归寝室","inOrOutDescription":"进入寝室","statsGroupKey":"stats:group:in_dormitory""[truncated 2 bytes]; line: 1, column: 344] (through reference chain: fastgatedemo.demo.dto.StudentStatusCacheDTO["cacheKey"])
2025-08-01 17:03:56.392 [http-nio-8080-exec-2] ERROR f.d.s.StudentStatusCacheService 103 获取学生状态缓存失败: personCode=SG000013, error=Could not read JSON: Unrecognized field "cacheKey" (class fastgatedemo.demo.dto.StudentStatusCacheDTO), not marked as ignorable (11 known properties: "personName", "gender", "personCode", "lastPassTime", "updateTime", "lastDeviceName", "departmentName", "lastAreaName", "departmentCode", "lastInOrOut", "telephone"])
 at [Source: (byte[])"["fastgatedemo.demo.dto.StudentStatusCacheDTO",{"personCode":"SG000013","personName":"李小红","lastInOrOut":1,"lastPassTime":"2025-08-01 17:02:45","lastDeviceName":"9-10栋5号门出","lastAreaName":null,"updateTime":"2025-08-01 17:02:45","departmentCode":"130120","departmentName":"130120","gender":2,"telephone":"13800138003","cacheKey":"student:status:SG000013","inDormitory":true,"statusDescription":"已归寝室","inOrOutDescription":"进入寝室","statsGroupKey":"stats:group:in_dormitory""[truncated 2 bytes]; line: 1, column: 344] (through reference chain: fastgatedemo.demo.dto.StudentStatusCacheDTO["cacheKey"]); nested exception is com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException: Unrecognized field "cacheKey" (class fastgatedemo.demo.dto.StudentStatusCacheDTO), not marked as ignorable (11 known properties: "personName", "gender", "personCode", "lastPassTime", "updateTime", "lastDeviceName", "departmentName", "lastAreaName", "departmentCode", "lastInOrOut", "telephone"])
 at [Source: (byte[])"["fastgatedemo.demo.dto.StudentStatusCacheDTO",{"personCode":"SG000013","personName":"李小红","lastInOrOut":1,"lastPassTime":"2025-08-01 17:02:45","lastDeviceName":"9-10栋5号门出","lastAreaName":null,"updateTime":"2025-08-01 17:02:45","departmentCode":"130120","departmentName":"130120","gender":2,"telephone":"13800138003","cacheKey":"student:status:SG000013","inDormitory":true,"statusDescription":"已归寝室","inOrOutDescription":"进入寝室","statsGroupKey":"stats:group:in_dormitory""[truncated 2 bytes]; line: 1, column: 344] (through reference chain: fastgatedemo.demo.dto.StudentStatusCacheDTO["cacheKey"])
2025-08-01 17:03:56.784 [SockJS-4] ERROR f.d.s.AsyncBatchPersistenceService 228 批量插入表tbl_access_control_record_2025_08_01失败: PreparedStatementCallback; bad SQL grammar [INSERT INTO tbl_access_control_record_2025_08_01 (person_code, person_name, device_code, device_name, place_code, place_name, area_code, area_name, inorout, pass_time, record_date, record_time, match_confidence, device_ip, temperature, update_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)]; nested exception is org.postgresql.util.PSQLException: ERROR: relation "tbl_access_control_record_2025_08_01" does not exist
  位置：13
2025-08-01 17:03:56.788 [SockJS-4] ERROR f.d.s.AsyncBatchPersistenceService 137 批量写入表tbl_access_control_record_2025_08_01失败: PreparedStatementCallback; bad SQL grammar [INSERT INTO tbl_access_control_record_2025_08_01 (person_code, person_name, device_code, device_name, place_code, place_name, area_code, area_name, inorout, pass_time, record_date, record_time, match_confidence, device_ip, temperature, update_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)]; nested exception is org.postgresql.util.PSQLException: ERROR: relation "tbl_access_control_record_2025_08_01" does not exist
  位置：13
2025-08-01 17:03:57.889 [http-nio-8080-exec-6] ERROR f.d.s.StudentStatusCacheService 103 获取学生状态缓存失败: personCode=SG000013, error=Could not read JSON: Unrecognized field "cacheKey" (class fastgatedemo.demo.dto.StudentStatusCacheDTO), not marked as ignorable (11 known properties: "personName", "gender", "personCode", "lastPassTime", "updateTime", "lastDeviceName", "departmentName", "lastAreaName", "departmentCode", "lastInOrOut", "telephone"])
 at [Source: (byte[])"["fastgatedemo.demo.dto.StudentStatusCacheDTO",{"personCode":"SG000013","personName":"李小红","lastInOrOut":1,"lastPassTime":"2025-08-01 17:03:56","lastDeviceName":"9-10栋5号门出","lastAreaName":null,"updateTime":"2025-08-01 17:03:56","departmentCode":"130120","departmentName":"130120","gender":2,"telephone":"13800138003","cacheKey":"student:status:SG000013","inDormitory":true,"statusDescription":"已归寝室","inOrOutDescription":"进入寝室","statsGroupKey":"stats:group:in_dormitory""[truncated 2 bytes]; line: 1, column: 344] (through reference chain: fastgatedemo.demo.dto.StudentStatusCacheDTO["cacheKey"]); nested exception is com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException: Unrecognized field "cacheKey" (class fastgatedemo.demo.dto.StudentStatusCacheDTO), not marked as ignorable (11 known properties: "personName", "gender", "personCode", "lastPassTime", "updateTime", "lastDeviceName", "departmentName", "lastAreaName", "departmentCode", "lastInOrOut", "telephone"])
 at [Source: (byte[])"["fastgatedemo.demo.dto.StudentStatusCacheDTO",{"personCode":"SG000013","personName":"李小红","lastInOrOut":1,"lastPassTime":"2025-08-01 17:03:56","lastDeviceName":"9-10栋5号门出","lastAreaName":null,"updateTime":"2025-08-01 17:03:56","departmentCode":"130120","departmentName":"130120","gender":2,"telephone":"13800138003","cacheKey":"student:status:SG000013","inDormitory":true,"statusDescription":"已归寝室","inOrOutDescription":"进入寝室","statsGroupKey":"stats:group:in_dormitory""[truncated 2 bytes]; line: 1, column: 344] (through reference chain: fastgatedemo.demo.dto.StudentStatusCacheDTO["cacheKey"])
2025-08-01 17:03:58.049 [http-nio-8080-exec-7] ERROR f.d.s.StudentStatusCacheService 103 获取学生状态缓存失败: personCode=SG000013, error=Could not read JSON: Unrecognized field "cacheKey" (class fastgatedemo.demo.dto.StudentStatusCacheDTO), not marked as ignorable (11 known properties: "personName", "gender", "personCode", "lastPassTime", "updateTime", "lastDeviceName", "departmentName", "lastAreaName", "departmentCode", "lastInOrOut", "telephone"])
 at [Source: (byte[])"["fastgatedemo.demo.dto.StudentStatusCacheDTO",{"personCode":"SG000013","personName":"李小红","lastInOrOut":1,"lastPassTime":"2025-08-01 17:03:57","lastDeviceName":"9-10栋5号门出","lastAreaName":null,"updateTime":"2025-08-01 17:03:57","departmentCode":"130120","departmentName":"130120","gender":2,"telephone":"13800138003","cacheKey":"student:status:SG000013","inDormitory":true,"statusDescription":"已归寝室","inOrOutDescription":"进入寝室","statsGroupKey":"stats:group:in_dormitory""[truncated 2 bytes]; line: 1, column: 344] (through reference chain: fastgatedemo.demo.dto.StudentStatusCacheDTO["cacheKey"]); nested exception is com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException: Unrecognized field "cacheKey" (class fastgatedemo.demo.dto.StudentStatusCacheDTO), not marked as ignorable (11 known properties: "personName", "gender", "personCode", "lastPassTime", "updateTime", "lastDeviceName", "departmentName", "lastAreaName", "departmentCode", "lastInOrOut", "telephone"])
 at [Source: (byte[])"["fastgatedemo.demo.dto.StudentStatusCacheDTO",{"personCode":"SG000013","personName":"李小红","lastInOrOut":1,"lastPassTime":"2025-08-01 17:03:57","lastDeviceName":"9-10栋5号门出","lastAreaName":null,"updateTime":"2025-08-01 17:03:57","departmentCode":"130120","departmentName":"130120","gender":2,"telephone":"13800138003","cacheKey":"student:status:SG000013","inDormitory":true,"statusDescription":"已归寝室","inOrOutDescription":"进入寝室","statsGroupKey":"stats:group:in_dormitory""[truncated 2 bytes]; line: 1, column: 344] (through reference chain: fastgatedemo.demo.dto.StudentStatusCacheDTO["cacheKey"])
2025-08-01 17:03:58.200 [http-nio-8080-exec-4] ERROR f.d.s.StudentStatusCacheService 103 获取学生状态缓存失败: personCode=SG000013, error=Could not read JSON: Unrecognized field "cacheKey" (class fastgatedemo.demo.dto.StudentStatusCacheDTO), not marked as ignorable (11 known properties: "personName", "gender", "personCode", "lastPassTime", "updateTime", "lastDeviceName", "departmentName", "lastAreaName", "departmentCode", "lastInOrOut", "telephone"])
 at [Source: (byte[])"["fastgatedemo.demo.dto.StudentStatusCacheDTO",{"personCode":"SG000013","personName":"李小红","lastInOrOut":1,"lastPassTime":"2025-08-01 17:03:58","lastDeviceName":"9-10栋5号门出","lastAreaName":null,"updateTime":"2025-08-01 17:03:58","departmentCode":"130120","departmentName":"130120","gender":2,"telephone":"13800138003","cacheKey":"student:status:SG000013","inDormitory":true,"statusDescription":"已归寝室","inOrOutDescription":"进入寝室","statsGroupKey":"stats:group:in_dormitory""[truncated 2 bytes]; line: 1, column: 344] (through reference chain: fastgatedemo.demo.dto.StudentStatusCacheDTO["cacheKey"]); nested exception is com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException: Unrecognized field "cacheKey" (class fastgatedemo.demo.dto.StudentStatusCacheDTO), not marked as ignorable (11 known properties: "personName", "gender", "personCode", "lastPassTime", "updateTime", "lastDeviceName", "departmentName", "lastAreaName", "departmentCode", "lastInOrOut", "telephone"])
 at [Source: (byte[])"["fastgatedemo.demo.dto.StudentStatusCacheDTO",{"personCode":"SG000013","personName":"李小红","lastInOrOut":1,"lastPassTime":"2025-08-01 17:03:58","lastDeviceName":"9-10栋5号门出","lastAreaName":null,"updateTime":"2025-08-01 17:03:58","departmentCode":"130120","departmentName":"130120","gender":2,"telephone":"13800138003","cacheKey":"student:status:SG000013","inDormitory":true,"statusDescription":"已归寝室","inOrOutDescription":"进入寝室","statsGroupKey":"stats:group:in_dormitory""[truncated 2 bytes]; line: 1, column: 344] (through reference chain: fastgatedemo.demo.dto.StudentStatusCacheDTO["cacheKey"])
2025-08-01 17:03:58.329 [http-nio-8080-exec-5] ERROR f.d.s.StudentStatusCacheService 103 获取学生状态缓存失败: personCode=SG000013, error=Could not read JSON: Unrecognized field "cacheKey" (class fastgatedemo.demo.dto.StudentStatusCacheDTO), not marked as ignorable (11 known properties: "personName", "gender", "personCode", "lastPassTime", "updateTime", "lastDeviceName", "departmentName", "lastAreaName", "departmentCode", "lastInOrOut", "telephone"])
 at [Source: (byte[])"["fastgatedemo.demo.dto.StudentStatusCacheDTO",{"personCode":"SG000013","personName":"李小红","lastInOrOut":1,"lastPassTime":"2025-08-01 17:03:58","lastDeviceName":"9-10栋5号门出","lastAreaName":null,"updateTime":"2025-08-01 17:03:58","departmentCode":"130120","departmentName":"130120","gender":2,"telephone":"13800138003","cacheKey":"student:status:SG000013","inDormitory":true,"statusDescription":"已归寝室","inOrOutDescription":"进入寝室","statsGroupKey":"stats:group:in_dormitory""[truncated 2 bytes]; line: 1, column: 344] (through reference chain: fastgatedemo.demo.dto.StudentStatusCacheDTO["cacheKey"]); nested exception is com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException: Unrecognized field "cacheKey" (class fastgatedemo.demo.dto.StudentStatusCacheDTO), not marked as ignorable (11 known properties: "personName", "gender", "personCode", "lastPassTime", "updateTime", "lastDeviceName", "departmentName", "lastAreaName", "departmentCode", "lastInOrOut", "telephone"])
 at [Source: (byte[])"["fastgatedemo.demo.dto.StudentStatusCacheDTO",{"personCode":"SG000013","personName":"李小红","lastInOrOut":1,"lastPassTime":"2025-08-01 17:03:58","lastDeviceName":"9-10栋5号门出","lastAreaName":null,"updateTime":"2025-08-01 17:03:58","departmentCode":"130120","departmentName":"130120","gender":2,"telephone":"13800138003","cacheKey":"student:status:SG000013","inDormitory":true,"statusDescription":"已归寝室","inOrOutDescription":"进入寝室","statsGroupKey":"stats:group:in_dormitory""[truncated 2 bytes]; line: 1, column: 344] (through reference chain: fastgatedemo.demo.dto.StudentStatusCacheDTO["cacheKey"])
2025-08-01 17:03:58.474 [http-nio-8080-exec-8] ERROR f.d.s.StudentStatusCacheService 103 获取学生状态缓存失败: personCode=SG000013, error=Could not read JSON: Unrecognized field "cacheKey" (class fastgatedemo.demo.dto.StudentStatusCacheDTO), not marked as ignorable (11 known properties: "personName", "gender", "personCode", "lastPassTime", "updateTime", "lastDeviceName", "departmentName", "lastAreaName", "departmentCode", "lastInOrOut", "telephone"])
 at [Source: (byte[])"["fastgatedemo.demo.dto.StudentStatusCacheDTO",{"personCode":"SG000013","personName":"李小红","lastInOrOut":1,"lastPassTime":"2025-08-01 17:03:58","lastDeviceName":"9-10栋5号门出","lastAreaName":null,"updateTime":"2025-08-01 17:03:58","departmentCode":"130120","departmentName":"130120","gender":2,"telephone":"13800138003","cacheKey":"student:status:SG000013","inDormitory":true,"statusDescription":"已归寝室","inOrOutDescription":"进入寝室","statsGroupKey":"stats:group:in_dormitory""[truncated 2 bytes]; line: 1, column: 344] (through reference chain: fastgatedemo.demo.dto.StudentStatusCacheDTO["cacheKey"]); nested exception is com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException: Unrecognized field "cacheKey" (class fastgatedemo.demo.dto.StudentStatusCacheDTO), not marked as ignorable (11 known properties: "personName", "gender", "personCode", "lastPassTime", "updateTime", "lastDeviceName", "departmentName", "lastAreaName", "departmentCode", "lastInOrOut", "telephone"])
 at [Source: (byte[])"["fastgatedemo.demo.dto.StudentStatusCacheDTO",{"personCode":"SG000013","personName":"李小红","lastInOrOut":1,"lastPassTime":"2025-08-01 17:03:58","lastDeviceName":"9-10栋5号门出","lastAreaName":null,"updateTime":"2025-08-01 17:03:58","departmentCode":"130120","departmentName":"130120","gender":2,"telephone":"13800138003","cacheKey":"student:status:SG000013","inDormitory":true,"statusDescription":"已归寝室","inOrOutDescription":"进入寝室","statsGroupKey":"stats:group:in_dormitory""[truncated 2 bytes]; line: 1, column: 344] (through reference chain: fastgatedemo.demo.dto.StudentStatusCacheDTO["cacheKey"])
2025-08-01 17:03:58.615 [http-nio-8080-exec-9] ERROR f.d.s.StudentStatusCacheService 103 获取学生状态缓存失败: personCode=SG000013, error=Could not read JSON: Unrecognized field "cacheKey" (class fastgatedemo.demo.dto.StudentStatusCacheDTO), not marked as ignorable (11 known properties: "personName", "gender", "personCode", "lastPassTime", "updateTime", "lastDeviceName", "departmentName", "lastAreaName", "departmentCode", "lastInOrOut", "telephone"])
 at [Source: (byte[])"["fastgatedemo.demo.dto.StudentStatusCacheDTO",{"personCode":"SG000013","personName":"李小红","lastInOrOut":1,"lastPassTime":"2025-08-01 17:03:58","lastDeviceName":"9-10栋5号门出","lastAreaName":null,"updateTime":"2025-08-01 17:03:58","departmentCode":"130120","departmentName":"130120","gender":2,"telephone":"13800138003","cacheKey":"student:status:SG000013","inDormitory":true,"statusDescription":"已归寝室","inOrOutDescription":"进入寝室","statsGroupKey":"stats:group:in_dormitory""[truncated 2 bytes]; line: 1, column: 344] (through reference chain: fastgatedemo.demo.dto.StudentStatusCacheDTO["cacheKey"]); nested exception is com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException: Unrecognized field "cacheKey" (class fastgatedemo.demo.dto.StudentStatusCacheDTO), not marked as ignorable (11 known properties: "personName", "gender", "personCode", "lastPassTime", "updateTime", "lastDeviceName", "departmentName", "lastAreaName", "departmentCode", "lastInOrOut", "telephone"])
 at [Source: (byte[])"["fastgatedemo.demo.dto.StudentStatusCacheDTO",{"personCode":"SG000013","personName":"李小红","lastInOrOut":1,"lastPassTime":"2025-08-01 17:03:58","lastDeviceName":"9-10栋5号门出","lastAreaName":null,"updateTime":"2025-08-01 17:03:58","departmentCode":"130120","departmentName":"130120","gender":2,"telephone":"13800138003","cacheKey":"student:status:SG000013","inDormitory":true,"statusDescription":"已归寝室","inOrOutDescription":"进入寝室","statsGroupKey":"stats:group:in_dormitory""[truncated 2 bytes]; line: 1, column: 344] (through reference chain: fastgatedemo.demo.dto.StudentStatusCacheDTO["cacheKey"])
2025-08-01 17:03:58.738 [http-nio-8080-exec-10] ERROR f.d.s.StudentStatusCacheService 103 获取学生状态缓存失败: personCode=SG000013, error=Could not read JSON: Unrecognized field "cacheKey" (class fastgatedemo.demo.dto.StudentStatusCacheDTO), not marked as ignorable (11 known properties: "personName", "gender", "personCode", "lastPassTime", "updateTime", "lastDeviceName", "departmentName", "lastAreaName", "departmentCode", "lastInOrOut", "telephone"])
 at [Source: (byte[])"["fastgatedemo.demo.dto.StudentStatusCacheDTO",{"personCode":"SG000013","personName":"李小红","lastInOrOut":1,"lastPassTime":"2025-08-01 17:03:58","lastDeviceName":"9-10栋5号门出","lastAreaName":null,"updateTime":"2025-08-01 17:03:58","departmentCode":"130120","departmentName":"130120","gender":2,"telephone":"13800138003","cacheKey":"student:status:SG000013","inDormitory":true,"statusDescription":"已归寝室","inOrOutDescription":"进入寝室","statsGroupKey":"stats:group:in_dormitory""[truncated 2 bytes]; line: 1, column: 344] (through reference chain: fastgatedemo.demo.dto.StudentStatusCacheDTO["cacheKey"]); nested exception is com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException: Unrecognized field "cacheKey" (class fastgatedemo.demo.dto.StudentStatusCacheDTO), not marked as ignorable (11 known properties: "personName", "gender", "personCode", "lastPassTime", "updateTime", "lastDeviceName", "departmentName", "lastAreaName", "departmentCode", "lastInOrOut", "telephone"])
 at [Source: (byte[])"["fastgatedemo.demo.dto.StudentStatusCacheDTO",{"personCode":"SG000013","personName":"李小红","lastInOrOut":1,"lastPassTime":"2025-08-01 17:03:58","lastDeviceName":"9-10栋5号门出","lastAreaName":null,"updateTime":"2025-08-01 17:03:58","departmentCode":"130120","departmentName":"130120","gender":2,"telephone":"13800138003","cacheKey":"student:status:SG000013","inDormitory":true,"statusDescription":"已归寝室","inOrOutDescription":"进入寝室","statsGroupKey":"stats:group:in_dormitory""[truncated 2 bytes]; line: 1, column: 344] (through reference chain: fastgatedemo.demo.dto.StudentStatusCacheDTO["cacheKey"])
2025-08-01 17:03:58.871 [http-nio-8080-exec-1] ERROR f.d.s.StudentStatusCacheService 103 获取学生状态缓存失败: personCode=SG000013, error=Could not read JSON: Unrecognized field "cacheKey" (class fastgatedemo.demo.dto.StudentStatusCacheDTO), not marked as ignorable (11 known properties: "personName", "gender", "personCode", "lastPassTime", "updateTime", "lastDeviceName", "departmentName", "lastAreaName", "departmentCode", "lastInOrOut", "telephone"])
 at [Source: (byte[])"["fastgatedemo.demo.dto.StudentStatusCacheDTO",{"personCode":"SG000013","personName":"李小红","lastInOrOut":1,"lastPassTime":"2025-08-01 17:03:58","lastDeviceName":"9-10栋5号门出","lastAreaName":null,"updateTime":"2025-08-01 17:03:58","departmentCode":"130120","departmentName":"130120","gender":2,"telephone":"13800138003","cacheKey":"student:status:SG000013","inDormitory":true,"statusDescription":"已归寝室","inOrOutDescription":"进入寝室","statsGroupKey":"stats:group:in_dormitory""[truncated 2 bytes]; line: 1, column: 344] (through reference chain: fastgatedemo.demo.dto.StudentStatusCacheDTO["cacheKey"]); nested exception is com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException: Unrecognized field "cacheKey" (class fastgatedemo.demo.dto.StudentStatusCacheDTO), not marked as ignorable (11 known properties: "personName", "gender", "personCode", "lastPassTime", "updateTime", "lastDeviceName", "departmentName", "lastAreaName", "departmentCode", "lastInOrOut", "telephone"])
 at [Source: (byte[])"["fastgatedemo.demo.dto.StudentStatusCacheDTO",{"personCode":"SG000013","personName":"李小红","lastInOrOut":1,"lastPassTime":"2025-08-01 17:03:58","lastDeviceName":"9-10栋5号门出","lastAreaName":null,"updateTime":"2025-08-01 17:03:58","departmentCode":"130120","departmentName":"130120","gender":2,"telephone":"13800138003","cacheKey":"student:status:SG000013","inDormitory":true,"statusDescription":"已归寝室","inOrOutDescription":"进入寝室","statsGroupKey":"stats:group:in_dormitory""[truncated 2 bytes]; line: 1, column: 344] (through reference chain: fastgatedemo.demo.dto.StudentStatusCacheDTO["cacheKey"])
2025-08-01 17:04:01.819 [SockJS-10] ERROR f.d.s.AsyncBatchPersistenceService 228 批量插入表tbl_access_control_record_2025_08_01失败: PreparedStatementCallback; bad SQL grammar [INSERT INTO tbl_access_control_record_2025_08_01 (person_code, person_name, device_code, device_name, place_code, place_name, area_code, area_name, inorout, pass_time, record_date, record_time, match_confidence, device_ip, temperature, update_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)]; nested exception is org.postgresql.util.PSQLException: ERROR: relation "tbl_access_control_record_2025_08_01" does not exist
  位置：13
2025-08-01 17:04:01.826 [SockJS-10] ERROR f.d.s.AsyncBatchPersistenceService 137 批量写入表tbl_access_control_record_2025_08_01失败: PreparedStatementCallback; bad SQL grammar [INSERT INTO tbl_access_control_record_2025_08_01 (person_code, person_name, device_code, device_name, place_code, place_name, area_code, area_name, inorout, pass_time, record_date, record_time, match_confidence, device_ip, temperature, update_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)]; nested exception is org.postgresql.util.PSQLException: ERROR: relation "tbl_access_control_record_2025_08_01" does not exist
  位置：13

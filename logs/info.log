2025-08-01 11:17:30.218 [main] INFO  f.d.StartMain 50 Starting StartMain on PC-20230626VQFC with PID 24412 (E:\Jkxy\demo\target\classes started by Administrator in E:\Jkxy\demo)
2025-08-01 11:17:30.224 [main] INFO  f.d.StartMain 684 The following profiles are active: dev
2025-08-01 11:17:32.474 [main] INFO  o.a.c.h.Http11NioProtocol 180 Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 11:17:32.489 [main] INFO  o.a.c.c.StandardService 180 Starting service [Tomcat]
2025-08-01 11:17:32.490 [main] INFO  o.a.c.c.StandardEngine 180 Starting Servlet Engine: Apache Tomcat/8.5.34
2025-08-01 11:17:32.501 [localhost-startStop-1] INFO  o.a.c.c.AprLifecycleListener 180 The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: [D:\Environment\java8\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;D:\Environment\ZenTao\bin\git\cmd;D:\Environment\ZenTao\bin\sliksvn;D:\Environment\ZenTao\zbox\nssm\win32;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\Tools\VMware\bin\;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;D:\Environment\Git\cmd;E:\nodejsFile\node_global;D:\Environment\redis\;D:\Environment\apache-maven-3.5.4\bin;D:\Tools\xshell\;D:\Environment\nvm;D:\Environment\nodejs;C:\Program Files\dotnet\;D:\Environment\ffmpeg-5.1.2-full_build\bin;D:\Environment\nc;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\Tools\IntelliJ IDEA 2021.2.1\bin;D:\Tools\WebStorm 2021.2.1\bin;C:\Users\<USER>\AppData\Roaming\npm;D:\Environment\nvm;D:\Environment\nodejs;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\Tools\IntelliJ IDEA 2021.2.1\bin;D:\Tools\WebStorm 2021.2.1\bin;C:\Users\<USER>\AppData\Roaming\npm;D:\E;C:\Program Files (x86)\PuTTY\;D:\Tools\Microsoft VS Code\bin;D:\Mcp-Servers\chuzhi;C:\Program Files\Pandoc\;C:\Windows\system32\config\systemprofile\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\.local\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;D:\Tools\IntelliJ IDEA 2025.1.3\bin;D:\Tools\WebStorm 2025.1.3\bin;D:\Tools\DataGrip 2024.2.2\bin;D:\Soft\Windsurf\bin;D:\Tools\PyCharm 2025.1.1\bin;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;D:\Tools\cursor\resources\app\bin;D:\Tools\Kiro\bin;.]
2025-08-01 11:17:32.596 [localhost-startStop-1] INFO  o.a.c.c.C.[.[.[/] 180 Initializing Spring embedded WebApplicationContext
2025-08-01 11:17:33.607 [localhost-startStop-1] INFO  c.b.d.d.DynamicRoutingDataSource 154 dynamic-datasource - add a datasource named [fastgate] success
2025-08-01 11:17:33.607 [localhost-startStop-1] INFO  c.b.d.d.DynamicRoutingDataSource 154 dynamic-datasource - add a datasource named [master] success
2025-08-01 11:17:33.608 [localhost-startStop-1] INFO  c.b.d.d.DynamicRoutingDataSource 234 dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-08-01 11:17:36.112 [main] INFO  f.d.c.DynamicDataSourceConfig 25 动态数据源配置初始化完成 - 使用dynamic-datasource-spring-boot-starter
2025-08-01 11:17:37.650 [main] INFO  f.d.s.ScheduledTaskManagementService 99 定时任务信息初始化完成
2025-08-01 11:17:39.341 [main] INFO  o.a.c.h.Http11NioProtocol 180 Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 11:17:39.362 [main] INFO  o.a.t.u.n.NioSelectorPool 180 Using a shared selector for servlet write/read
2025-08-01 11:17:39.387 [main] INFO  f.d.StartMain 59 Started StartMain in 9.617 seconds (JVM running for 11.394)
2025-08-01 11:17:40.307 [RMI TCP Connection(3)-*******] INFO  i.l.c.EpollProvider 101 Starting without optional epoll library
2025-08-01 11:17:40.313 [RMI TCP Connection(3)-*******] INFO  i.l.c.KqueueProvider 101 Starting without optional kqueue library
2025-08-01 11:17:40.366 [RMI TCP Connection(6)-*******] INFO  o.a.c.c.C.[.[.[/] 180 Initializing Spring FrameworkServlet 'dispatcherServlet'
2025-08-01 11:27:42.285 [Thread-24] INFO  c.b.d.d.DynamicRoutingDataSource 211 dynamic-datasource start closing ....
2025-08-01 11:27:42.288 [Thread-24] INFO  c.b.d.d.DynamicRoutingDataSource 215 dynamic-datasource all closed success,bye
2025-08-01 11:27:48.549 [main] INFO  f.d.StartMain 50 Starting StartMain on PC-20230626VQFC with PID 19904 (E:\Jkxy\demo\target\classes started by Administrator in E:\Jkxy\demo)
2025-08-01 11:27:48.556 [main] INFO  f.d.StartMain 684 The following profiles are active: dev
2025-08-01 11:27:50.518 [main] INFO  o.a.c.h.Http11NioProtocol 180 Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 11:27:50.532 [main] INFO  o.a.c.c.StandardService 180 Starting service [Tomcat]
2025-08-01 11:27:50.533 [main] INFO  o.a.c.c.StandardEngine 180 Starting Servlet Engine: Apache Tomcat/8.5.34
2025-08-01 11:27:50.544 [localhost-startStop-1] INFO  o.a.c.c.AprLifecycleListener 180 The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: [D:\Environment\java8\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;D:\Environment\ZenTao\bin\git\cmd;D:\Environment\ZenTao\bin\sliksvn;D:\Environment\ZenTao\zbox\nssm\win32;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\Tools\VMware\bin\;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;D:\Environment\Git\cmd;E:\nodejsFile\node_global;D:\Environment\redis\;D:\Environment\apache-maven-3.5.4\bin;D:\Tools\xshell\;D:\Environment\nvm;D:\Environment\nodejs;C:\Program Files\dotnet\;D:\Environment\ffmpeg-5.1.2-full_build\bin;D:\Environment\nc;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\Tools\IntelliJ IDEA 2021.2.1\bin;D:\Tools\WebStorm 2021.2.1\bin;C:\Users\<USER>\AppData\Roaming\npm;D:\Environment\nvm;D:\Environment\nodejs;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\Tools\IntelliJ IDEA 2021.2.1\bin;D:\Tools\WebStorm 2021.2.1\bin;C:\Users\<USER>\AppData\Roaming\npm;D:\E;C:\Program Files (x86)\PuTTY\;D:\Tools\Microsoft VS Code\bin;D:\Mcp-Servers\chuzhi;C:\Program Files\Pandoc\;C:\Windows\system32\config\systemprofile\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\.local\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;D:\Tools\IntelliJ IDEA 2025.1.3\bin;D:\Tools\WebStorm 2025.1.3\bin;D:\Tools\DataGrip 2024.2.2\bin;D:\Soft\Windsurf\bin;D:\Tools\PyCharm 2025.1.1\bin;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;D:\Tools\cursor\resources\app\bin;D:\Tools\Kiro\bin;.]
2025-08-01 11:27:50.637 [localhost-startStop-1] INFO  o.a.c.c.C.[.[.[/] 180 Initializing Spring embedded WebApplicationContext
2025-08-01 11:27:51.601 [localhost-startStop-1] INFO  c.b.d.d.DynamicRoutingDataSource 154 dynamic-datasource - add a datasource named [fastgate] success
2025-08-01 11:27:51.601 [localhost-startStop-1] INFO  c.b.d.d.DynamicRoutingDataSource 154 dynamic-datasource - add a datasource named [master] success
2025-08-01 11:27:51.602 [localhost-startStop-1] INFO  c.b.d.d.DynamicRoutingDataSource 234 dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-08-01 11:27:54.020 [main] INFO  f.d.c.DynamicDataSourceConfig 25 动态数据源配置初始化完成 - 使用dynamic-datasource-spring-boot-starter
2025-08-01 11:27:55.459 [main] INFO  f.d.s.ScheduledTaskManagementService 99 定时任务信息初始化完成
2025-08-01 11:27:57.087 [main] INFO  o.a.c.h.Http11NioProtocol 180 Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 11:27:57.110 [main] INFO  o.a.t.u.n.NioSelectorPool 180 Using a shared selector for servlet write/read
2025-08-01 11:27:57.132 [main] INFO  f.d.StartMain 59 Started StartMain in 8.981 seconds (JVM running for 10.583)
2025-08-01 11:27:57.955 [RMI TCP Connection(5)-*******] INFO  o.a.c.c.C.[.[.[/] 180 Initializing Spring FrameworkServlet 'dispatcherServlet'
2025-08-01 11:27:58.056 [RMI TCP Connection(3)-*******] INFO  i.l.c.EpollProvider 101 Starting without optional epoll library
2025-08-01 11:27:58.062 [RMI TCP Connection(3)-*******] INFO  i.l.c.KqueueProvider 101 Starting without optional kqueue library
2025-08-01 13:48:57.566 [http-nio-8080-exec-1] INFO  f.d.c.DashboardApiController 39 获取楼栋列表请求
2025-08-01 13:48:57.851 [http-nio-8080-exec-1] INFO  f.d.s.DormitoryService 507 查询楼栋列表成功，共22个楼栋
2025-08-01 13:48:57.852 [http-nio-8080-exec-1] INFO  f.d.c.DashboardApiController 57 获取楼栋列表成功，共23个楼栋
2025-08-01 13:48:58.379 [http-nio-8080-exec-2] INFO  f.d.c.DashboardController 309 统一大屏数据查询: buildingCodes=null, forceRefresh=false
2025-08-01 13:48:58.380 [http-nio-8080-exec-2] INFO  f.d.c.DashboardController 326 查询全部统计数据
2025-08-01 13:48:58.421 [http-nio-8080-exec-2] INFO  f.d.s.CachedDormitoryStatsService 62 开始获取基于已分配宿舍学生的统计信息（优化版本）
2025-08-01 13:48:58.627 [http-nio-8080-exec-2] INFO  f.d.s.CachedDormitoryStatsService 334 开始计算基于已分配宿舍学生的统计信息
2025-08-01 13:48:58.675 [http-nio-8080-exec-2] INFO  f.d.s.CachedDormitoryStatsService 342 已分配宿舍的学生总数: 0
2025-08-01 13:48:58.766 [http-nio-8080-exec-2] INFO  f.d.s.CachedDormitoryStatsService 84 基于已分配宿舍学生的统计信息计算并缓存完成，耗时: 345ms
2025-08-01 13:48:58.767 [http-nio-8080-exec-2] INFO  f.d.c.DashboardController 373 统一大屏数据查询成功: 楼栋=null, 总人数=0, 在寝=0, 外出=0, 无记录=0, 归寝率=0.0%, 刷新=false, 耗时=387ms
2025-08-01 13:48:59.286 [http-nio-8080-exec-4] INFO  f.d.c.WebSocketStatusController 46 WebSocket状态查询: 连接数=0
2025-08-01 13:48:59.310 [http-nio-8080-exec-3] INFO  f.d.s.CachedDormitoryStatsService 62 开始获取基于已分配宿舍学生的统计信息（优化版本）
2025-08-01 13:48:59.336 [http-nio-8080-exec-3] INFO  f.d.s.CachedDormitoryStatsService 70 从缓存获取统计信息成功，耗时: 27ms
2025-08-01 13:48:59.685 [http-nio-8080-exec-5] INFO  f.d.w.DashboardWebSocketHandler 39 WebSocket连接建立: sessionId=63292d86-ec14-58a7-3565-c5dd92207a88, 当前连接数: 1
2025-08-01 13:49:16.583 [http-nio-8080-exec-6] INFO  f.d.w.DashboardWebSocketHandler 66 WebSocket连接关闭: sessionId=63292d86-ec14-58a7-3565-c5dd92207a88, status=CloseStatus[code=1000, reason=手动断开], 当前连接数: 0
2025-08-01 13:49:16.614 [http-nio-8080-exec-8] INFO  f.d.c.StudentDormitoryQueryController 189 获取宿舍楼列表
2025-08-01 13:49:16.628 [http-nio-8080-exec-8] INFO  f.d.s.StudentDormitoryQueryService 185 获取宿舍楼列表
2025-08-01 13:49:16.637 [http-nio-8080-exec-8] INFO  f.d.s.StudentDormitoryQueryService 206 获取到9个宿舍楼
2025-08-01 13:49:16.638 [http-nio-8080-exec-8] INFO  f.d.c.StudentDormitoryQueryController 201 获取宿舍楼列表成功，共9个楼栋
2025-08-01 13:49:16.640 [http-nio-8080-exec-7] INFO  f.d.c.StudentDormitoryQueryController 53 查询已分配宿舍学生状态，页码: 0, 大小: 20, 姓名: null, 楼栋: null, 状态: null
2025-08-01 13:49:16.641 [http-nio-8080-exec-7] INFO  f.d.s.StudentDormitoryQueryService 55 开始查询已分配宿舍学生状态: personName=null, buildingCode=null, dormitoryStatus=null
2025-08-01 13:49:16.644 [http-nio-8080-exec-7] INFO  f.d.s.StudentDormitoryQueryService 62 没有找到符合条件的已分配宿舍学生
2025-08-01 13:49:16.661 [http-nio-8080-exec-7] INFO  f.d.c.StudentDormitoryQueryController 102 查询已分配宿舍学生状态成功，总数: 0, 当前页: 0
2025-08-01 13:49:17.961 [http-nio-8080-exec-9] INFO  f.d.c.StudentDormitoryQueryController 53 查询已分配宿舍学生状态，页码: 0, 大小: 20, 姓名: null, 楼栋: null, 状态: null
2025-08-01 13:49:17.962 [http-nio-8080-exec-9] INFO  f.d.s.StudentDormitoryQueryService 55 开始查询已分配宿舍学生状态: personName=null, buildingCode=null, dormitoryStatus=null
2025-08-01 13:49:17.965 [http-nio-8080-exec-9] INFO  f.d.s.StudentDormitoryQueryService 62 没有找到符合条件的已分配宿舍学生
2025-08-01 13:49:17.966 [http-nio-8080-exec-9] INFO  f.d.c.StudentDormitoryQueryController 102 查询已分配宿舍学生状态成功，总数: 0, 当前页: 0
2025-08-01 13:49:18.692 [http-nio-8080-exec-10] INFO  f.d.c.DashboardApiController 39 获取楼栋列表请求
2025-08-01 13:49:18.695 [http-nio-8080-exec-10] INFO  f.d.s.DormitoryService 507 查询楼栋列表成功，共22个楼栋
2025-08-01 13:49:18.695 [http-nio-8080-exec-10] INFO  f.d.c.DashboardApiController 57 获取楼栋列表成功，共23个楼栋
2025-08-01 13:49:18.718 [http-nio-8080-exec-1] INFO  f.d.c.DashboardController 309 统一大屏数据查询: buildingCodes=null, forceRefresh=false
2025-08-01 13:49:18.719 [http-nio-8080-exec-1] INFO  f.d.c.DashboardController 326 查询全部统计数据
2025-08-01 13:49:18.719 [http-nio-8080-exec-1] INFO  f.d.s.CachedDormitoryStatsService 62 开始获取基于已分配宿舍学生的统计信息（优化版本）
2025-08-01 13:49:18.721 [http-nio-8080-exec-1] INFO  f.d.s.CachedDormitoryStatsService 70 从缓存获取统计信息成功，耗时: 2ms
2025-08-01 13:49:18.722 [http-nio-8080-exec-1] INFO  f.d.c.DashboardController 373 统一大屏数据查询成功: 楼栋=null, 总人数=0, 在寝=0, 外出=0, 无记录=0, 归寝率=0.0%, 刷新=false, 耗时=3ms
2025-08-01 13:49:19.238 [http-nio-8080-exec-2] INFO  f.d.c.WebSocketStatusController 46 WebSocket状态查询: 连接数=0
2025-08-01 13:49:19.247 [http-nio-8080-exec-4] INFO  f.d.s.CachedDormitoryStatsService 62 开始获取基于已分配宿舍学生的统计信息（优化版本）
2025-08-01 13:49:19.248 [http-nio-8080-exec-4] INFO  f.d.s.CachedDormitoryStatsService 70 从缓存获取统计信息成功，耗时: 1ms
2025-08-01 13:49:19.264 [http-nio-8080-exec-3] INFO  f.d.w.DashboardWebSocketHandler 39 WebSocket连接建立: sessionId=d9984f0b-0730-1c1f-af25-b30e72eb7e21, 当前连接数: 1
2025-08-01 14:12:58.955 [http-nio-8080-exec-9] INFO  f.d.w.DashboardWebSocketHandler 66 WebSocket连接关闭: sessionId=d9984f0b-0730-1c1f-af25-b30e72eb7e21, status=CloseStatus[code=1000, reason=手动断开], 当前连接数: 0
2025-08-01 14:13:09.997 [http-nio-8080-exec-2] INFO  f.d.c.StudentDormitoryQueryController 189 获取宿舍楼列表
2025-08-01 14:13:09.997 [http-nio-8080-exec-4] INFO  f.d.c.StudentDormitoryQueryController 53 查询已分配宿舍学生状态，页码: 0, 大小: 20, 姓名: null, 楼栋: null, 状态: null
2025-08-01 14:13:09.997 [http-nio-8080-exec-4] INFO  f.d.s.StudentDormitoryQueryService 55 开始查询已分配宿舍学生状态: personName=null, buildingCode=null, dormitoryStatus=null
2025-08-01 14:13:09.997 [http-nio-8080-exec-2] INFO  f.d.s.StudentDormitoryQueryService 185 获取宿舍楼列表
2025-08-01 14:13:10.003 [http-nio-8080-exec-4] INFO  f.d.s.StudentDormitoryQueryService 62 没有找到符合条件的已分配宿舍学生
2025-08-01 14:13:10.003 [http-nio-8080-exec-4] INFO  f.d.c.StudentDormitoryQueryController 102 查询已分配宿舍学生状态成功，总数: 0, 当前页: 0
2025-08-01 14:13:10.004 [http-nio-8080-exec-2] INFO  f.d.s.StudentDormitoryQueryService 206 获取到9个宿舍楼
2025-08-01 14:13:10.005 [http-nio-8080-exec-2] INFO  f.d.c.StudentDormitoryQueryController 201 获取宿舍楼列表成功，共9个楼栋
2025-08-01 14:13:12.360 [http-nio-8080-exec-3] INFO  f.d.c.StudentDormitoryQueryController 53 查询已分配宿舍学生状态，页码: 0, 大小: 20, 姓名: null, 楼栋: null, 状态: null
2025-08-01 14:13:12.360 [http-nio-8080-exec-3] INFO  f.d.s.StudentDormitoryQueryService 55 开始查询已分配宿舍学生状态: personName=null, buildingCode=null, dormitoryStatus=null
2025-08-01 14:13:12.365 [http-nio-8080-exec-3] INFO  f.d.s.StudentDormitoryQueryService 62 没有找到符合条件的已分配宿舍学生
2025-08-01 14:13:12.365 [http-nio-8080-exec-3] INFO  f.d.c.StudentDormitoryQueryController 102 查询已分配宿舍学生状态成功，总数: 0, 当前页: 0
2025-08-01 14:13:16.909 [http-nio-8080-exec-5] INFO  f.d.c.StudentDormitoryQueryController 189 获取宿舍楼列表
2025-08-01 14:13:16.910 [http-nio-8080-exec-5] INFO  f.d.s.StudentDormitoryQueryService 185 获取宿舍楼列表
2025-08-01 14:13:16.910 [http-nio-8080-exec-6] INFO  f.d.c.StudentDormitoryQueryController 53 查询已分配宿舍学生状态，页码: 0, 大小: 20, 姓名: null, 楼栋: null, 状态: null
2025-08-01 14:13:16.911 [http-nio-8080-exec-6] INFO  f.d.s.StudentDormitoryQueryService 55 开始查询已分配宿舍学生状态: personName=null, buildingCode=null, dormitoryStatus=null
2025-08-01 14:13:16.912 [http-nio-8080-exec-6] INFO  f.d.s.StudentDormitoryQueryService 62 没有找到符合条件的已分配宿舍学生
2025-08-01 14:13:16.913 [http-nio-8080-exec-6] INFO  f.d.c.StudentDormitoryQueryController 102 查询已分配宿舍学生状态成功，总数: 0, 当前页: 0
2025-08-01 14:13:16.914 [http-nio-8080-exec-5] INFO  f.d.s.StudentDormitoryQueryService 206 获取到9个宿舍楼
2025-08-01 14:13:16.915 [http-nio-8080-exec-5] INFO  f.d.c.StudentDormitoryQueryController 201 获取宿舍楼列表成功，共9个楼栋
2025-08-01 14:16:24.981 [http-nio-8080-exec-8] INFO  f.d.c.StudentDormitoryQueryController 53 查询已分配宿舍学生状态，页码: 0, 大小: 20, 姓名: null, 楼栋: null, 状态: null
2025-08-01 14:16:24.981 [http-nio-8080-exec-8] INFO  f.d.s.StudentDormitoryQueryService 55 开始查询已分配宿舍学生状态: personName=null, buildingCode=null, dormitoryStatus=null
2025-08-01 14:16:24.986 [http-nio-8080-exec-8] INFO  f.d.s.StudentDormitoryQueryService 62 没有找到符合条件的已分配宿舍学生
2025-08-01 14:16:24.987 [http-nio-8080-exec-8] INFO  f.d.c.StudentDormitoryQueryController 102 查询已分配宿舍学生状态成功，总数: 0, 当前页: 0
2025-08-01 14:29:29.014 [Thread-23] INFO  c.b.d.d.DynamicRoutingDataSource 211 dynamic-datasource start closing ....
2025-08-01 14:29:29.016 [Thread-23] INFO  c.b.d.d.DynamicRoutingDataSource 215 dynamic-datasource all closed success,bye
2025-08-01 14:30:52.730 [main] INFO  f.d.StartMain 50 Starting StartMain on PC-20230626VQFC with PID 2620 (E:\Jkxy\demo\target\classes started by Administrator in E:\Jkxy\demo)
2025-08-01 14:30:52.741 [main] INFO  f.d.StartMain 684 The following profiles are active: dev
2025-08-01 14:30:54.583 [main] INFO  o.a.c.h.Http11NioProtocol 180 Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 14:30:54.598 [main] INFO  o.a.c.c.StandardService 180 Starting service [Tomcat]
2025-08-01 14:30:54.598 [main] INFO  o.a.c.c.StandardEngine 180 Starting Servlet Engine: Apache Tomcat/8.5.34
2025-08-01 14:30:54.611 [localhost-startStop-1] INFO  o.a.c.c.AprLifecycleListener 180 The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: [D:\Environment\java8\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;D:\Environment\ZenTao\bin\git\cmd;D:\Environment\ZenTao\bin\sliksvn;D:\Environment\ZenTao\zbox\nssm\win32;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\Tools\VMware\bin\;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;D:\Environment\Git\cmd;E:\nodejsFile\node_global;D:\Environment\redis\;D:\Environment\apache-maven-3.5.4\bin;D:\Tools\xshell\;D:\Environment\nvm;D:\Environment\nodejs;C:\Program Files\dotnet\;D:\Environment\ffmpeg-5.1.2-full_build\bin;D:\Environment\nc;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\Tools\IntelliJ IDEA 2021.2.1\bin;D:\Tools\WebStorm 2021.2.1\bin;C:\Users\<USER>\AppData\Roaming\npm;D:\Environment\nvm;D:\Environment\nodejs;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\Tools\IntelliJ IDEA 2021.2.1\bin;D:\Tools\WebStorm 2021.2.1\bin;C:\Users\<USER>\AppData\Roaming\npm;D:\E;C:\Program Files (x86)\PuTTY\;D:\Tools\Microsoft VS Code\bin;D:\Mcp-Servers\chuzhi;C:\Program Files\Pandoc\;C:\Windows\system32\config\systemprofile\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\.local\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;D:\Tools\IntelliJ IDEA 2025.1.3\bin;D:\Tools\WebStorm 2025.1.3\bin;D:\Tools\DataGrip 2024.2.2\bin;D:\Soft\Windsurf\bin;D:\Tools\PyCharm 2025.1.1\bin;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;D:\Tools\cursor\resources\app\bin;D:\Tools\Kiro\bin;.]
2025-08-01 14:30:54.721 [localhost-startStop-1] INFO  o.a.c.c.C.[.[.[/] 180 Initializing Spring embedded WebApplicationContext
2025-08-01 14:30:55.884 [localhost-startStop-1] INFO  c.b.d.d.DynamicRoutingDataSource 154 dynamic-datasource - add a datasource named [fastgate] success
2025-08-01 14:30:55.885 [localhost-startStop-1] INFO  c.b.d.d.DynamicRoutingDataSource 154 dynamic-datasource - add a datasource named [master] success
2025-08-01 14:30:55.886 [localhost-startStop-1] INFO  c.b.d.d.DynamicRoutingDataSource 234 dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-08-01 14:30:58.371 [main] INFO  f.d.c.DynamicDataSourceConfig 25 动态数据源配置初始化完成 - 使用dynamic-datasource-spring-boot-starter
2025-08-01 14:30:59.858 [main] INFO  f.d.s.ScheduledTaskManagementService 99 定时任务信息初始化完成
2025-08-01 14:31:01.448 [main] INFO  o.a.c.h.Http11NioProtocol 180 Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 14:31:01.480 [main] INFO  o.a.t.u.n.NioSelectorPool 180 Using a shared selector for servlet write/read
2025-08-01 14:31:01.504 [main] INFO  f.d.StartMain 59 Started StartMain in 9.2 seconds (JVM running for 11.294)
2025-08-01 14:31:02.429 [RMI TCP Connection(6)-*******] INFO  o.a.c.c.C.[.[.[/] 180 Initializing Spring FrameworkServlet 'dispatcherServlet'
2025-08-01 14:31:02.670 [RMI TCP Connection(5)-*******] INFO  i.l.c.EpollProvider 101 Starting without optional epoll library
2025-08-01 14:31:02.674 [RMI TCP Connection(5)-*******] INFO  i.l.c.KqueueProvider 101 Starting without optional kqueue library
2025-08-01 14:39:29.407 [Thread-23] INFO  c.b.d.d.DynamicRoutingDataSource 211 dynamic-datasource start closing ....
2025-08-01 14:39:29.410 [Thread-23] INFO  c.b.d.d.DynamicRoutingDataSource 215 dynamic-datasource all closed success,bye
2025-08-01 14:39:37.218 [main] INFO  f.d.StartMain 50 Starting StartMain on PC-20230626VQFC with PID 16544 (E:\Jkxy\demo\target\classes started by Administrator in E:\Jkxy\demo)
2025-08-01 14:39:37.225 [main] INFO  f.d.StartMain 684 The following profiles are active: dev
2025-08-01 14:39:38.806 [main] INFO  o.a.c.h.Http11NioProtocol 180 Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 14:39:38.818 [main] INFO  o.a.c.c.StandardService 180 Starting service [Tomcat]
2025-08-01 14:39:38.818 [main] INFO  o.a.c.c.StandardEngine 180 Starting Servlet Engine: Apache Tomcat/8.5.34
2025-08-01 14:39:38.829 [localhost-startStop-1] INFO  o.a.c.c.AprLifecycleListener 180 The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: [D:\Environment\java8\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;D:\Environment\ZenTao\bin\git\cmd;D:\Environment\ZenTao\bin\sliksvn;D:\Environment\ZenTao\zbox\nssm\win32;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\Tools\VMware\bin\;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;D:\Environment\Git\cmd;E:\nodejsFile\node_global;D:\Environment\redis\;D:\Environment\apache-maven-3.5.4\bin;D:\Tools\xshell\;D:\Environment\nvm;D:\Environment\nodejs;C:\Program Files\dotnet\;D:\Environment\ffmpeg-5.1.2-full_build\bin;D:\Environment\nc;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\Tools\IntelliJ IDEA 2021.2.1\bin;D:\Tools\WebStorm 2021.2.1\bin;C:\Users\<USER>\AppData\Roaming\npm;D:\Environment\nvm;D:\Environment\nodejs;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\Tools\IntelliJ IDEA 2021.2.1\bin;D:\Tools\WebStorm 2021.2.1\bin;C:\Users\<USER>\AppData\Roaming\npm;D:\E;C:\Program Files (x86)\PuTTY\;D:\Tools\Microsoft VS Code\bin;D:\Mcp-Servers\chuzhi;C:\Program Files\Pandoc\;C:\Windows\system32\config\systemprofile\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\.local\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;D:\Tools\IntelliJ IDEA 2025.1.3\bin;D:\Tools\WebStorm 2025.1.3\bin;D:\Tools\DataGrip 2024.2.2\bin;D:\Soft\Windsurf\bin;D:\Tools\PyCharm 2025.1.1\bin;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;D:\Tools\cursor\resources\app\bin;D:\Tools\Kiro\bin;.]
2025-08-01 14:39:38.915 [localhost-startStop-1] INFO  o.a.c.c.C.[.[.[/] 180 Initializing Spring embedded WebApplicationContext
2025-08-01 14:39:39.806 [localhost-startStop-1] INFO  c.b.d.d.DynamicRoutingDataSource 154 dynamic-datasource - add a datasource named [fastgate] success
2025-08-01 14:39:39.806 [localhost-startStop-1] INFO  c.b.d.d.DynamicRoutingDataSource 154 dynamic-datasource - add a datasource named [master] success
2025-08-01 14:39:39.806 [localhost-startStop-1] INFO  c.b.d.d.DynamicRoutingDataSource 234 dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-08-01 14:39:41.981 [main] INFO  f.d.c.DynamicDataSourceConfig 25 动态数据源配置初始化完成 - 使用dynamic-datasource-spring-boot-starter
2025-08-01 14:39:43.279 [main] INFO  f.d.s.ScheduledTaskManagementService 99 定时任务信息初始化完成
2025-08-01 14:39:44.751 [main] INFO  o.a.c.h.Http11NioProtocol 180 Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 14:39:44.771 [main] INFO  o.a.t.u.n.NioSelectorPool 180 Using a shared selector for servlet write/read
2025-08-01 14:39:44.791 [main] INFO  f.d.StartMain 59 Started StartMain in 7.943 seconds (JVM running for 9.24)
2025-08-01 14:39:46.081 [RMI TCP Connection(6)-*******] INFO  o.a.c.c.C.[.[.[/] 180 Initializing Spring FrameworkServlet 'dispatcherServlet'
2025-08-01 14:39:46.330 [RMI TCP Connection(3)-*******] INFO  i.l.c.EpollProvider 101 Starting without optional epoll library
2025-08-01 14:39:46.336 [RMI TCP Connection(3)-*******] INFO  i.l.c.KqueueProvider 101 Starting without optional kqueue library
2025-08-01 14:44:26.096 [http-nio-8080-exec-1] INFO  f.d.c.StudentDormitoryQueryController 189 获取宿舍楼列表
2025-08-01 14:44:26.106 [http-nio-8080-exec-1] INFO  f.d.s.StudentDormitoryQueryService 185 获取宿舍楼列表
2025-08-01 14:44:26.226 [http-nio-8080-exec-2] INFO  f.d.c.StudentDormitoryQueryController 53 查询已分配宿舍学生状态，页码: 0, 大小: 20, 姓名: null, 楼栋: null, 状态: null
2025-08-01 14:44:26.228 [http-nio-8080-exec-2] INFO  f.d.s.StudentDormitoryQueryService 55 开始查询已分配宿舍学生状态: personName=null, buildingCode=null, dormitoryStatus=null
2025-08-01 14:44:26.337 [http-nio-8080-exec-1] INFO  f.d.s.StudentDormitoryQueryService 206 获取到9个宿舍楼
2025-08-01 14:44:26.337 [http-nio-8080-exec-2] INFO  f.d.s.StudentDormitoryQueryService 62 没有找到符合条件的已分配宿舍学生
2025-08-01 14:44:26.337 [http-nio-8080-exec-1] INFO  f.d.c.StudentDormitoryQueryController 201 获取宿舍楼列表成功，共9个楼栋
2025-08-01 14:44:26.372 [http-nio-8080-exec-2] INFO  f.d.c.StudentDormitoryQueryController 102 查询已分配宿舍学生状态成功，总数: 0, 当前页: 0
2025-08-01 15:01:33.248 [Thread-21] INFO  c.b.d.d.DynamicRoutingDataSource 211 dynamic-datasource start closing ....
2025-08-01 15:01:33.251 [Thread-21] INFO  c.b.d.d.DynamicRoutingDataSource 215 dynamic-datasource all closed success,bye

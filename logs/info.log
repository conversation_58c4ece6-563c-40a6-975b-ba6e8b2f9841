2025-08-01 11:17:30.218 [main] INFO  f.d.StartMain 50 Starting StartMain on PC-20230626VQFC with PID 24412 (E:\Jkxy\demo\target\classes started by Administrator in E:\Jkxy\demo)
2025-08-01 11:17:30.224 [main] INFO  f.d.StartMain 684 The following profiles are active: dev
2025-08-01 11:17:32.474 [main] INFO  o.a.c.h.Http11NioProtocol 180 Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 11:17:32.489 [main] INFO  o.a.c.c.StandardService 180 Starting service [Tomcat]
2025-08-01 11:17:32.490 [main] INFO  o.a.c.c.StandardEngine 180 Starting Servlet Engine: Apache Tomcat/8.5.34
2025-08-01 11:17:32.501 [localhost-startStop-1] INFO  o.a.c.c.AprLifecycleListener 180 The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: [D:\Environment\java8\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;D:\Environment\ZenTao\bin\git\cmd;D:\Environment\ZenTao\bin\sliksvn;D:\Environment\ZenTao\zbox\nssm\win32;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\Tools\VMware\bin\;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;D:\Environment\Git\cmd;E:\nodejsFile\node_global;D:\Environment\redis\;D:\Environment\apache-maven-3.5.4\bin;D:\Tools\xshell\;D:\Environment\nvm;D:\Environment\nodejs;C:\Program Files\dotnet\;D:\Environment\ffmpeg-5.1.2-full_build\bin;D:\Environment\nc;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\Tools\IntelliJ IDEA 2021.2.1\bin;D:\Tools\WebStorm 2021.2.1\bin;C:\Users\<USER>\AppData\Roaming\npm;D:\Environment\nvm;D:\Environment\nodejs;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\Tools\IntelliJ IDEA 2021.2.1\bin;D:\Tools\WebStorm 2021.2.1\bin;C:\Users\<USER>\AppData\Roaming\npm;D:\E;C:\Program Files (x86)\PuTTY\;D:\Tools\Microsoft VS Code\bin;D:\Mcp-Servers\chuzhi;C:\Program Files\Pandoc\;C:\Windows\system32\config\systemprofile\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\.local\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;D:\Tools\IntelliJ IDEA 2025.1.3\bin;D:\Tools\WebStorm 2025.1.3\bin;D:\Tools\DataGrip 2024.2.2\bin;D:\Soft\Windsurf\bin;D:\Tools\PyCharm 2025.1.1\bin;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;D:\Tools\cursor\resources\app\bin;D:\Tools\Kiro\bin;.]
2025-08-01 11:17:32.596 [localhost-startStop-1] INFO  o.a.c.c.C.[.[.[/] 180 Initializing Spring embedded WebApplicationContext
2025-08-01 11:17:33.607 [localhost-startStop-1] INFO  c.b.d.d.DynamicRoutingDataSource 154 dynamic-datasource - add a datasource named [fastgate] success
2025-08-01 11:17:33.607 [localhost-startStop-1] INFO  c.b.d.d.DynamicRoutingDataSource 154 dynamic-datasource - add a datasource named [master] success
2025-08-01 11:17:33.608 [localhost-startStop-1] INFO  c.b.d.d.DynamicRoutingDataSource 234 dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-08-01 11:17:36.112 [main] INFO  f.d.c.DynamicDataSourceConfig 25 动态数据源配置初始化完成 - 使用dynamic-datasource-spring-boot-starter
2025-08-01 11:17:37.650 [main] INFO  f.d.s.ScheduledTaskManagementService 99 定时任务信息初始化完成
2025-08-01 11:17:39.341 [main] INFO  o.a.c.h.Http11NioProtocol 180 Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 11:17:39.362 [main] INFO  o.a.t.u.n.NioSelectorPool 180 Using a shared selector for servlet write/read
2025-08-01 11:17:39.387 [main] INFO  f.d.StartMain 59 Started StartMain in 9.617 seconds (JVM running for 11.394)
2025-08-01 11:17:40.307 [RMI TCP Connection(3)-*******] INFO  i.l.c.EpollProvider 101 Starting without optional epoll library
2025-08-01 11:17:40.313 [RMI TCP Connection(3)-*******] INFO  i.l.c.KqueueProvider 101 Starting without optional kqueue library
2025-08-01 11:17:40.366 [RMI TCP Connection(6)-*******] INFO  o.a.c.c.C.[.[.[/] 180 Initializing Spring FrameworkServlet 'dispatcherServlet'
2025-08-01 11:27:42.285 [Thread-24] INFO  c.b.d.d.DynamicRoutingDataSource 211 dynamic-datasource start closing ....
2025-08-01 11:27:42.288 [Thread-24] INFO  c.b.d.d.DynamicRoutingDataSource 215 dynamic-datasource all closed success,bye
2025-08-01 11:27:48.549 [main] INFO  f.d.StartMain 50 Starting StartMain on PC-20230626VQFC with PID 19904 (E:\Jkxy\demo\target\classes started by Administrator in E:\Jkxy\demo)
2025-08-01 11:27:48.556 [main] INFO  f.d.StartMain 684 The following profiles are active: dev
2025-08-01 11:27:50.518 [main] INFO  o.a.c.h.Http11NioProtocol 180 Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 11:27:50.532 [main] INFO  o.a.c.c.StandardService 180 Starting service [Tomcat]
2025-08-01 11:27:50.533 [main] INFO  o.a.c.c.StandardEngine 180 Starting Servlet Engine: Apache Tomcat/8.5.34
2025-08-01 11:27:50.544 [localhost-startStop-1] INFO  o.a.c.c.AprLifecycleListener 180 The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: [D:\Environment\java8\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;D:\Environment\ZenTao\bin\git\cmd;D:\Environment\ZenTao\bin\sliksvn;D:\Environment\ZenTao\zbox\nssm\win32;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\Tools\VMware\bin\;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;D:\Environment\Git\cmd;E:\nodejsFile\node_global;D:\Environment\redis\;D:\Environment\apache-maven-3.5.4\bin;D:\Tools\xshell\;D:\Environment\nvm;D:\Environment\nodejs;C:\Program Files\dotnet\;D:\Environment\ffmpeg-5.1.2-full_build\bin;D:\Environment\nc;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\Tools\IntelliJ IDEA 2021.2.1\bin;D:\Tools\WebStorm 2021.2.1\bin;C:\Users\<USER>\AppData\Roaming\npm;D:\Environment\nvm;D:\Environment\nodejs;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\Tools\IntelliJ IDEA 2021.2.1\bin;D:\Tools\WebStorm 2021.2.1\bin;C:\Users\<USER>\AppData\Roaming\npm;D:\E;C:\Program Files (x86)\PuTTY\;D:\Tools\Microsoft VS Code\bin;D:\Mcp-Servers\chuzhi;C:\Program Files\Pandoc\;C:\Windows\system32\config\systemprofile\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\.local\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;D:\Tools\IntelliJ IDEA 2025.1.3\bin;D:\Tools\WebStorm 2025.1.3\bin;D:\Tools\DataGrip 2024.2.2\bin;D:\Soft\Windsurf\bin;D:\Tools\PyCharm 2025.1.1\bin;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;D:\Tools\cursor\resources\app\bin;D:\Tools\Kiro\bin;.]
2025-08-01 11:27:50.637 [localhost-startStop-1] INFO  o.a.c.c.C.[.[.[/] 180 Initializing Spring embedded WebApplicationContext
2025-08-01 11:27:51.601 [localhost-startStop-1] INFO  c.b.d.d.DynamicRoutingDataSource 154 dynamic-datasource - add a datasource named [fastgate] success
2025-08-01 11:27:51.601 [localhost-startStop-1] INFO  c.b.d.d.DynamicRoutingDataSource 154 dynamic-datasource - add a datasource named [master] success
2025-08-01 11:27:51.602 [localhost-startStop-1] INFO  c.b.d.d.DynamicRoutingDataSource 234 dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-08-01 11:27:54.020 [main] INFO  f.d.c.DynamicDataSourceConfig 25 动态数据源配置初始化完成 - 使用dynamic-datasource-spring-boot-starter
2025-08-01 11:27:55.459 [main] INFO  f.d.s.ScheduledTaskManagementService 99 定时任务信息初始化完成
2025-08-01 11:27:57.087 [main] INFO  o.a.c.h.Http11NioProtocol 180 Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 11:27:57.110 [main] INFO  o.a.t.u.n.NioSelectorPool 180 Using a shared selector for servlet write/read
2025-08-01 11:27:57.132 [main] INFO  f.d.StartMain 59 Started StartMain in 8.981 seconds (JVM running for 10.583)
2025-08-01 11:27:57.955 [RMI TCP Connection(5)-*******] INFO  o.a.c.c.C.[.[.[/] 180 Initializing Spring FrameworkServlet 'dispatcherServlet'
2025-08-01 11:27:58.056 [RMI TCP Connection(3)-*******] INFO  i.l.c.EpollProvider 101 Starting without optional epoll library
2025-08-01 11:27:58.062 [RMI TCP Connection(3)-*******] INFO  i.l.c.KqueueProvider 101 Starting without optional kqueue library
2025-08-01 13:48:57.566 [http-nio-8080-exec-1] INFO  f.d.c.DashboardApiController 39 获取楼栋列表请求
2025-08-01 13:48:57.851 [http-nio-8080-exec-1] INFO  f.d.s.DormitoryService 507 查询楼栋列表成功，共22个楼栋
2025-08-01 13:48:57.852 [http-nio-8080-exec-1] INFO  f.d.c.DashboardApiController 57 获取楼栋列表成功，共23个楼栋
2025-08-01 13:48:58.379 [http-nio-8080-exec-2] INFO  f.d.c.DashboardController 309 统一大屏数据查询: buildingCodes=null, forceRefresh=false
2025-08-01 13:48:58.380 [http-nio-8080-exec-2] INFO  f.d.c.DashboardController 326 查询全部统计数据
2025-08-01 13:48:58.421 [http-nio-8080-exec-2] INFO  f.d.s.CachedDormitoryStatsService 62 开始获取基于已分配宿舍学生的统计信息（优化版本）
2025-08-01 13:48:58.627 [http-nio-8080-exec-2] INFO  f.d.s.CachedDormitoryStatsService 334 开始计算基于已分配宿舍学生的统计信息
2025-08-01 13:48:58.675 [http-nio-8080-exec-2] INFO  f.d.s.CachedDormitoryStatsService 342 已分配宿舍的学生总数: 0
2025-08-01 13:48:58.766 [http-nio-8080-exec-2] INFO  f.d.s.CachedDormitoryStatsService 84 基于已分配宿舍学生的统计信息计算并缓存完成，耗时: 345ms
2025-08-01 13:48:58.767 [http-nio-8080-exec-2] INFO  f.d.c.DashboardController 373 统一大屏数据查询成功: 楼栋=null, 总人数=0, 在寝=0, 外出=0, 无记录=0, 归寝率=0.0%, 刷新=false, 耗时=387ms
2025-08-01 13:48:59.286 [http-nio-8080-exec-4] INFO  f.d.c.WebSocketStatusController 46 WebSocket状态查询: 连接数=0
2025-08-01 13:48:59.310 [http-nio-8080-exec-3] INFO  f.d.s.CachedDormitoryStatsService 62 开始获取基于已分配宿舍学生的统计信息（优化版本）
2025-08-01 13:48:59.336 [http-nio-8080-exec-3] INFO  f.d.s.CachedDormitoryStatsService 70 从缓存获取统计信息成功，耗时: 27ms
2025-08-01 13:48:59.685 [http-nio-8080-exec-5] INFO  f.d.w.DashboardWebSocketHandler 39 WebSocket连接建立: sessionId=63292d86-ec14-58a7-3565-c5dd92207a88, 当前连接数: 1
2025-08-01 13:49:16.583 [http-nio-8080-exec-6] INFO  f.d.w.DashboardWebSocketHandler 66 WebSocket连接关闭: sessionId=63292d86-ec14-58a7-3565-c5dd92207a88, status=CloseStatus[code=1000, reason=手动断开], 当前连接数: 0
2025-08-01 13:49:16.614 [http-nio-8080-exec-8] INFO  f.d.c.StudentDormitoryQueryController 189 获取宿舍楼列表
2025-08-01 13:49:16.628 [http-nio-8080-exec-8] INFO  f.d.s.StudentDormitoryQueryService 185 获取宿舍楼列表
2025-08-01 13:49:16.637 [http-nio-8080-exec-8] INFO  f.d.s.StudentDormitoryQueryService 206 获取到9个宿舍楼
2025-08-01 13:49:16.638 [http-nio-8080-exec-8] INFO  f.d.c.StudentDormitoryQueryController 201 获取宿舍楼列表成功，共9个楼栋
2025-08-01 13:49:16.640 [http-nio-8080-exec-7] INFO  f.d.c.StudentDormitoryQueryController 53 查询已分配宿舍学生状态，页码: 0, 大小: 20, 姓名: null, 楼栋: null, 状态: null
2025-08-01 13:49:16.641 [http-nio-8080-exec-7] INFO  f.d.s.StudentDormitoryQueryService 55 开始查询已分配宿舍学生状态: personName=null, buildingCode=null, dormitoryStatus=null
2025-08-01 13:49:16.644 [http-nio-8080-exec-7] INFO  f.d.s.StudentDormitoryQueryService 62 没有找到符合条件的已分配宿舍学生
2025-08-01 13:49:16.661 [http-nio-8080-exec-7] INFO  f.d.c.StudentDormitoryQueryController 102 查询已分配宿舍学生状态成功，总数: 0, 当前页: 0
2025-08-01 13:49:17.961 [http-nio-8080-exec-9] INFO  f.d.c.StudentDormitoryQueryController 53 查询已分配宿舍学生状态，页码: 0, 大小: 20, 姓名: null, 楼栋: null, 状态: null
2025-08-01 13:49:17.962 [http-nio-8080-exec-9] INFO  f.d.s.StudentDormitoryQueryService 55 开始查询已分配宿舍学生状态: personName=null, buildingCode=null, dormitoryStatus=null
2025-08-01 13:49:17.965 [http-nio-8080-exec-9] INFO  f.d.s.StudentDormitoryQueryService 62 没有找到符合条件的已分配宿舍学生
2025-08-01 13:49:17.966 [http-nio-8080-exec-9] INFO  f.d.c.StudentDormitoryQueryController 102 查询已分配宿舍学生状态成功，总数: 0, 当前页: 0
2025-08-01 13:49:18.692 [http-nio-8080-exec-10] INFO  f.d.c.DashboardApiController 39 获取楼栋列表请求
2025-08-01 13:49:18.695 [http-nio-8080-exec-10] INFO  f.d.s.DormitoryService 507 查询楼栋列表成功，共22个楼栋
2025-08-01 13:49:18.695 [http-nio-8080-exec-10] INFO  f.d.c.DashboardApiController 57 获取楼栋列表成功，共23个楼栋
2025-08-01 13:49:18.718 [http-nio-8080-exec-1] INFO  f.d.c.DashboardController 309 统一大屏数据查询: buildingCodes=null, forceRefresh=false
2025-08-01 13:49:18.719 [http-nio-8080-exec-1] INFO  f.d.c.DashboardController 326 查询全部统计数据
2025-08-01 13:49:18.719 [http-nio-8080-exec-1] INFO  f.d.s.CachedDormitoryStatsService 62 开始获取基于已分配宿舍学生的统计信息（优化版本）
2025-08-01 13:49:18.721 [http-nio-8080-exec-1] INFO  f.d.s.CachedDormitoryStatsService 70 从缓存获取统计信息成功，耗时: 2ms
2025-08-01 13:49:18.722 [http-nio-8080-exec-1] INFO  f.d.c.DashboardController 373 统一大屏数据查询成功: 楼栋=null, 总人数=0, 在寝=0, 外出=0, 无记录=0, 归寝率=0.0%, 刷新=false, 耗时=3ms
2025-08-01 13:49:19.238 [http-nio-8080-exec-2] INFO  f.d.c.WebSocketStatusController 46 WebSocket状态查询: 连接数=0
2025-08-01 13:49:19.247 [http-nio-8080-exec-4] INFO  f.d.s.CachedDormitoryStatsService 62 开始获取基于已分配宿舍学生的统计信息（优化版本）
2025-08-01 13:49:19.248 [http-nio-8080-exec-4] INFO  f.d.s.CachedDormitoryStatsService 70 从缓存获取统计信息成功，耗时: 1ms
2025-08-01 13:49:19.264 [http-nio-8080-exec-3] INFO  f.d.w.DashboardWebSocketHandler 39 WebSocket连接建立: sessionId=d9984f0b-0730-1c1f-af25-b30e72eb7e21, 当前连接数: 1
2025-08-01 14:12:58.955 [http-nio-8080-exec-9] INFO  f.d.w.DashboardWebSocketHandler 66 WebSocket连接关闭: sessionId=d9984f0b-0730-1c1f-af25-b30e72eb7e21, status=CloseStatus[code=1000, reason=手动断开], 当前连接数: 0
2025-08-01 14:13:09.997 [http-nio-8080-exec-2] INFO  f.d.c.StudentDormitoryQueryController 189 获取宿舍楼列表
2025-08-01 14:13:09.997 [http-nio-8080-exec-4] INFO  f.d.c.StudentDormitoryQueryController 53 查询已分配宿舍学生状态，页码: 0, 大小: 20, 姓名: null, 楼栋: null, 状态: null
2025-08-01 14:13:09.997 [http-nio-8080-exec-4] INFO  f.d.s.StudentDormitoryQueryService 55 开始查询已分配宿舍学生状态: personName=null, buildingCode=null, dormitoryStatus=null
2025-08-01 14:13:09.997 [http-nio-8080-exec-2] INFO  f.d.s.StudentDormitoryQueryService 185 获取宿舍楼列表
2025-08-01 14:13:10.003 [http-nio-8080-exec-4] INFO  f.d.s.StudentDormitoryQueryService 62 没有找到符合条件的已分配宿舍学生
2025-08-01 14:13:10.003 [http-nio-8080-exec-4] INFO  f.d.c.StudentDormitoryQueryController 102 查询已分配宿舍学生状态成功，总数: 0, 当前页: 0
2025-08-01 14:13:10.004 [http-nio-8080-exec-2] INFO  f.d.s.StudentDormitoryQueryService 206 获取到9个宿舍楼
2025-08-01 14:13:10.005 [http-nio-8080-exec-2] INFO  f.d.c.StudentDormitoryQueryController 201 获取宿舍楼列表成功，共9个楼栋
2025-08-01 14:13:12.360 [http-nio-8080-exec-3] INFO  f.d.c.StudentDormitoryQueryController 53 查询已分配宿舍学生状态，页码: 0, 大小: 20, 姓名: null, 楼栋: null, 状态: null
2025-08-01 14:13:12.360 [http-nio-8080-exec-3] INFO  f.d.s.StudentDormitoryQueryService 55 开始查询已分配宿舍学生状态: personName=null, buildingCode=null, dormitoryStatus=null
2025-08-01 14:13:12.365 [http-nio-8080-exec-3] INFO  f.d.s.StudentDormitoryQueryService 62 没有找到符合条件的已分配宿舍学生
2025-08-01 14:13:12.365 [http-nio-8080-exec-3] INFO  f.d.c.StudentDormitoryQueryController 102 查询已分配宿舍学生状态成功，总数: 0, 当前页: 0
2025-08-01 14:13:16.909 [http-nio-8080-exec-5] INFO  f.d.c.StudentDormitoryQueryController 189 获取宿舍楼列表
2025-08-01 14:13:16.910 [http-nio-8080-exec-5] INFO  f.d.s.StudentDormitoryQueryService 185 获取宿舍楼列表
2025-08-01 14:13:16.910 [http-nio-8080-exec-6] INFO  f.d.c.StudentDormitoryQueryController 53 查询已分配宿舍学生状态，页码: 0, 大小: 20, 姓名: null, 楼栋: null, 状态: null
2025-08-01 14:13:16.911 [http-nio-8080-exec-6] INFO  f.d.s.StudentDormitoryQueryService 55 开始查询已分配宿舍学生状态: personName=null, buildingCode=null, dormitoryStatus=null
2025-08-01 14:13:16.912 [http-nio-8080-exec-6] INFO  f.d.s.StudentDormitoryQueryService 62 没有找到符合条件的已分配宿舍学生
2025-08-01 14:13:16.913 [http-nio-8080-exec-6] INFO  f.d.c.StudentDormitoryQueryController 102 查询已分配宿舍学生状态成功，总数: 0, 当前页: 0
2025-08-01 14:13:16.914 [http-nio-8080-exec-5] INFO  f.d.s.StudentDormitoryQueryService 206 获取到9个宿舍楼
2025-08-01 14:13:16.915 [http-nio-8080-exec-5] INFO  f.d.c.StudentDormitoryQueryController 201 获取宿舍楼列表成功，共9个楼栋
2025-08-01 14:16:24.981 [http-nio-8080-exec-8] INFO  f.d.c.StudentDormitoryQueryController 53 查询已分配宿舍学生状态，页码: 0, 大小: 20, 姓名: null, 楼栋: null, 状态: null
2025-08-01 14:16:24.981 [http-nio-8080-exec-8] INFO  f.d.s.StudentDormitoryQueryService 55 开始查询已分配宿舍学生状态: personName=null, buildingCode=null, dormitoryStatus=null
2025-08-01 14:16:24.986 [http-nio-8080-exec-8] INFO  f.d.s.StudentDormitoryQueryService 62 没有找到符合条件的已分配宿舍学生
2025-08-01 14:16:24.987 [http-nio-8080-exec-8] INFO  f.d.c.StudentDormitoryQueryController 102 查询已分配宿舍学生状态成功，总数: 0, 当前页: 0
2025-08-01 14:29:29.014 [Thread-23] INFO  c.b.d.d.DynamicRoutingDataSource 211 dynamic-datasource start closing ....
2025-08-01 14:29:29.016 [Thread-23] INFO  c.b.d.d.DynamicRoutingDataSource 215 dynamic-datasource all closed success,bye
2025-08-01 14:30:52.730 [main] INFO  f.d.StartMain 50 Starting StartMain on PC-20230626VQFC with PID 2620 (E:\Jkxy\demo\target\classes started by Administrator in E:\Jkxy\demo)
2025-08-01 14:30:52.741 [main] INFO  f.d.StartMain 684 The following profiles are active: dev
2025-08-01 14:30:54.583 [main] INFO  o.a.c.h.Http11NioProtocol 180 Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 14:30:54.598 [main] INFO  o.a.c.c.StandardService 180 Starting service [Tomcat]
2025-08-01 14:30:54.598 [main] INFO  o.a.c.c.StandardEngine 180 Starting Servlet Engine: Apache Tomcat/8.5.34
2025-08-01 14:30:54.611 [localhost-startStop-1] INFO  o.a.c.c.AprLifecycleListener 180 The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: [D:\Environment\java8\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;D:\Environment\ZenTao\bin\git\cmd;D:\Environment\ZenTao\bin\sliksvn;D:\Environment\ZenTao\zbox\nssm\win32;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\Tools\VMware\bin\;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;D:\Environment\Git\cmd;E:\nodejsFile\node_global;D:\Environment\redis\;D:\Environment\apache-maven-3.5.4\bin;D:\Tools\xshell\;D:\Environment\nvm;D:\Environment\nodejs;C:\Program Files\dotnet\;D:\Environment\ffmpeg-5.1.2-full_build\bin;D:\Environment\nc;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\Tools\IntelliJ IDEA 2021.2.1\bin;D:\Tools\WebStorm 2021.2.1\bin;C:\Users\<USER>\AppData\Roaming\npm;D:\Environment\nvm;D:\Environment\nodejs;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\Tools\IntelliJ IDEA 2021.2.1\bin;D:\Tools\WebStorm 2021.2.1\bin;C:\Users\<USER>\AppData\Roaming\npm;D:\E;C:\Program Files (x86)\PuTTY\;D:\Tools\Microsoft VS Code\bin;D:\Mcp-Servers\chuzhi;C:\Program Files\Pandoc\;C:\Windows\system32\config\systemprofile\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\.local\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;D:\Tools\IntelliJ IDEA 2025.1.3\bin;D:\Tools\WebStorm 2025.1.3\bin;D:\Tools\DataGrip 2024.2.2\bin;D:\Soft\Windsurf\bin;D:\Tools\PyCharm 2025.1.1\bin;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;D:\Tools\cursor\resources\app\bin;D:\Tools\Kiro\bin;.]
2025-08-01 14:30:54.721 [localhost-startStop-1] INFO  o.a.c.c.C.[.[.[/] 180 Initializing Spring embedded WebApplicationContext
2025-08-01 14:30:55.884 [localhost-startStop-1] INFO  c.b.d.d.DynamicRoutingDataSource 154 dynamic-datasource - add a datasource named [fastgate] success
2025-08-01 14:30:55.885 [localhost-startStop-1] INFO  c.b.d.d.DynamicRoutingDataSource 154 dynamic-datasource - add a datasource named [master] success
2025-08-01 14:30:55.886 [localhost-startStop-1] INFO  c.b.d.d.DynamicRoutingDataSource 234 dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-08-01 14:30:58.371 [main] INFO  f.d.c.DynamicDataSourceConfig 25 动态数据源配置初始化完成 - 使用dynamic-datasource-spring-boot-starter
2025-08-01 14:30:59.858 [main] INFO  f.d.s.ScheduledTaskManagementService 99 定时任务信息初始化完成
2025-08-01 14:31:01.448 [main] INFO  o.a.c.h.Http11NioProtocol 180 Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 14:31:01.480 [main] INFO  o.a.t.u.n.NioSelectorPool 180 Using a shared selector for servlet write/read
2025-08-01 14:31:01.504 [main] INFO  f.d.StartMain 59 Started StartMain in 9.2 seconds (JVM running for 11.294)
2025-08-01 14:31:02.429 [RMI TCP Connection(6)-*******] INFO  o.a.c.c.C.[.[.[/] 180 Initializing Spring FrameworkServlet 'dispatcherServlet'
2025-08-01 14:31:02.670 [RMI TCP Connection(5)-*******] INFO  i.l.c.EpollProvider 101 Starting without optional epoll library
2025-08-01 14:31:02.674 [RMI TCP Connection(5)-*******] INFO  i.l.c.KqueueProvider 101 Starting without optional kqueue library
2025-08-01 14:39:29.407 [Thread-23] INFO  c.b.d.d.DynamicRoutingDataSource 211 dynamic-datasource start closing ....
2025-08-01 14:39:29.410 [Thread-23] INFO  c.b.d.d.DynamicRoutingDataSource 215 dynamic-datasource all closed success,bye
2025-08-01 14:39:37.218 [main] INFO  f.d.StartMain 50 Starting StartMain on PC-20230626VQFC with PID 16544 (E:\Jkxy\demo\target\classes started by Administrator in E:\Jkxy\demo)
2025-08-01 14:39:37.225 [main] INFO  f.d.StartMain 684 The following profiles are active: dev
2025-08-01 14:39:38.806 [main] INFO  o.a.c.h.Http11NioProtocol 180 Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 14:39:38.818 [main] INFO  o.a.c.c.StandardService 180 Starting service [Tomcat]
2025-08-01 14:39:38.818 [main] INFO  o.a.c.c.StandardEngine 180 Starting Servlet Engine: Apache Tomcat/8.5.34
2025-08-01 14:39:38.829 [localhost-startStop-1] INFO  o.a.c.c.AprLifecycleListener 180 The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: [D:\Environment\java8\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;D:\Environment\ZenTao\bin\git\cmd;D:\Environment\ZenTao\bin\sliksvn;D:\Environment\ZenTao\zbox\nssm\win32;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\Tools\VMware\bin\;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;D:\Environment\Git\cmd;E:\nodejsFile\node_global;D:\Environment\redis\;D:\Environment\apache-maven-3.5.4\bin;D:\Tools\xshell\;D:\Environment\nvm;D:\Environment\nodejs;C:\Program Files\dotnet\;D:\Environment\ffmpeg-5.1.2-full_build\bin;D:\Environment\nc;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\Tools\IntelliJ IDEA 2021.2.1\bin;D:\Tools\WebStorm 2021.2.1\bin;C:\Users\<USER>\AppData\Roaming\npm;D:\Environment\nvm;D:\Environment\nodejs;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\Tools\IntelliJ IDEA 2021.2.1\bin;D:\Tools\WebStorm 2021.2.1\bin;C:\Users\<USER>\AppData\Roaming\npm;D:\E;C:\Program Files (x86)\PuTTY\;D:\Tools\Microsoft VS Code\bin;D:\Mcp-Servers\chuzhi;C:\Program Files\Pandoc\;C:\Windows\system32\config\systemprofile\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\.local\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;D:\Tools\IntelliJ IDEA 2025.1.3\bin;D:\Tools\WebStorm 2025.1.3\bin;D:\Tools\DataGrip 2024.2.2\bin;D:\Soft\Windsurf\bin;D:\Tools\PyCharm 2025.1.1\bin;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;D:\Tools\cursor\resources\app\bin;D:\Tools\Kiro\bin;.]
2025-08-01 14:39:38.915 [localhost-startStop-1] INFO  o.a.c.c.C.[.[.[/] 180 Initializing Spring embedded WebApplicationContext
2025-08-01 14:39:39.806 [localhost-startStop-1] INFO  c.b.d.d.DynamicRoutingDataSource 154 dynamic-datasource - add a datasource named [fastgate] success
2025-08-01 14:39:39.806 [localhost-startStop-1] INFO  c.b.d.d.DynamicRoutingDataSource 154 dynamic-datasource - add a datasource named [master] success
2025-08-01 14:39:39.806 [localhost-startStop-1] INFO  c.b.d.d.DynamicRoutingDataSource 234 dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-08-01 14:39:41.981 [main] INFO  f.d.c.DynamicDataSourceConfig 25 动态数据源配置初始化完成 - 使用dynamic-datasource-spring-boot-starter
2025-08-01 14:39:43.279 [main] INFO  f.d.s.ScheduledTaskManagementService 99 定时任务信息初始化完成
2025-08-01 14:39:44.751 [main] INFO  o.a.c.h.Http11NioProtocol 180 Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 14:39:44.771 [main] INFO  o.a.t.u.n.NioSelectorPool 180 Using a shared selector for servlet write/read
2025-08-01 14:39:44.791 [main] INFO  f.d.StartMain 59 Started StartMain in 7.943 seconds (JVM running for 9.24)
2025-08-01 14:39:46.081 [RMI TCP Connection(6)-*******] INFO  o.a.c.c.C.[.[.[/] 180 Initializing Spring FrameworkServlet 'dispatcherServlet'
2025-08-01 14:39:46.330 [RMI TCP Connection(3)-*******] INFO  i.l.c.EpollProvider 101 Starting without optional epoll library
2025-08-01 14:39:46.336 [RMI TCP Connection(3)-*******] INFO  i.l.c.KqueueProvider 101 Starting without optional kqueue library
2025-08-01 14:44:26.096 [http-nio-8080-exec-1] INFO  f.d.c.StudentDormitoryQueryController 189 获取宿舍楼列表
2025-08-01 14:44:26.106 [http-nio-8080-exec-1] INFO  f.d.s.StudentDormitoryQueryService 185 获取宿舍楼列表
2025-08-01 14:44:26.226 [http-nio-8080-exec-2] INFO  f.d.c.StudentDormitoryQueryController 53 查询已分配宿舍学生状态，页码: 0, 大小: 20, 姓名: null, 楼栋: null, 状态: null
2025-08-01 14:44:26.228 [http-nio-8080-exec-2] INFO  f.d.s.StudentDormitoryQueryService 55 开始查询已分配宿舍学生状态: personName=null, buildingCode=null, dormitoryStatus=null
2025-08-01 14:44:26.337 [http-nio-8080-exec-1] INFO  f.d.s.StudentDormitoryQueryService 206 获取到9个宿舍楼
2025-08-01 14:44:26.337 [http-nio-8080-exec-2] INFO  f.d.s.StudentDormitoryQueryService 62 没有找到符合条件的已分配宿舍学生
2025-08-01 14:44:26.337 [http-nio-8080-exec-1] INFO  f.d.c.StudentDormitoryQueryController 201 获取宿舍楼列表成功，共9个楼栋
2025-08-01 14:44:26.372 [http-nio-8080-exec-2] INFO  f.d.c.StudentDormitoryQueryController 102 查询已分配宿舍学生状态成功，总数: 0, 当前页: 0
2025-08-01 15:01:33.248 [Thread-21] INFO  c.b.d.d.DynamicRoutingDataSource 211 dynamic-datasource start closing ....
2025-08-01 15:01:33.251 [Thread-21] INFO  c.b.d.d.DynamicRoutingDataSource 215 dynamic-datasource all closed success,bye
2025-08-01 15:01:42.580 [main] INFO  f.d.StartMain 50 Starting StartMain on PC-20230626VQFC with PID 18980 (E:\Jkxy\demo\target\classes started by Administrator in E:\Jkxy\demo)
2025-08-01 15:01:42.587 [main] INFO  f.d.StartMain 684 The following profiles are active: dev
2025-08-01 15:01:44.328 [main] INFO  o.a.c.h.Http11NioProtocol 180 Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 15:01:44.341 [main] INFO  o.a.c.c.StandardService 180 Starting service [Tomcat]
2025-08-01 15:01:44.342 [main] INFO  o.a.c.c.StandardEngine 180 Starting Servlet Engine: Apache Tomcat/8.5.34
2025-08-01 15:01:44.352 [localhost-startStop-1] INFO  o.a.c.c.AprLifecycleListener 180 The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: [D:\Environment\java8\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;D:\Environment\ZenTao\bin\git\cmd;D:\Environment\ZenTao\bin\sliksvn;D:\Environment\ZenTao\zbox\nssm\win32;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\Tools\VMware\bin\;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;D:\Environment\Git\cmd;E:\nodejsFile\node_global;D:\Environment\redis\;D:\Environment\apache-maven-3.5.4\bin;D:\Tools\xshell\;D:\Environment\nvm;D:\Environment\nodejs;C:\Program Files\dotnet\;D:\Environment\ffmpeg-5.1.2-full_build\bin;D:\Environment\nc;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\Tools\IntelliJ IDEA 2021.2.1\bin;D:\Tools\WebStorm 2021.2.1\bin;C:\Users\<USER>\AppData\Roaming\npm;D:\Environment\nvm;D:\Environment\nodejs;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\Tools\IntelliJ IDEA 2021.2.1\bin;D:\Tools\WebStorm 2021.2.1\bin;C:\Users\<USER>\AppData\Roaming\npm;D:\E;C:\Program Files (x86)\PuTTY\;D:\Tools\Microsoft VS Code\bin;D:\Mcp-Servers\chuzhi;C:\Program Files\Pandoc\;C:\Windows\system32\config\systemprofile\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\.local\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;D:\Tools\IntelliJ IDEA 2025.1.3\bin;D:\Tools\WebStorm 2025.1.3\bin;D:\Tools\DataGrip 2024.2.2\bin;D:\Soft\Windsurf\bin;D:\Tools\PyCharm 2025.1.1\bin;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;D:\Tools\cursor\resources\app\bin;D:\Tools\Kiro\bin;.]
2025-08-01 15:01:44.439 [localhost-startStop-1] INFO  o.a.c.c.C.[.[.[/] 180 Initializing Spring embedded WebApplicationContext
2025-08-01 15:01:45.503 [localhost-startStop-1] INFO  c.b.d.d.DynamicRoutingDataSource 154 dynamic-datasource - add a datasource named [fastgate] success
2025-08-01 15:01:45.503 [localhost-startStop-1] INFO  c.b.d.d.DynamicRoutingDataSource 154 dynamic-datasource - add a datasource named [master] success
2025-08-01 15:01:45.503 [localhost-startStop-1] INFO  c.b.d.d.DynamicRoutingDataSource 234 dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-08-01 15:01:47.878 [main] INFO  f.d.c.DynamicDataSourceConfig 25 动态数据源配置初始化完成 - 使用dynamic-datasource-spring-boot-starter
2025-08-01 15:01:49.233 [main] INFO  f.d.s.ScheduledTaskManagementService 99 定时任务信息初始化完成
2025-08-01 15:01:50.687 [main] INFO  o.a.c.h.Http11NioProtocol 180 Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 15:01:50.708 [main] INFO  o.a.t.u.n.NioSelectorPool 180 Using a shared selector for servlet write/read
2025-08-01 15:01:50.728 [main] INFO  f.d.StartMain 59 Started StartMain in 8.538 seconds (JVM running for 10.01)
2025-08-01 15:01:51.595 [RMI TCP Connection(5)-*******] INFO  o.a.c.c.C.[.[.[/] 180 Initializing Spring FrameworkServlet 'dispatcherServlet'
2025-08-01 15:01:52.218 [RMI TCP Connection(6)-*******] INFO  i.l.c.EpollProvider 101 Starting without optional epoll library
2025-08-01 15:01:52.221 [RMI TCP Connection(6)-*******] INFO  i.l.c.KqueueProvider 101 Starting without optional kqueue library
2025-08-01 15:02:34.880 [http-nio-8080-exec-2] INFO  f.d.c.StudentDormitoryQueryController 189 获取宿舍楼列表
2025-08-01 15:02:34.889 [http-nio-8080-exec-2] INFO  f.d.s.StudentDormitoryQueryService 185 获取宿舍楼列表
2025-08-01 15:02:35.017 [http-nio-8080-exec-1] INFO  f.d.c.StudentDormitoryQueryController 53 查询已分配宿舍学生状态，页码: 0, 大小: 20, 姓名: null, 楼栋: null, 状态: null
2025-08-01 15:02:35.018 [http-nio-8080-exec-1] INFO  f.d.s.StudentDormitoryQueryService 55 开始查询已分配宿舍学生状态: personName=null, buildingCode=null, dormitoryStatus=null
2025-08-01 15:02:35.115 [http-nio-8080-exec-2] INFO  f.d.s.StudentDormitoryQueryService 206 获取到9个宿舍楼
2025-08-01 15:02:35.115 [http-nio-8080-exec-1] INFO  f.d.s.StudentDormitoryQueryService 62 没有找到符合条件的已分配宿舍学生
2025-08-01 15:02:35.115 [http-nio-8080-exec-2] INFO  f.d.c.StudentDormitoryQueryController 201 获取宿舍楼列表成功，共9个楼栋
2025-08-01 15:02:35.158 [http-nio-8080-exec-1] INFO  f.d.c.StudentDormitoryQueryController 102 查询已分配宿舍学生状态成功，总数: 0, 当前页: 0
2025-08-01 15:02:48.322 [http-nio-8080-exec-6] INFO  f.d.c.StudentDormitoryQueryController 53 查询已分配宿舍学生状态，页码: 0, 大小: 20, 姓名: null, 楼栋: 2fa26b3b40e3410a8a9e3ed0bc4fcbc3, 状态: null
2025-08-01 15:02:48.323 [http-nio-8080-exec-6] INFO  f.d.s.StudentDormitoryQueryService 55 开始查询已分配宿舍学生状态: personName=null, buildingCode=2fa26b3b40e3410a8a9e3ed0bc4fcbc3, dormitoryStatus=null
2025-08-01 15:02:48.333 [http-nio-8080-exec-6] INFO  f.d.s.StudentDormitoryQueryService 62 没有找到符合条件的已分配宿舍学生
2025-08-01 15:02:48.333 [http-nio-8080-exec-6] INFO  f.d.c.StudentDormitoryQueryController 102 查询已分配宿舍学生状态成功，总数: 0, 当前页: 0
2025-08-01 15:02:50.130 [http-nio-8080-exec-5] INFO  f.d.c.StudentDormitoryQueryController 53 查询已分配宿舍学生状态，页码: 0, 大小: 20, 姓名: null, 楼栋: 96a972c28e5c444b937d9409ec996432, 状态: null
2025-08-01 15:02:50.130 [http-nio-8080-exec-5] INFO  f.d.s.StudentDormitoryQueryService 55 开始查询已分配宿舍学生状态: personName=null, buildingCode=96a972c28e5c444b937d9409ec996432, dormitoryStatus=null
2025-08-01 15:02:50.134 [http-nio-8080-exec-5] INFO  f.d.s.StudentDormitoryQueryService 62 没有找到符合条件的已分配宿舍学生
2025-08-01 15:02:50.135 [http-nio-8080-exec-5] INFO  f.d.c.StudentDormitoryQueryController 102 查询已分配宿舍学生状态成功，总数: 0, 当前页: 0
2025-08-01 15:03:50.960 [http-nio-8080-exec-4] INFO  f.d.c.StudentDormitoryQueryController 189 获取宿舍楼列表
2025-08-01 15:03:50.960 [http-nio-8080-exec-3] INFO  f.d.c.StudentDormitoryQueryController 53 查询已分配宿舍学生状态，页码: 0, 大小: 20, 姓名: null, 楼栋: null, 状态: null
2025-08-01 15:03:50.960 [http-nio-8080-exec-4] INFO  f.d.s.StudentDormitoryQueryService 185 获取宿舍楼列表
2025-08-01 15:03:50.961 [http-nio-8080-exec-3] INFO  f.d.s.StudentDormitoryQueryService 55 开始查询已分配宿舍学生状态: personName=null, buildingCode=null, dormitoryStatus=null
2025-08-01 15:03:50.964 [http-nio-8080-exec-3] INFO  f.d.s.StudentDormitoryQueryService 62 没有找到符合条件的已分配宿舍学生
2025-08-01 15:03:50.965 [http-nio-8080-exec-3] INFO  f.d.c.StudentDormitoryQueryController 102 查询已分配宿舍学生状态成功，总数: 0, 当前页: 0
2025-08-01 15:03:50.966 [http-nio-8080-exec-4] INFO  f.d.s.StudentDormitoryQueryService 206 获取到9个宿舍楼
2025-08-01 15:03:50.966 [http-nio-8080-exec-4] INFO  f.d.c.StudentDormitoryQueryController 201 获取宿舍楼列表成功，共9个楼栋
2025-08-01 15:04:15.714 [http-nio-8080-exec-7] INFO  f.d.c.StudentDormitoryQueryController 189 获取宿舍楼列表
2025-08-01 15:04:15.715 [http-nio-8080-exec-8] INFO  f.d.c.StudentDormitoryQueryController 53 查询已分配宿舍学生状态，页码: 0, 大小: 20, 姓名: null, 楼栋: null, 状态: null
2025-08-01 15:04:15.715 [http-nio-8080-exec-7] INFO  f.d.s.StudentDormitoryQueryService 185 获取宿舍楼列表
2025-08-01 15:04:15.715 [http-nio-8080-exec-8] INFO  f.d.s.StudentDormitoryQueryService 55 开始查询已分配宿舍学生状态: personName=null, buildingCode=null, dormitoryStatus=null
2025-08-01 15:04:15.718 [http-nio-8080-exec-8] INFO  f.d.s.StudentDormitoryQueryService 62 没有找到符合条件的已分配宿舍学生
2025-08-01 15:04:15.718 [http-nio-8080-exec-8] INFO  f.d.c.StudentDormitoryQueryController 102 查询已分配宿舍学生状态成功，总数: 0, 当前页: 0
2025-08-01 15:04:15.720 [http-nio-8080-exec-7] INFO  f.d.s.StudentDormitoryQueryService 206 获取到9个宿舍楼
2025-08-01 15:04:15.720 [http-nio-8080-exec-7] INFO  f.d.c.StudentDormitoryQueryController 201 获取宿舍楼列表成功，共9个楼栋
2025-08-01 15:04:32.193 [http-nio-8080-exec-9] INFO  f.d.c.StudentDormitoryQueryController 189 获取宿舍楼列表
2025-08-01 15:04:32.193 [http-nio-8080-exec-10] INFO  f.d.c.StudentDormitoryQueryController 53 查询已分配宿舍学生状态，页码: 0, 大小: 20, 姓名: null, 楼栋: null, 状态: null
2025-08-01 15:04:32.193 [http-nio-8080-exec-9] INFO  f.d.s.StudentDormitoryQueryService 185 获取宿舍楼列表
2025-08-01 15:04:32.193 [http-nio-8080-exec-10] INFO  f.d.s.StudentDormitoryQueryService 55 开始查询已分配宿舍学生状态: personName=null, buildingCode=null, dormitoryStatus=null
2025-08-01 15:04:32.196 [http-nio-8080-exec-10] INFO  f.d.s.StudentDormitoryQueryService 62 没有找到符合条件的已分配宿舍学生
2025-08-01 15:04:32.196 [http-nio-8080-exec-10] INFO  f.d.c.StudentDormitoryQueryController 102 查询已分配宿舍学生状态成功，总数: 0, 当前页: 0
2025-08-01 15:04:32.199 [http-nio-8080-exec-9] INFO  f.d.s.StudentDormitoryQueryService 206 获取到9个宿舍楼
2025-08-01 15:04:32.199 [http-nio-8080-exec-9] INFO  f.d.c.StudentDormitoryQueryController 201 获取宿舍楼列表成功，共9个楼栋
2025-08-01 15:04:43.226 [http-nio-8080-exec-1] INFO  f.d.c.StudentDormitoryQueryController 189 获取宿舍楼列表
2025-08-01 15:04:43.226 [http-nio-8080-exec-2] INFO  f.d.c.StudentDormitoryQueryController 53 查询已分配宿舍学生状态，页码: 0, 大小: 20, 姓名: null, 楼栋: null, 状态: null
2025-08-01 15:04:43.226 [http-nio-8080-exec-1] INFO  f.d.s.StudentDormitoryQueryService 185 获取宿舍楼列表
2025-08-01 15:04:43.228 [http-nio-8080-exec-2] INFO  f.d.s.StudentDormitoryQueryService 55 开始查询已分配宿舍学生状态: personName=null, buildingCode=null, dormitoryStatus=null
2025-08-01 15:04:43.228 [http-nio-8080-exec-6] INFO  f.d.c.StudentDormitoryQueryController 223 获取已分配宿舍学生统计信息，楼栋: null
2025-08-01 15:04:43.228 [http-nio-8080-exec-6] INFO  f.d.s.StudentDormitoryQueryService 131 开始获取已分配宿舍学生统计信息: buildingCode=null
2025-08-01 15:04:43.231 [http-nio-8080-exec-2] INFO  f.d.s.StudentDormitoryQueryService 62 没有找到符合条件的已分配宿舍学生
2025-08-01 15:04:43.231 [http-nio-8080-exec-2] INFO  f.d.c.StudentDormitoryQueryController 102 查询已分配宿舍学生状态成功，总数: 0, 当前页: 0
2025-08-01 15:04:43.232 [http-nio-8080-exec-1] INFO  f.d.s.StudentDormitoryQueryService 206 获取到9个宿舍楼
2025-08-01 15:04:43.233 [http-nio-8080-exec-1] INFO  f.d.c.StudentDormitoryQueryController 201 获取宿舍楼列表成功，共9个楼栋
2025-08-01 15:04:43.236 [http-nio-8080-exec-6] INFO  f.d.s.StudentDormitoryQueryService 138 没有找到已分配宿舍的学生
2025-08-01 15:04:43.236 [http-nio-8080-exec-6] INFO  f.d.c.StudentDormitoryQueryController 234 获取已分配宿舍学生统计信息成功
2025-08-01 15:04:59.012 [http-nio-8080-exec-3] INFO  f.d.c.StudentDormitoryQueryController 53 查询已分配宿舍学生状态，页码: 0, 大小: 20, 姓名: null, 楼栋: null, 状态: null
2025-08-01 15:04:59.013 [http-nio-8080-exec-3] INFO  f.d.s.StudentDormitoryQueryService 55 开始查询已分配宿舍学生状态: personName=null, buildingCode=null, dormitoryStatus=null
2025-08-01 15:04:59.014 [http-nio-8080-exec-5] INFO  f.d.c.StudentDormitoryQueryController 189 获取宿舍楼列表
2025-08-01 15:04:59.015 [http-nio-8080-exec-5] INFO  f.d.s.StudentDormitoryQueryService 185 获取宿舍楼列表
2025-08-01 15:04:59.022 [http-nio-8080-exec-4] INFO  f.d.c.StudentDormitoryQueryController 223 获取已分配宿舍学生统计信息，楼栋: null
2025-08-01 15:04:59.022 [http-nio-8080-exec-4] INFO  f.d.s.StudentDormitoryQueryService 131 开始获取已分配宿舍学生统计信息: buildingCode=null
2025-08-01 15:04:59.024 [http-nio-8080-exec-3] INFO  f.d.s.StudentDormitoryQueryService 62 没有找到符合条件的已分配宿舍学生
2025-08-01 15:04:59.024 [http-nio-8080-exec-3] INFO  f.d.c.StudentDormitoryQueryController 102 查询已分配宿舍学生状态成功，总数: 0, 当前页: 0
2025-08-01 15:04:59.028 [http-nio-8080-exec-5] INFO  f.d.s.StudentDormitoryQueryService 206 获取到9个宿舍楼
2025-08-01 15:04:59.028 [http-nio-8080-exec-5] INFO  f.d.c.StudentDormitoryQueryController 201 获取宿舍楼列表成功，共9个楼栋
2025-08-01 15:04:59.031 [http-nio-8080-exec-4] INFO  f.d.s.StudentDormitoryQueryService 138 没有找到已分配宿舍的学生
2025-08-01 15:04:59.031 [http-nio-8080-exec-4] INFO  f.d.c.StudentDormitoryQueryController 234 获取已分配宿舍学生统计信息成功
2025-08-01 15:05:27.344 [http-nio-8080-exec-8] INFO  f.d.c.StudentDormitoryQueryController 189 获取宿舍楼列表
2025-08-01 15:05:27.344 [http-nio-8080-exec-10] INFO  f.d.c.StudentDormitoryQueryController 223 获取已分配宿舍学生统计信息，楼栋: null
2025-08-01 15:05:27.344 [http-nio-8080-exec-7] INFO  f.d.c.StudentDormitoryQueryController 53 查询已分配宿舍学生状态，页码: 0, 大小: 20, 姓名: null, 楼栋: null, 状态: null
2025-08-01 15:05:27.345 [http-nio-8080-exec-8] INFO  f.d.s.StudentDormitoryQueryService 185 获取宿舍楼列表
2025-08-01 15:05:27.345 [http-nio-8080-exec-7] INFO  f.d.s.StudentDormitoryQueryService 55 开始查询已分配宿舍学生状态: personName=null, buildingCode=null, dormitoryStatus=null
2025-08-01 15:05:27.345 [http-nio-8080-exec-10] INFO  f.d.s.StudentDormitoryQueryService 131 开始获取已分配宿舍学生统计信息: buildingCode=null
2025-08-01 15:05:27.348 [http-nio-8080-exec-7] INFO  f.d.s.StudentDormitoryQueryService 62 没有找到符合条件的已分配宿舍学生
2025-08-01 15:05:27.349 [http-nio-8080-exec-7] INFO  f.d.c.StudentDormitoryQueryController 102 查询已分配宿舍学生状态成功，总数: 0, 当前页: 0
2025-08-01 15:05:27.349 [http-nio-8080-exec-10] INFO  f.d.s.StudentDormitoryQueryService 138 没有找到已分配宿舍的学生
2025-08-01 15:05:27.350 [http-nio-8080-exec-10] INFO  f.d.c.StudentDormitoryQueryController 234 获取已分配宿舍学生统计信息成功
2025-08-01 15:05:27.352 [http-nio-8080-exec-8] INFO  f.d.s.StudentDormitoryQueryService 206 获取到9个宿舍楼
2025-08-01 15:05:27.352 [http-nio-8080-exec-8] INFO  f.d.c.StudentDormitoryQueryController 201 获取宿舍楼列表成功，共9个楼栋
2025-08-01 15:06:01.438 [http-nio-8080-exec-1] INFO  f.d.c.StudentDormitoryQueryController 223 获取已分配宿舍学生统计信息，楼栋: null
2025-08-01 15:06:01.438 [http-nio-8080-exec-9] INFO  f.d.c.StudentDormitoryQueryController 189 获取宿舍楼列表
2025-08-01 15:06:01.438 [http-nio-8080-exec-2] INFO  f.d.c.StudentDormitoryQueryController 53 查询已分配宿舍学生状态，页码: 0, 大小: 20, 姓名: null, 楼栋: null, 状态: null
2025-08-01 15:06:01.438 [http-nio-8080-exec-2] INFO  f.d.s.StudentDormitoryQueryService 55 开始查询已分配宿舍学生状态: personName=null, buildingCode=null, dormitoryStatus=null
2025-08-01 15:06:01.438 [http-nio-8080-exec-9] INFO  f.d.s.StudentDormitoryQueryService 185 获取宿舍楼列表
2025-08-01 15:06:01.438 [http-nio-8080-exec-1] INFO  f.d.s.StudentDormitoryQueryService 131 开始获取已分配宿舍学生统计信息: buildingCode=null
2025-08-01 15:06:01.442 [http-nio-8080-exec-2] INFO  f.d.s.StudentDormitoryQueryService 62 没有找到符合条件的已分配宿舍学生
2025-08-01 15:06:01.442 [http-nio-8080-exec-1] INFO  f.d.s.StudentDormitoryQueryService 138 没有找到已分配宿舍的学生
2025-08-01 15:06:01.442 [http-nio-8080-exec-1] INFO  f.d.c.StudentDormitoryQueryController 234 获取已分配宿舍学生统计信息成功
2025-08-01 15:06:01.442 [http-nio-8080-exec-2] INFO  f.d.c.StudentDormitoryQueryController 102 查询已分配宿舍学生状态成功，总数: 0, 当前页: 0
2025-08-01 15:06:01.444 [http-nio-8080-exec-9] INFO  f.d.s.StudentDormitoryQueryService 206 获取到9个宿舍楼
2025-08-01 15:06:01.444 [http-nio-8080-exec-9] INFO  f.d.c.StudentDormitoryQueryController 201 获取宿舍楼列表成功，共9个楼栋
2025-08-01 15:06:22.792 [http-nio-8080-exec-6] INFO  f.d.c.StudentDormitoryQueryController 189 获取宿舍楼列表
2025-08-01 15:06:22.792 [http-nio-8080-exec-5] INFO  f.d.c.StudentDormitoryQueryController 223 获取已分配宿舍学生统计信息，楼栋: null
2025-08-01 15:06:22.792 [http-nio-8080-exec-3] INFO  f.d.c.StudentDormitoryQueryController 53 查询已分配宿舍学生状态，页码: 0, 大小: 20, 姓名: null, 楼栋: null, 状态: null
2025-08-01 15:06:22.792 [http-nio-8080-exec-6] INFO  f.d.s.StudentDormitoryQueryService 185 获取宿舍楼列表
2025-08-01 15:06:22.792 [http-nio-8080-exec-5] INFO  f.d.s.StudentDormitoryQueryService 131 开始获取已分配宿舍学生统计信息: buildingCode=null
2025-08-01 15:06:22.792 [http-nio-8080-exec-3] INFO  f.d.s.StudentDormitoryQueryService 55 开始查询已分配宿舍学生状态: personName=null, buildingCode=null, dormitoryStatus=null
2025-08-01 15:06:22.796 [http-nio-8080-exec-3] INFO  f.d.s.StudentDormitoryQueryService 62 没有找到符合条件的已分配宿舍学生
2025-08-01 15:06:22.796 [http-nio-8080-exec-5] INFO  f.d.s.StudentDormitoryQueryService 138 没有找到已分配宿舍的学生
2025-08-01 15:06:22.796 [http-nio-8080-exec-3] INFO  f.d.c.StudentDormitoryQueryController 102 查询已分配宿舍学生状态成功，总数: 0, 当前页: 0
2025-08-01 15:06:22.796 [http-nio-8080-exec-5] INFO  f.d.c.StudentDormitoryQueryController 234 获取已分配宿舍学生统计信息成功
2025-08-01 15:06:22.798 [http-nio-8080-exec-6] INFO  f.d.s.StudentDormitoryQueryService 206 获取到9个宿舍楼
2025-08-01 15:06:22.798 [http-nio-8080-exec-6] INFO  f.d.c.StudentDormitoryQueryController 201 获取宿舍楼列表成功，共9个楼栋
2025-08-01 15:06:47.983 [http-nio-8080-exec-4] INFO  f.d.c.StudentDormitoryQueryController 189 获取宿舍楼列表
2025-08-01 15:06:47.983 [http-nio-8080-exec-10] INFO  f.d.c.StudentDormitoryQueryController 223 获取已分配宿舍学生统计信息，楼栋: null
2025-08-01 15:06:47.983 [http-nio-8080-exec-7] INFO  f.d.c.StudentDormitoryQueryController 53 查询已分配宿舍学生状态，页码: 0, 大小: 20, 姓名: null, 楼栋: null, 状态: null
2025-08-01 15:06:47.983 [http-nio-8080-exec-4] INFO  f.d.s.StudentDormitoryQueryService 185 获取宿舍楼列表
2025-08-01 15:06:47.983 [http-nio-8080-exec-10] INFO  f.d.s.StudentDormitoryQueryService 131 开始获取已分配宿舍学生统计信息: buildingCode=null
2025-08-01 15:06:47.983 [http-nio-8080-exec-7] INFO  f.d.s.StudentDormitoryQueryService 55 开始查询已分配宿舍学生状态: personName=null, buildingCode=null, dormitoryStatus=null
2025-08-01 15:06:47.985 [http-nio-8080-exec-7] INFO  f.d.s.StudentDormitoryQueryService 62 没有找到符合条件的已分配宿舍学生
2025-08-01 15:06:47.986 [http-nio-8080-exec-7] INFO  f.d.c.StudentDormitoryQueryController 102 查询已分配宿舍学生状态成功，总数: 0, 当前页: 0
2025-08-01 15:06:47.987 [http-nio-8080-exec-10] INFO  f.d.s.StudentDormitoryQueryService 138 没有找到已分配宿舍的学生
2025-08-01 15:06:47.987 [http-nio-8080-exec-10] INFO  f.d.c.StudentDormitoryQueryController 234 获取已分配宿舍学生统计信息成功
2025-08-01 15:06:47.990 [http-nio-8080-exec-4] INFO  f.d.s.StudentDormitoryQueryService 206 获取到9个宿舍楼
2025-08-01 15:06:47.991 [http-nio-8080-exec-4] INFO  f.d.c.StudentDormitoryQueryController 201 获取宿舍楼列表成功，共9个楼栋
2025-08-01 15:07:32.225 [http-nio-8080-exec-8] INFO  f.d.c.StudentDormitoryQueryController 189 获取宿舍楼列表
2025-08-01 15:07:32.225 [http-nio-8080-exec-1] INFO  f.d.c.StudentDormitoryQueryController 53 查询已分配宿舍学生状态，页码: 0, 大小: 20, 姓名: null, 楼栋: null, 状态: null
2025-08-01 15:07:32.225 [http-nio-8080-exec-2] INFO  f.d.c.StudentDormitoryQueryController 223 获取已分配宿舍学生统计信息，楼栋: null
2025-08-01 15:07:32.225 [http-nio-8080-exec-8] INFO  f.d.s.StudentDormitoryQueryService 185 获取宿舍楼列表
2025-08-01 15:07:32.225 [http-nio-8080-exec-2] INFO  f.d.s.StudentDormitoryQueryService 131 开始获取已分配宿舍学生统计信息: buildingCode=null
2025-08-01 15:07:32.225 [http-nio-8080-exec-1] INFO  f.d.s.StudentDormitoryQueryService 55 开始查询已分配宿舍学生状态: personName=null, buildingCode=null, dormitoryStatus=null
2025-08-01 15:07:32.228 [http-nio-8080-exec-2] INFO  f.d.s.StudentDormitoryQueryService 138 没有找到已分配宿舍的学生
2025-08-01 15:07:32.232 [http-nio-8080-exec-2] INFO  f.d.c.StudentDormitoryQueryController 234 获取已分配宿舍学生统计信息成功
2025-08-01 15:07:32.235 [http-nio-8080-exec-1] INFO  f.d.s.StudentDormitoryQueryService 62 没有找到符合条件的已分配宿舍学生
2025-08-01 15:07:32.237 [http-nio-8080-exec-1] INFO  f.d.c.StudentDormitoryQueryController 102 查询已分配宿舍学生状态成功，总数: 0, 当前页: 0
2025-08-01 15:07:32.242 [http-nio-8080-exec-8] INFO  f.d.s.StudentDormitoryQueryService 206 获取到9个宿舍楼
2025-08-01 15:07:32.242 [http-nio-8080-exec-8] INFO  f.d.c.StudentDormitoryQueryController 201 获取宿舍楼列表成功，共9个楼栋
2025-08-01 15:08:07.969 [http-nio-8080-exec-9] INFO  f.d.c.StudentDormitoryQueryController 189 获取宿舍楼列表
2025-08-01 15:08:07.969 [http-nio-8080-exec-5] INFO  f.d.c.StudentDormitoryQueryController 223 获取已分配宿舍学生统计信息，楼栋: null
2025-08-01 15:08:07.969 [http-nio-8080-exec-3] INFO  f.d.c.StudentDormitoryQueryController 53 查询已分配宿舍学生状态，页码: 0, 大小: 20, 姓名: null, 楼栋: null, 状态: null
2025-08-01 15:08:07.969 [http-nio-8080-exec-9] INFO  f.d.s.StudentDormitoryQueryService 185 获取宿舍楼列表
2025-08-01 15:08:07.969 [http-nio-8080-exec-3] INFO  f.d.s.StudentDormitoryQueryService 55 开始查询已分配宿舍学生状态: personName=null, buildingCode=null, dormitoryStatus=null
2025-08-01 15:08:07.969 [http-nio-8080-exec-5] INFO  f.d.s.StudentDormitoryQueryService 131 开始获取已分配宿舍学生统计信息: buildingCode=null
2025-08-01 15:08:07.971 [http-nio-8080-exec-3] INFO  f.d.s.StudentDormitoryQueryService 62 没有找到符合条件的已分配宿舍学生
2025-08-01 15:08:07.971 [http-nio-8080-exec-5] INFO  f.d.s.StudentDormitoryQueryService 138 没有找到已分配宿舍的学生
2025-08-01 15:08:07.971 [http-nio-8080-exec-3] INFO  f.d.c.StudentDormitoryQueryController 102 查询已分配宿舍学生状态成功，总数: 0, 当前页: 0
2025-08-01 15:08:07.971 [http-nio-8080-exec-5] INFO  f.d.c.StudentDormitoryQueryController 234 获取已分配宿舍学生统计信息成功
2025-08-01 15:08:07.975 [http-nio-8080-exec-9] INFO  f.d.s.StudentDormitoryQueryService 206 获取到9个宿舍楼
2025-08-01 15:08:07.975 [http-nio-8080-exec-9] INFO  f.d.c.StudentDormitoryQueryController 201 获取宿舍楼列表成功，共9个楼栋
2025-08-01 15:08:17.711 [http-nio-8080-exec-10] INFO  f.d.c.StudentDormitoryQueryController 223 获取已分配宿舍学生统计信息，楼栋: null
2025-08-01 15:08:17.711 [http-nio-8080-exec-6] INFO  f.d.c.StudentDormitoryQueryController 189 获取宿舍楼列表
2025-08-01 15:08:17.711 [http-nio-8080-exec-7] INFO  f.d.c.StudentDormitoryQueryController 53 查询已分配宿舍学生状态，页码: 0, 大小: 20, 姓名: null, 楼栋: null, 状态: null
2025-08-01 15:08:17.711 [http-nio-8080-exec-10] INFO  f.d.s.StudentDormitoryQueryService 131 开始获取已分配宿舍学生统计信息: buildingCode=null
2025-08-01 15:08:17.711 [http-nio-8080-exec-7] INFO  f.d.s.StudentDormitoryQueryService 55 开始查询已分配宿舍学生状态: personName=null, buildingCode=null, dormitoryStatus=null
2025-08-01 15:08:17.711 [http-nio-8080-exec-6] INFO  f.d.s.StudentDormitoryQueryService 185 获取宿舍楼列表
2025-08-01 15:08:17.713 [http-nio-8080-exec-7] INFO  f.d.s.StudentDormitoryQueryService 62 没有找到符合条件的已分配宿舍学生
2025-08-01 15:08:17.713 [http-nio-8080-exec-10] INFO  f.d.s.StudentDormitoryQueryService 138 没有找到已分配宿舍的学生
2025-08-01 15:08:17.714 [http-nio-8080-exec-10] INFO  f.d.c.StudentDormitoryQueryController 234 获取已分配宿舍学生统计信息成功
2025-08-01 15:08:17.714 [http-nio-8080-exec-7] INFO  f.d.c.StudentDormitoryQueryController 102 查询已分配宿舍学生状态成功，总数: 0, 当前页: 0
2025-08-01 15:08:17.717 [http-nio-8080-exec-6] INFO  f.d.s.StudentDormitoryQueryService 206 获取到9个宿舍楼
2025-08-01 15:08:17.717 [http-nio-8080-exec-6] INFO  f.d.c.StudentDormitoryQueryController 201 获取宿舍楼列表成功，共9个楼栋
2025-08-01 15:08:29.178 [http-nio-8080-exec-4] INFO  f.d.c.StudentDormitoryQueryController 189 获取宿舍楼列表
2025-08-01 15:08:29.178 [http-nio-8080-exec-2] INFO  f.d.c.StudentDormitoryQueryController 223 获取已分配宿舍学生统计信息，楼栋: null
2025-08-01 15:08:29.178 [http-nio-8080-exec-4] INFO  f.d.s.StudentDormitoryQueryService 185 获取宿舍楼列表
2025-08-01 15:08:29.178 [http-nio-8080-exec-2] INFO  f.d.s.StudentDormitoryQueryService 131 开始获取已分配宿舍学生统计信息: buildingCode=null
2025-08-01 15:08:29.179 [http-nio-8080-exec-1] INFO  f.d.c.StudentDormitoryQueryController 53 查询已分配宿舍学生状态，页码: 0, 大小: 20, 姓名: null, 楼栋: null, 状态: null
2025-08-01 15:08:29.179 [http-nio-8080-exec-1] INFO  f.d.s.StudentDormitoryQueryService 55 开始查询已分配宿舍学生状态: personName=null, buildingCode=null, dormitoryStatus=null
2025-08-01 15:08:29.180 [http-nio-8080-exec-1] INFO  f.d.s.StudentDormitoryQueryService 62 没有找到符合条件的已分配宿舍学生
2025-08-01 15:08:29.180 [http-nio-8080-exec-1] INFO  f.d.c.StudentDormitoryQueryController 102 查询已分配宿舍学生状态成功，总数: 0, 当前页: 0
2025-08-01 15:08:29.181 [http-nio-8080-exec-2] INFO  f.d.s.StudentDormitoryQueryService 138 没有找到已分配宿舍的学生
2025-08-01 15:08:29.181 [http-nio-8080-exec-2] INFO  f.d.c.StudentDormitoryQueryController 234 获取已分配宿舍学生统计信息成功
2025-08-01 15:08:29.182 [http-nio-8080-exec-4] INFO  f.d.s.StudentDormitoryQueryService 206 获取到9个宿舍楼
2025-08-01 15:08:29.182 [http-nio-8080-exec-4] INFO  f.d.c.StudentDormitoryQueryController 201 获取宿舍楼列表成功，共9个楼栋
2025-08-01 15:08:40.894 [http-nio-8080-exec-8] INFO  f.d.c.StudentDormitoryQueryController 189 获取宿舍楼列表
2025-08-01 15:08:40.894 [http-nio-8080-exec-3] INFO  f.d.c.StudentDormitoryQueryController 223 获取已分配宿舍学生统计信息，楼栋: null
2025-08-01 15:08:40.895 [http-nio-8080-exec-8] INFO  f.d.s.StudentDormitoryQueryService 185 获取宿舍楼列表
2025-08-01 15:08:40.895 [http-nio-8080-exec-3] INFO  f.d.s.StudentDormitoryQueryService 131 开始获取已分配宿舍学生统计信息: buildingCode=null
2025-08-01 15:08:40.895 [http-nio-8080-exec-5] INFO  f.d.c.StudentDormitoryQueryController 53 查询已分配宿舍学生状态，页码: 0, 大小: 20, 姓名: null, 楼栋: null, 状态: null
2025-08-01 15:08:40.895 [http-nio-8080-exec-5] INFO  f.d.s.StudentDormitoryQueryService 55 开始查询已分配宿舍学生状态: personName=null, buildingCode=null, dormitoryStatus=null
2025-08-01 15:08:40.898 [http-nio-8080-exec-3] INFO  f.d.s.StudentDormitoryQueryService 138 没有找到已分配宿舍的学生
2025-08-01 15:08:40.898 [http-nio-8080-exec-5] INFO  f.d.s.StudentDormitoryQueryService 62 没有找到符合条件的已分配宿舍学生
2025-08-01 15:08:40.898 [http-nio-8080-exec-3] INFO  f.d.c.StudentDormitoryQueryController 234 获取已分配宿舍学生统计信息成功
2025-08-01 15:08:40.898 [http-nio-8080-exec-5] INFO  f.d.c.StudentDormitoryQueryController 102 查询已分配宿舍学生状态成功，总数: 0, 当前页: 0
2025-08-01 15:08:40.900 [http-nio-8080-exec-8] INFO  f.d.s.StudentDormitoryQueryService 206 获取到9个宿舍楼
2025-08-01 15:08:40.901 [http-nio-8080-exec-8] INFO  f.d.c.StudentDormitoryQueryController 201 获取宿舍楼列表成功，共9个楼栋
2025-08-01 15:10:22.613 [http-nio-8080-exec-9] INFO  f.d.c.StudentDormitoryQueryController 189 获取宿舍楼列表
2025-08-01 15:10:22.614 [http-nio-8080-exec-9] INFO  f.d.s.StudentDormitoryQueryService 185 获取宿舍楼列表
2025-08-01 15:10:22.617 [http-nio-8080-exec-10] INFO  f.d.c.StudentDormitoryQueryController 223 获取已分配宿舍学生统计信息，楼栋: null
2025-08-01 15:10:22.617 [http-nio-8080-exec-7] INFO  f.d.c.StudentDormitoryQueryController 53 查询已分配宿舍学生状态，页码: 0, 大小: 20, 姓名: null, 楼栋: null, 状态: null
2025-08-01 15:10:22.617 [http-nio-8080-exec-7] INFO  f.d.s.StudentDormitoryQueryService 55 开始查询已分配宿舍学生状态: personName=null, buildingCode=null, dormitoryStatus=null
2025-08-01 15:10:22.617 [http-nio-8080-exec-10] INFO  f.d.s.StudentDormitoryQueryService 131 开始获取已分配宿舍学生统计信息: buildingCode=null
2025-08-01 15:10:22.620 [http-nio-8080-exec-7] INFO  f.d.s.StudentDormitoryQueryService 62 没有找到符合条件的已分配宿舍学生
2025-08-01 15:10:22.620 [http-nio-8080-exec-7] INFO  f.d.c.StudentDormitoryQueryController 102 查询已分配宿舍学生状态成功，总数: 0, 当前页: 0
2025-08-01 15:10:22.620 [http-nio-8080-exec-9] INFO  f.d.s.StudentDormitoryQueryService 206 获取到9个宿舍楼
2025-08-01 15:10:22.620 [http-nio-8080-exec-10] INFO  f.d.s.StudentDormitoryQueryService 138 没有找到已分配宿舍的学生
2025-08-01 15:10:22.621 [http-nio-8080-exec-9] INFO  f.d.c.StudentDormitoryQueryController 201 获取宿舍楼列表成功，共9个楼栋
2025-08-01 15:10:22.621 [http-nio-8080-exec-10] INFO  f.d.c.StudentDormitoryQueryController 234 获取已分配宿舍学生统计信息成功
2025-08-01 15:11:03.922 [http-nio-8080-exec-6] INFO  f.d.c.StudentDormitoryQueryController 189 获取宿舍楼列表
2025-08-01 15:11:03.922 [http-nio-8080-exec-6] INFO  f.d.s.StudentDormitoryQueryService 185 获取宿舍楼列表
2025-08-01 15:11:03.924 [http-nio-8080-exec-1] INFO  f.d.c.StudentDormitoryQueryController 53 查询已分配宿舍学生状态，页码: 0, 大小: 20, 姓名: null, 楼栋: null, 状态: null
2025-08-01 15:11:03.924 [http-nio-8080-exec-2] INFO  f.d.c.StudentDormitoryQueryController 223 获取已分配宿舍学生统计信息，楼栋: null
2025-08-01 15:11:03.924 [http-nio-8080-exec-2] INFO  f.d.s.StudentDormitoryQueryService 131 开始获取已分配宿舍学生统计信息: buildingCode=null
2025-08-01 15:11:03.924 [http-nio-8080-exec-1] INFO  f.d.s.StudentDormitoryQueryService 55 开始查询已分配宿舍学生状态: personName=null, buildingCode=null, dormitoryStatus=null
2025-08-01 15:11:03.926 [http-nio-8080-exec-1] INFO  f.d.s.StudentDormitoryQueryService 62 没有找到符合条件的已分配宿舍学生
2025-08-01 15:11:03.926 [http-nio-8080-exec-1] INFO  f.d.c.StudentDormitoryQueryController 102 查询已分配宿舍学生状态成功，总数: 0, 当前页: 0
2025-08-01 15:11:03.927 [http-nio-8080-exec-2] INFO  f.d.s.StudentDormitoryQueryService 138 没有找到已分配宿舍的学生
2025-08-01 15:11:03.927 [http-nio-8080-exec-6] INFO  f.d.s.StudentDormitoryQueryService 206 获取到9个宿舍楼
2025-08-01 15:11:03.927 [http-nio-8080-exec-2] INFO  f.d.c.StudentDormitoryQueryController 234 获取已分配宿舍学生统计信息成功
2025-08-01 15:11:03.927 [http-nio-8080-exec-6] INFO  f.d.c.StudentDormitoryQueryController 201 获取宿舍楼列表成功，共9个楼栋
2025-08-01 15:15:34.434 [http-nio-8080-exec-4] INFO  f.d.c.StudentDormitoryQueryController 189 获取宿舍楼列表
2025-08-01 15:15:34.435 [http-nio-8080-exec-4] INFO  f.d.s.StudentDormitoryQueryService 185 获取宿舍楼列表
2025-08-01 15:15:34.439 [http-nio-8080-exec-4] INFO  f.d.s.StudentDormitoryQueryService 206 获取到9个宿舍楼
2025-08-01 15:15:34.440 [http-nio-8080-exec-4] INFO  f.d.c.StudentDormitoryQueryController 201 获取宿舍楼列表成功，共9个楼栋
2025-08-01 15:15:34.441 [http-nio-8080-exec-3] INFO  f.d.c.StudentDormitoryQueryController 223 获取已分配宿舍学生统计信息，楼栋: null
2025-08-01 15:15:34.442 [http-nio-8080-exec-5] INFO  f.d.c.StudentDormitoryQueryController 53 查询已分配宿舍学生状态，页码: 0, 大小: 20, 姓名: null, 楼栋: null, 状态: null
2025-08-01 15:15:34.442 [http-nio-8080-exec-3] INFO  f.d.s.StudentDormitoryQueryService 131 开始获取已分配宿舍学生统计信息: buildingCode=null
2025-08-01 15:15:34.442 [http-nio-8080-exec-5] INFO  f.d.s.StudentDormitoryQueryService 55 开始查询已分配宿舍学生状态: personName=null, buildingCode=null, dormitoryStatus=null
2025-08-01 15:15:34.444 [http-nio-8080-exec-3] INFO  f.d.s.StudentDormitoryQueryService 138 没有找到已分配宿舍的学生
2025-08-01 15:15:34.444 [http-nio-8080-exec-5] INFO  f.d.s.StudentDormitoryQueryService 62 没有找到符合条件的已分配宿舍学生
2025-08-01 15:15:34.445 [http-nio-8080-exec-3] INFO  f.d.c.StudentDormitoryQueryController 234 获取已分配宿舍学生统计信息成功
2025-08-01 15:15:34.445 [http-nio-8080-exec-5] INFO  f.d.c.StudentDormitoryQueryController 102 查询已分配宿舍学生状态成功，总数: 0, 当前页: 0
2025-08-01 15:15:39.162 [http-nio-8080-exec-8] INFO  f.d.c.StudentDormitoryQueryController 53 查询已分配宿舍学生状态，页码: 0, 大小: 20, 姓名: null, 楼栋: 4e8591e829ec48f7bd178d442cf49ecc, 状态: null
2025-08-01 15:15:39.162 [http-nio-8080-exec-7] INFO  f.d.c.StudentDormitoryQueryController 223 获取已分配宿舍学生统计信息，楼栋: 4e8591e829ec48f7bd178d442cf49ecc
2025-08-01 15:15:39.162 [http-nio-8080-exec-7] INFO  f.d.s.StudentDormitoryQueryService 131 开始获取已分配宿舍学生统计信息: buildingCode=4e8591e829ec48f7bd178d442cf49ecc
2025-08-01 15:15:39.162 [http-nio-8080-exec-8] INFO  f.d.s.StudentDormitoryQueryService 55 开始查询已分配宿舍学生状态: personName=null, buildingCode=4e8591e829ec48f7bd178d442cf49ecc, dormitoryStatus=null
2025-08-01 15:15:39.166 [http-nio-8080-exec-8] INFO  f.d.s.StudentDormitoryQueryService 62 没有找到符合条件的已分配宿舍学生
2025-08-01 15:15:39.166 [http-nio-8080-exec-7] INFO  f.d.s.StudentDormitoryQueryService 138 没有找到已分配宿舍的学生
2025-08-01 15:15:39.166 [http-nio-8080-exec-7] INFO  f.d.c.StudentDormitoryQueryController 234 获取已分配宿舍学生统计信息成功
2025-08-01 15:15:39.166 [http-nio-8080-exec-8] INFO  f.d.c.StudentDormitoryQueryController 102 查询已分配宿舍学生状态成功，总数: 0, 当前页: 0
2025-08-01 15:15:39.597 [http-nio-8080-exec-9] INFO  f.d.c.StudentDormitoryQueryController 53 查询已分配宿舍学生状态，页码: 0, 大小: 100, 姓名: null, 楼栋: 4e8591e829ec48f7bd178d442cf49ecc, 状态: null
2025-08-01 15:15:39.597 [http-nio-8080-exec-10] INFO  f.d.c.StudentDormitoryQueryController 53 查询已分配宿舍学生状态，页码: 0, 大小: 100, 姓名: null, 楼栋: 96a972c28e5c444b937d9409ec996432, 状态: null
2025-08-01 15:15:39.597 [http-nio-8080-exec-9] INFO  f.d.s.StudentDormitoryQueryService 55 开始查询已分配宿舍学生状态: personName=null, buildingCode=4e8591e829ec48f7bd178d442cf49ecc, dormitoryStatus=null
2025-08-01 15:15:39.597 [http-nio-8080-exec-10] INFO  f.d.s.StudentDormitoryQueryService 55 开始查询已分配宿舍学生状态: personName=null, buildingCode=96a972c28e5c444b937d9409ec996432, dormitoryStatus=null
2025-08-01 15:15:39.598 [http-nio-8080-exec-1] INFO  f.d.c.DashboardController 105 获取多楼栋统计数据（优化版本）: buildingCodes=[4e8591e829ec48f7bd178d442cf49ecc, 96a972c28e5c444b937d9409ec996432]
2025-08-01 15:15:39.598 [http-nio-8080-exec-1] INFO  f.d.c.DashboardController 120 使用优化查询逻辑获取指定楼栋统计数据
2025-08-01 15:15:39.601 [http-nio-8080-exec-10] INFO  f.d.s.StudentDormitoryQueryService 62 没有找到符合条件的已分配宿舍学生
2025-08-01 15:15:39.601 [http-nio-8080-exec-9] INFO  f.d.s.StudentDormitoryQueryService 62 没有找到符合条件的已分配宿舍学生
2025-08-01 15:15:39.602 [http-nio-8080-exec-10] INFO  f.d.c.StudentDormitoryQueryController 102 查询已分配宿舍学生状态成功，总数: 0, 当前页: 0
2025-08-01 15:15:39.602 [http-nio-8080-exec-9] INFO  f.d.c.StudentDormitoryQueryController 102 查询已分配宿舍学生状态成功，总数: 0, 当前页: 0
2025-08-01 15:15:39.606 [http-nio-8080-exec-1] INFO  f.d.s.CachedDormitoryStatsService 573 开始获取指定楼栋的统计信息（优化版本）: buildingCodes=[4e8591e829ec48f7bd178d442cf49ecc, 96a972c28e5c444b937d9409ec996432]
2025-08-01 15:15:39.608 [http-nio-8080-exec-1] INFO  f.d.s.CachedDormitoryStatsService 592 有效楼栋代码: [4e8591e829ec48f7bd178d442cf49ecc, 96a972c28e5c444b937d9409ec996432]
2025-08-01 15:15:39.608 [http-nio-8080-exec-1] INFO  f.d.s.CachedDormitoryStatsService 671 开始计算指定楼栋统计（优化版本）: [4e8591e829ec48f7bd178d442cf49ecc, 96a972c28e5c444b937d9409ec996432]
2025-08-01 15:15:39.617 [http-nio-8080-exec-1] INFO  f.d.s.CachedDormitoryStatsService 679 楼栋[4e8591e829ec48f7bd178d442cf49ecc, 96a972c28e5c444b937d9409ec996432]共有0名已分配宿舍的学生
2025-08-01 15:15:39.619 [http-nio-8080-exec-1] INFO  f.d.s.CachedDormitoryStatsService 619 指定楼栋统计信息计算完成（优化版本）: 楼栋=[4e8591e829ec48f7bd178d442cf49ecc, 96a972c28e5c444b937d9409ec996432], 耗时=11ms
2025-08-01 15:15:39.619 [http-nio-8080-exec-1] INFO  f.d.c.DashboardController 162 多楼栋统计数据获取成功（优化版本）: 楼栋=[4e8591e829ec48f7bd178d442cf49ecc, 96a972c28e5c444b937d9409ec996432], 总人数=0, 在寝=0, 外出=0, 无记录=0, 归寝率=0.0%, 接口耗时=21ms
2025-08-01 15:15:41.098 [http-nio-8080-exec-2] INFO  f.d.c.StudentDormitoryQueryController 53 查询已分配宿舍学生状态，页码: 0, 大小: 100, 姓名: null, 楼栋: 4e8591e829ec48f7bd178d442cf49ecc, 状态: null
2025-08-01 15:15:41.098 [http-nio-8080-exec-2] INFO  f.d.s.StudentDormitoryQueryService 55 开始查询已分配宿舍学生状态: personName=null, buildingCode=4e8591e829ec48f7bd178d442cf49ecc, dormitoryStatus=null
2025-08-01 15:15:41.099 [http-nio-8080-exec-6] INFO  f.d.c.StudentDormitoryQueryController 53 查询已分配宿舍学生状态，页码: 0, 大小: 100, 姓名: null, 楼栋: 96a972c28e5c444b937d9409ec996432, 状态: null
2025-08-01 15:15:41.099 [http-nio-8080-exec-6] INFO  f.d.s.StudentDormitoryQueryService 55 开始查询已分配宿舍学生状态: personName=null, buildingCode=96a972c28e5c444b937d9409ec996432, dormitoryStatus=null
2025-08-01 15:15:41.099 [http-nio-8080-exec-4] INFO  f.d.c.StudentDormitoryQueryController 53 查询已分配宿舍学生状态，页码: 0, 大小: 100, 姓名: null, 楼栋: 2fa26b3b40e3410a8a9e3ed0bc4fcbc3, 状态: null
2025-08-01 15:15:41.099 [http-nio-8080-exec-4] INFO  f.d.s.StudentDormitoryQueryService 55 开始查询已分配宿舍学生状态: personName=null, buildingCode=2fa26b3b40e3410a8a9e3ed0bc4fcbc3, dormitoryStatus=null
2025-08-01 15:15:41.103 [http-nio-8080-exec-3] INFO  f.d.c.DashboardController 105 获取多楼栋统计数据（优化版本）: buildingCodes=[4e8591e829ec48f7bd178d442cf49ecc, 96a972c28e5c444b937d9409ec996432, 2fa26b3b40e3410a8a9e3ed0bc4fcbc3]
2025-08-01 15:15:41.103 [http-nio-8080-exec-3] INFO  f.d.c.DashboardController 120 使用优化查询逻辑获取指定楼栋统计数据
2025-08-01 15:15:41.104 [http-nio-8080-exec-4] INFO  f.d.s.StudentDormitoryQueryService 62 没有找到符合条件的已分配宿舍学生
2025-08-01 15:15:41.104 [http-nio-8080-exec-6] INFO  f.d.s.StudentDormitoryQueryService 62 没有找到符合条件的已分配宿舍学生
2025-08-01 15:15:41.104 [http-nio-8080-exec-2] INFO  f.d.s.StudentDormitoryQueryService 62 没有找到符合条件的已分配宿舍学生
2025-08-01 15:15:41.104 [http-nio-8080-exec-4] INFO  f.d.c.StudentDormitoryQueryController 102 查询已分配宿舍学生状态成功，总数: 0, 当前页: 0
2025-08-01 15:15:41.104 [http-nio-8080-exec-2] INFO  f.d.c.StudentDormitoryQueryController 102 查询已分配宿舍学生状态成功，总数: 0, 当前页: 0
2025-08-01 15:15:41.104 [http-nio-8080-exec-3] INFO  f.d.s.CachedDormitoryStatsService 573 开始获取指定楼栋的统计信息（优化版本）: buildingCodes=[4e8591e829ec48f7bd178d442cf49ecc, 96a972c28e5c444b937d9409ec996432, 2fa26b3b40e3410a8a9e3ed0bc4fcbc3]
2025-08-01 15:15:41.104 [http-nio-8080-exec-6] INFO  f.d.c.StudentDormitoryQueryController 102 查询已分配宿舍学生状态成功，总数: 0, 当前页: 0
2025-08-01 15:15:41.105 [http-nio-8080-exec-3] INFO  f.d.s.CachedDormitoryStatsService 592 有效楼栋代码: [4e8591e829ec48f7bd178d442cf49ecc, 96a972c28e5c444b937d9409ec996432, 2fa26b3b40e3410a8a9e3ed0bc4fcbc3]
2025-08-01 15:15:41.105 [http-nio-8080-exec-3] INFO  f.d.s.CachedDormitoryStatsService 671 开始计算指定楼栋统计（优化版本）: [4e8591e829ec48f7bd178d442cf49ecc, 96a972c28e5c444b937d9409ec996432, 2fa26b3b40e3410a8a9e3ed0bc4fcbc3]
2025-08-01 15:15:41.109 [http-nio-8080-exec-3] INFO  f.d.s.CachedDormitoryStatsService 679 楼栋[4e8591e829ec48f7bd178d442cf49ecc, 96a972c28e5c444b937d9409ec996432, 2fa26b3b40e3410a8a9e3ed0bc4fcbc3]共有0名已分配宿舍的学生
2025-08-01 15:15:41.110 [http-nio-8080-exec-3] INFO  f.d.s.CachedDormitoryStatsService 619 指定楼栋统计信息计算完成（优化版本）: 楼栋=[4e8591e829ec48f7bd178d442cf49ecc, 96a972c28e5c444b937d9409ec996432, 2fa26b3b40e3410a8a9e3ed0bc4fcbc3], 耗时=6ms
2025-08-01 15:15:41.110 [http-nio-8080-exec-3] INFO  f.d.c.DashboardController 162 多楼栋统计数据获取成功（优化版本）: 楼栋=[4e8591e829ec48f7bd178d442cf49ecc, 96a972c28e5c444b937d9409ec996432, 2fa26b3b40e3410a8a9e3ed0bc4fcbc3], 总人数=0, 在寝=0, 外出=0, 无记录=0, 归寝率=0.0%, 接口耗时=7ms
2025-08-01 15:15:42.434 [http-nio-8080-exec-5] INFO  f.d.c.StudentDormitoryQueryController 53 查询已分配宿舍学生状态，页码: 0, 大小: 100, 姓名: null, 楼栋: 96a972c28e5c444b937d9409ec996432, 状态: null
2025-08-01 15:15:42.434 [http-nio-8080-exec-5] INFO  f.d.s.StudentDormitoryQueryService 55 开始查询已分配宿舍学生状态: personName=null, buildingCode=96a972c28e5c444b937d9409ec996432, dormitoryStatus=null
2025-08-01 15:15:42.435 [http-nio-8080-exec-8] INFO  f.d.c.StudentDormitoryQueryController 53 查询已分配宿舍学生状态，页码: 0, 大小: 100, 姓名: null, 楼栋: 2fa26b3b40e3410a8a9e3ed0bc4fcbc3, 状态: null
2025-08-01 15:15:42.436 [http-nio-8080-exec-8] INFO  f.d.s.StudentDormitoryQueryService 55 开始查询已分配宿舍学生状态: personName=null, buildingCode=2fa26b3b40e3410a8a9e3ed0bc4fcbc3, dormitoryStatus=null
2025-08-01 15:15:42.438 [http-nio-8080-exec-7] INFO  f.d.c.DashboardController 105 获取多楼栋统计数据（优化版本）: buildingCodes=[96a972c28e5c444b937d9409ec996432, 2fa26b3b40e3410a8a9e3ed0bc4fcbc3]
2025-08-01 15:15:42.438 [http-nio-8080-exec-7] INFO  f.d.c.DashboardController 120 使用优化查询逻辑获取指定楼栋统计数据
2025-08-01 15:15:42.438 [http-nio-8080-exec-5] INFO  f.d.s.StudentDormitoryQueryService 62 没有找到符合条件的已分配宿舍学生
2025-08-01 15:15:42.438 [http-nio-8080-exec-7] INFO  f.d.s.CachedDormitoryStatsService 573 开始获取指定楼栋的统计信息（优化版本）: buildingCodes=[96a972c28e5c444b937d9409ec996432, 2fa26b3b40e3410a8a9e3ed0bc4fcbc3]
2025-08-01 15:15:42.439 [http-nio-8080-exec-7] INFO  f.d.s.CachedDormitoryStatsService 592 有效楼栋代码: [96a972c28e5c444b937d9409ec996432, 2fa26b3b40e3410a8a9e3ed0bc4fcbc3]
2025-08-01 15:15:42.439 [http-nio-8080-exec-5] INFO  f.d.c.StudentDormitoryQueryController 102 查询已分配宿舍学生状态成功，总数: 0, 当前页: 0
2025-08-01 15:15:42.439 [http-nio-8080-exec-7] INFO  f.d.s.CachedDormitoryStatsService 671 开始计算指定楼栋统计（优化版本）: [96a972c28e5c444b937d9409ec996432, 2fa26b3b40e3410a8a9e3ed0bc4fcbc3]
2025-08-01 15:15:42.439 [http-nio-8080-exec-8] INFO  f.d.s.StudentDormitoryQueryService 62 没有找到符合条件的已分配宿舍学生
2025-08-01 15:15:42.439 [http-nio-8080-exec-8] INFO  f.d.c.StudentDormitoryQueryController 102 查询已分配宿舍学生状态成功，总数: 0, 当前页: 0
2025-08-01 15:15:42.442 [http-nio-8080-exec-7] INFO  f.d.s.CachedDormitoryStatsService 679 楼栋[96a972c28e5c444b937d9409ec996432, 2fa26b3b40e3410a8a9e3ed0bc4fcbc3]共有0名已分配宿舍的学生
2025-08-01 15:15:42.443 [http-nio-8080-exec-7] INFO  f.d.s.CachedDormitoryStatsService 619 指定楼栋统计信息计算完成（优化版本）: 楼栋=[96a972c28e5c444b937d9409ec996432, 2fa26b3b40e3410a8a9e3ed0bc4fcbc3], 耗时=5ms
2025-08-01 15:15:42.443 [http-nio-8080-exec-7] INFO  f.d.c.DashboardController 162 多楼栋统计数据获取成功（优化版本）: 楼栋=[96a972c28e5c444b937d9409ec996432, 2fa26b3b40e3410a8a9e3ed0bc4fcbc3], 总人数=0, 在寝=0, 外出=0, 无记录=0, 归寝率=0.0%, 接口耗时=5ms
2025-08-01 15:15:42.775 [http-nio-8080-exec-9] INFO  f.d.c.StudentDormitoryQueryController 53 查询已分配宿舍学生状态，页码: 0, 大小: 20, 姓名: null, 楼栋: 2fa26b3b40e3410a8a9e3ed0bc4fcbc3, 状态: null
2025-08-01 15:15:42.775 [http-nio-8080-exec-9] INFO  f.d.s.StudentDormitoryQueryService 55 开始查询已分配宿舍学生状态: personName=null, buildingCode=2fa26b3b40e3410a8a9e3ed0bc4fcbc3, dormitoryStatus=null
2025-08-01 15:15:42.775 [http-nio-8080-exec-10] INFO  f.d.c.StudentDormitoryQueryController 223 获取已分配宿舍学生统计信息，楼栋: 2fa26b3b40e3410a8a9e3ed0bc4fcbc3
2025-08-01 15:15:42.775 [http-nio-8080-exec-10] INFO  f.d.s.StudentDormitoryQueryService 131 开始获取已分配宿舍学生统计信息: buildingCode=2fa26b3b40e3410a8a9e3ed0bc4fcbc3
2025-08-01 15:15:42.777 [http-nio-8080-exec-9] INFO  f.d.s.StudentDormitoryQueryService 62 没有找到符合条件的已分配宿舍学生
2025-08-01 15:15:42.778 [http-nio-8080-exec-9] INFO  f.d.c.StudentDormitoryQueryController 102 查询已分配宿舍学生状态成功，总数: 0, 当前页: 0
2025-08-01 15:15:42.778 [http-nio-8080-exec-10] INFO  f.d.s.StudentDormitoryQueryService 138 没有找到已分配宿舍的学生
2025-08-01 15:15:42.778 [http-nio-8080-exec-10] INFO  f.d.c.StudentDormitoryQueryController 234 获取已分配宿舍学生统计信息成功
2025-08-01 15:15:43.186 [http-nio-8080-exec-1] INFO  f.d.c.StudentDormitoryQueryController 53 查询已分配宿舍学生状态，页码: 0, 大小: 20, 姓名: null, 楼栋: null, 状态: null
2025-08-01 15:15:43.186 [http-nio-8080-exec-2] INFO  f.d.c.StudentDormitoryQueryController 223 获取已分配宿舍学生统计信息，楼栋: null
2025-08-01 15:15:43.186 [http-nio-8080-exec-2] INFO  f.d.s.StudentDormitoryQueryService 131 开始获取已分配宿舍学生统计信息: buildingCode=null
2025-08-01 15:15:43.186 [http-nio-8080-exec-1] INFO  f.d.s.StudentDormitoryQueryService 55 开始查询已分配宿舍学生状态: personName=null, buildingCode=null, dormitoryStatus=null
2025-08-01 15:15:43.188 [http-nio-8080-exec-2] INFO  f.d.s.StudentDormitoryQueryService 138 没有找到已分配宿舍的学生
2025-08-01 15:15:43.189 [http-nio-8080-exec-2] INFO  f.d.c.StudentDormitoryQueryController 234 获取已分配宿舍学生统计信息成功
2025-08-01 15:15:43.189 [http-nio-8080-exec-1] INFO  f.d.s.StudentDormitoryQueryService 62 没有找到符合条件的已分配宿舍学生
2025-08-01 15:15:43.189 [http-nio-8080-exec-1] INFO  f.d.c.StudentDormitoryQueryController 102 查询已分配宿舍学生状态成功，总数: 0, 当前页: 0
2025-08-01 15:15:44.803 [http-nio-8080-exec-4] INFO  f.d.c.DashboardApiController 39 获取楼栋列表请求
2025-08-01 15:15:44.810 [http-nio-8080-exec-4] INFO  f.d.s.DormitoryService 507 查询楼栋列表成功，共22个楼栋
2025-08-01 15:15:44.811 [http-nio-8080-exec-4] INFO  f.d.c.DashboardApiController 57 获取楼栋列表成功，共23个楼栋
2025-08-01 15:15:44.906 [http-nio-8080-exec-6] INFO  f.d.c.DashboardController 309 统一大屏数据查询: buildingCodes=null, forceRefresh=false
2025-08-01 15:15:44.906 [http-nio-8080-exec-6] INFO  f.d.c.DashboardController 326 查询全部统计数据
2025-08-01 15:15:44.906 [http-nio-8080-exec-6] INFO  f.d.s.CachedDormitoryStatsService 62 开始获取基于已分配宿舍学生的统计信息（优化版本）
2025-08-01 15:15:44.938 [http-nio-8080-exec-6] INFO  f.d.s.CachedDormitoryStatsService 334 开始计算基于已分配宿舍学生的统计信息
2025-08-01 15:15:44.942 [http-nio-8080-exec-6] INFO  f.d.s.CachedDormitoryStatsService 342 已分配宿舍的学生总数: 0
2025-08-01 15:15:44.965 [http-nio-8080-exec-6] INFO  f.d.s.CachedDormitoryStatsService 84 基于已分配宿舍学生的统计信息计算并缓存完成，耗时: 59ms
2025-08-01 15:15:44.966 [http-nio-8080-exec-6] INFO  f.d.c.DashboardController 373 统一大屏数据查询成功: 楼栋=null, 总人数=0, 在寝=0, 外出=0, 无记录=0, 归寝率=0.0%, 刷新=false, 耗时=60ms
2025-08-01 15:15:45.479 [http-nio-8080-exec-3] INFO  f.d.c.WebSocketStatusController 46 WebSocket状态查询: 连接数=0
2025-08-01 15:15:45.487 [http-nio-8080-exec-5] INFO  f.d.s.CachedDormitoryStatsService 62 开始获取基于已分配宿舍学生的统计信息（优化版本）
2025-08-01 15:15:45.510 [http-nio-8080-exec-5] INFO  f.d.s.CachedDormitoryStatsService 70 从缓存获取统计信息成功，耗时: 23ms
2025-08-01 15:15:45.694 [http-nio-8080-exec-8] INFO  f.d.w.DashboardWebSocketHandler 39 WebSocket连接建立: sessionId=1431e92c-57db-f0e2-6984-ffa692b1e660, 当前连接数: 1
2025-08-01 15:15:50.370 [http-nio-8080-exec-7] INFO  f.d.w.DashboardWebSocketHandler 66 WebSocket连接关闭: sessionId=1431e92c-57db-f0e2-6984-ffa692b1e660, status=CloseStatus[code=1000, reason=手动断开], 当前连接数: 0
2025-08-01 15:15:50.389 [http-nio-8080-exec-9] INFO  f.d.c.StudentDormitoryQueryController 189 获取宿舍楼列表
2025-08-01 15:15:50.389 [http-nio-8080-exec-10] INFO  f.d.c.StudentDormitoryQueryController 53 查询已分配宿舍学生状态，页码: 0, 大小: 20, 姓名: null, 楼栋: null, 状态: null
2025-08-01 15:15:50.389 [http-nio-8080-exec-10] INFO  f.d.s.StudentDormitoryQueryService 55 开始查询已分配宿舍学生状态: personName=null, buildingCode=null, dormitoryStatus=null
2025-08-01 15:15:50.389 [http-nio-8080-exec-9] INFO  f.d.s.StudentDormitoryQueryService 185 获取宿舍楼列表
2025-08-01 15:15:50.390 [http-nio-8080-exec-2] INFO  f.d.c.StudentDormitoryQueryController 223 获取已分配宿舍学生统计信息，楼栋: null
2025-08-01 15:15:50.390 [http-nio-8080-exec-2] INFO  f.d.s.StudentDormitoryQueryService 131 开始获取已分配宿舍学生统计信息: buildingCode=null
2025-08-01 15:15:50.392 [http-nio-8080-exec-10] INFO  f.d.s.StudentDormitoryQueryService 62 没有找到符合条件的已分配宿舍学生
2025-08-01 15:15:50.393 [http-nio-8080-exec-10] INFO  f.d.c.StudentDormitoryQueryController 102 查询已分配宿舍学生状态成功，总数: 0, 当前页: 0
2025-08-01 15:15:50.393 [http-nio-8080-exec-2] INFO  f.d.s.StudentDormitoryQueryService 138 没有找到已分配宿舍的学生
2025-08-01 15:15:50.394 [http-nio-8080-exec-2] INFO  f.d.c.StudentDormitoryQueryController 234 获取已分配宿舍学生统计信息成功
2025-08-01 15:15:50.395 [http-nio-8080-exec-9] INFO  f.d.s.StudentDormitoryQueryService 206 获取到9个宿舍楼
2025-08-01 15:15:50.396 [http-nio-8080-exec-9] INFO  f.d.c.StudentDormitoryQueryController 201 获取宿舍楼列表成功，共9个楼栋
2025-08-01 15:16:35.543 [http-nio-8080-exec-6] INFO  f.d.c.StudentDormitoryQueryController 189 获取宿舍楼列表
2025-08-01 15:16:35.543 [http-nio-8080-exec-6] INFO  f.d.s.StudentDormitoryQueryService 185 获取宿舍楼列表
2025-08-01 15:16:35.544 [http-nio-8080-exec-5] INFO  f.d.c.StudentDormitoryQueryController 223 获取已分配宿舍学生统计信息，楼栋: null
2025-08-01 15:16:35.544 [http-nio-8080-exec-3] INFO  f.d.c.StudentDormitoryQueryController 53 查询已分配宿舍学生状态，页码: 0, 大小: 20, 姓名: null, 楼栋: null, 状态: null
2025-08-01 15:16:35.544 [http-nio-8080-exec-5] INFO  f.d.s.StudentDormitoryQueryService 131 开始获取已分配宿舍学生统计信息: buildingCode=null
2025-08-01 15:16:35.544 [http-nio-8080-exec-3] INFO  f.d.s.StudentDormitoryQueryService 55 开始查询已分配宿舍学生状态: personName=null, buildingCode=null, dormitoryStatus=null
2025-08-01 15:16:35.547 [http-nio-8080-exec-3] INFO  f.d.s.StudentDormitoryQueryService 62 没有找到符合条件的已分配宿舍学生
2025-08-01 15:16:35.547 [http-nio-8080-exec-5] INFO  f.d.s.StudentDormitoryQueryService 138 没有找到已分配宿舍的学生
2025-08-01 15:16:35.547 [http-nio-8080-exec-3] INFO  f.d.c.StudentDormitoryQueryController 102 查询已分配宿舍学生状态成功，总数: 0, 当前页: 0
2025-08-01 15:16:35.547 [http-nio-8080-exec-5] INFO  f.d.c.StudentDormitoryQueryController 234 获取已分配宿舍学生统计信息成功
2025-08-01 15:16:35.548 [http-nio-8080-exec-6] INFO  f.d.s.StudentDormitoryQueryService 206 获取到9个宿舍楼
2025-08-01 15:16:35.548 [http-nio-8080-exec-6] INFO  f.d.c.StudentDormitoryQueryController 201 获取宿舍楼列表成功，共9个楼栋
2025-08-01 15:28:01.755 [Thread-22] INFO  c.b.d.d.DynamicRoutingDataSource 211 dynamic-datasource start closing ....
2025-08-01 15:28:01.758 [Thread-22] INFO  c.b.d.d.DynamicRoutingDataSource 215 dynamic-datasource all closed success,bye
2025-08-01 15:28:08.306 [main] INFO  f.d.StartMain 50 Starting StartMain on PC-20230626VQFC with PID 18224 (E:\Jkxy\demo\target\classes started by Administrator in E:\Jkxy\demo)
2025-08-01 15:28:08.313 [main] INFO  f.d.StartMain 684 The following profiles are active: dev
2025-08-01 15:28:10.192 [main] INFO  o.a.c.h.Http11NioProtocol 180 Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 15:28:10.205 [main] INFO  o.a.c.c.StandardService 180 Starting service [Tomcat]
2025-08-01 15:28:10.206 [main] INFO  o.a.c.c.StandardEngine 180 Starting Servlet Engine: Apache Tomcat/8.5.34
2025-08-01 15:28:10.216 [localhost-startStop-1] INFO  o.a.c.c.AprLifecycleListener 180 The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: [D:\Environment\java8\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;D:\Environment\ZenTao\bin\git\cmd;D:\Environment\ZenTao\bin\sliksvn;D:\Environment\ZenTao\zbox\nssm\win32;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\Tools\VMware\bin\;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;D:\Environment\Git\cmd;E:\nodejsFile\node_global;D:\Environment\redis\;D:\Environment\apache-maven-3.5.4\bin;D:\Tools\xshell\;D:\Environment\nvm;D:\Environment\nodejs;C:\Program Files\dotnet\;D:\Environment\ffmpeg-5.1.2-full_build\bin;D:\Environment\nc;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\Tools\IntelliJ IDEA 2021.2.1\bin;D:\Tools\WebStorm 2021.2.1\bin;C:\Users\<USER>\AppData\Roaming\npm;D:\Environment\nvm;D:\Environment\nodejs;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\Tools\IntelliJ IDEA 2021.2.1\bin;D:\Tools\WebStorm 2021.2.1\bin;C:\Users\<USER>\AppData\Roaming\npm;D:\E;C:\Program Files (x86)\PuTTY\;D:\Tools\Microsoft VS Code\bin;D:\Mcp-Servers\chuzhi;C:\Program Files\Pandoc\;C:\Windows\system32\config\systemprofile\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\.local\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;D:\Tools\IntelliJ IDEA 2025.1.3\bin;D:\Tools\WebStorm 2025.1.3\bin;D:\Tools\DataGrip 2024.2.2\bin;D:\Soft\Windsurf\bin;D:\Tools\PyCharm 2025.1.1\bin;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;D:\Tools\cursor\resources\app\bin;D:\Tools\Kiro\bin;.]
2025-08-01 15:28:10.307 [localhost-startStop-1] INFO  o.a.c.c.C.[.[.[/] 180 Initializing Spring embedded WebApplicationContext
2025-08-01 15:28:11.191 [localhost-startStop-1] INFO  c.b.d.d.DynamicRoutingDataSource 154 dynamic-datasource - add a datasource named [fastgate] success
2025-08-01 15:28:11.191 [localhost-startStop-1] INFO  c.b.d.d.DynamicRoutingDataSource 154 dynamic-datasource - add a datasource named [master] success
2025-08-01 15:28:11.191 [localhost-startStop-1] INFO  c.b.d.d.DynamicRoutingDataSource 234 dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-08-01 15:28:13.400 [main] INFO  f.d.c.DynamicDataSourceConfig 25 动态数据源配置初始化完成 - 使用dynamic-datasource-spring-boot-starter
2025-08-01 15:28:14.725 [main] INFO  f.d.s.ScheduledTaskManagementService 99 定时任务信息初始化完成
2025-08-01 15:28:16.214 [main] INFO  o.a.c.h.Http11NioProtocol 180 Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 15:28:16.235 [main] INFO  o.a.t.u.n.NioSelectorPool 180 Using a shared selector for servlet write/read
2025-08-01 15:28:16.255 [main] INFO  f.d.StartMain 59 Started StartMain in 8.357 seconds (JVM running for 9.674)
2025-08-01 15:28:17.017 [RMI TCP Connection(4)-*******] INFO  o.a.c.c.C.[.[.[/] 180 Initializing Spring FrameworkServlet 'dispatcherServlet'
2025-08-01 15:28:17.478 [RMI TCP Connection(3)-*******] INFO  i.l.c.EpollProvider 101 Starting without optional epoll library
2025-08-01 15:28:17.482 [RMI TCP Connection(3)-*******] INFO  i.l.c.KqueueProvider 101 Starting without optional kqueue library
2025-08-01 15:29:01.892 [http-nio-8080-exec-6] INFO  f.d.c.StudentDormitoryQueryController 189 获取宿舍楼列表
2025-08-01 15:29:01.894 [http-nio-8080-exec-5] INFO  f.d.c.StudentDormitoryQueryController 223 获取已分配宿舍学生统计信息，楼栋: null
2025-08-01 15:29:01.894 [http-nio-8080-exec-4] INFO  f.d.c.StudentDormitoryQueryController 53 查询已分配宿舍学生状态，页码: 0, 大小: 20, 姓名: null, 楼栋: null, 状态: null
2025-08-01 15:29:01.897 [http-nio-8080-exec-5] INFO  f.d.s.StudentDormitoryQueryService 131 开始获取已分配宿舍学生统计信息: buildingCode=null
2025-08-01 15:29:01.897 [http-nio-8080-exec-4] INFO  f.d.s.StudentDormitoryQueryService 55 开始查询已分配宿舍学生状态: personName=null, buildingCode=null, dormitoryStatus=null
2025-08-01 15:29:01.897 [http-nio-8080-exec-6] INFO  f.d.s.StudentDormitoryQueryService 185 获取宿舍楼列表
2025-08-01 15:29:01.903 [http-nio-8080-exec-4] INFO  f.d.s.StudentDormitoryQueryService 62 没有找到符合条件的已分配宿舍学生
2025-08-01 15:29:01.904 [http-nio-8080-exec-4] INFO  f.d.c.StudentDormitoryQueryController 102 查询已分配宿舍学生状态成功，总数: 0, 当前页: 0
2025-08-01 15:29:01.906 [http-nio-8080-exec-6] INFO  f.d.s.StudentDormitoryQueryService 206 获取到9个宿舍楼
2025-08-01 15:29:01.907 [http-nio-8080-exec-6] INFO  f.d.c.StudentDormitoryQueryController 201 获取宿舍楼列表成功，共9个楼栋
2025-08-01 15:29:01.907 [http-nio-8080-exec-5] INFO  f.d.s.StudentDormitoryQueryService 138 没有找到已分配宿舍的学生
2025-08-01 15:29:01.908 [http-nio-8080-exec-5] INFO  f.d.c.StudentDormitoryQueryController 234 获取已分配宿舍学生统计信息成功
2025-08-01 15:29:06.574 [http-nio-8080-exec-9] INFO  f.d.c.StudentDormitoryQueryController 53 查询已分配宿舍学生状态，页码: 0, 大小: 10, 姓名: null, 楼栋: null, 状态: null
2025-08-01 15:29:06.574 [http-nio-8080-exec-9] INFO  f.d.s.StudentDormitoryQueryService 55 开始查询已分配宿舍学生状态: personName=null, buildingCode=null, dormitoryStatus=null
2025-08-01 15:29:06.577 [http-nio-8080-exec-9] INFO  f.d.s.StudentDormitoryQueryService 62 没有找到符合条件的已分配宿舍学生
2025-08-01 15:29:06.577 [http-nio-8080-exec-9] INFO  f.d.c.StudentDormitoryQueryController 102 查询已分配宿舍学生状态成功，总数: 0, 当前页: 0
2025-08-01 15:36:13.933 [http-nio-8080-exec-7] INFO  f.d.c.DormitoryManagementController 35 开始获取楼栋列表
2025-08-01 15:36:13.943 [http-nio-8080-exec-7] INFO  f.d.c.DormitoryManagementController 44 获取楼栋列表成功，共22个楼栋
2025-08-01 16:37:58.203 [http-nio-8080-exec-8] INFO  f.d.c.DormitoryManagementController 63 开始获取楼栋0774c892f3c143fd932a75a854689b6f的楼层列表
2025-08-01 16:37:58.224 [http-nio-8080-exec-8] INFO  f.d.c.DormitoryManagementController 73 获取楼栋0774c892f3c143fd932a75a854689b6f的楼层列表成功，共6个楼层
2025-08-01 16:38:00.331 [http-nio-8080-exec-3] INFO  f.d.c.DormitoryManagementController 96 开始获取楼栋0774c892f3c143fd932a75a854689b6f第1层的寝室列表
2025-08-01 16:38:00.371 [http-nio-8080-exec-3] INFO  f.d.c.DormitoryManagementController 123 获取楼栋0774c892f3c143fd932a75a854689b6f第1层寝室列表成功，共13间寝室，入住率{:.1f}%
2025-08-01 16:38:03.277 [http-nio-8080-exec-10] INFO  f.d.c.StudentDormitoryQueryController 189 获取宿舍楼列表
2025-08-01 16:38:03.277 [http-nio-8080-exec-1] INFO  f.d.c.StudentDormitoryQueryController 223 获取已分配宿舍学生统计信息，楼栋: null
2025-08-01 16:38:03.277 [http-nio-8080-exec-10] INFO  f.d.s.StudentDormitoryQueryService 185 获取宿舍楼列表
2025-08-01 16:38:03.277 [http-nio-8080-exec-1] INFO  f.d.s.StudentDormitoryQueryService 131 开始获取已分配宿舍学生统计信息: buildingCode=null
2025-08-01 16:38:03.279 [http-nio-8080-exec-2] INFO  f.d.c.StudentDormitoryQueryController 53 查询已分配宿舍学生状态，页码: 0, 大小: 20, 姓名: null, 楼栋: null, 状态: null
2025-08-01 16:38:03.279 [http-nio-8080-exec-2] INFO  f.d.s.StudentDormitoryQueryService 55 开始查询已分配宿舍学生状态: personName=null, buildingCode=null, dormitoryStatus=null
2025-08-01 16:38:03.285 [http-nio-8080-exec-2] INFO  f.d.s.StudentDormitoryQueryService 66 找到5名已分配宿舍的学生
2025-08-01 16:38:03.291 [http-nio-8080-exec-10] INFO  f.d.s.StudentDormitoryQueryService 206 获取到10个宿舍楼
2025-08-01 16:38:03.292 [http-nio-8080-exec-10] INFO  f.d.c.StudentDormitoryQueryController 201 获取宿舍楼列表成功，共10个楼栋
2025-08-01 16:38:03.378 [http-nio-8080-exec-1] INFO  f.d.s.StudentDormitoryQueryService 166 已分配宿舍学生统计完成: 总数=5, 在寝=0, 外出=0, 无记录=5, 归寝率=0.0%, 耗时=101ms
2025-08-01 16:38:03.379 [http-nio-8080-exec-1] INFO  f.d.c.StudentDormitoryQueryController 234 获取已分配宿舍学生统计信息成功
2025-08-01 16:38:03.392 [http-nio-8080-exec-2] INFO  f.d.s.StudentDormitoryQueryService 110 查询已分配宿舍学生状态完成: 总数=5, 筛选后=5, 当前页=5, 耗时=113ms
2025-08-01 16:38:03.395 [http-nio-8080-exec-2] INFO  f.d.c.StudentDormitoryQueryController 102 查询已分配宿舍学生状态成功，总数: 5, 当前页: 5
2025-08-01 16:38:07.678 [http-nio-8080-exec-4] INFO  f.d.c.DashboardApiController 39 获取楼栋列表请求
2025-08-01 16:38:07.683 [http-nio-8080-exec-4] INFO  f.d.s.DormitoryService 507 查询楼栋列表成功，共22个楼栋
2025-08-01 16:38:07.683 [http-nio-8080-exec-4] INFO  f.d.c.DashboardApiController 57 获取楼栋列表成功，共23个楼栋
2025-08-01 16:38:07.833 [http-nio-8080-exec-6] INFO  f.d.c.DashboardController 309 统一大屏数据查询: buildingCodes=null, forceRefresh=false
2025-08-01 16:38:07.833 [http-nio-8080-exec-6] INFO  f.d.c.DashboardController 326 查询全部统计数据
2025-08-01 16:38:07.842 [http-nio-8080-exec-6] INFO  f.d.s.CachedDormitoryStatsService 62 开始获取基于已分配宿舍学生的统计信息（优化版本）
2025-08-01 16:38:07.848 [http-nio-8080-exec-6] INFO  f.d.s.CachedDormitoryStatsService 334 开始计算基于已分配宿舍学生的统计信息
2025-08-01 16:38:07.854 [http-nio-8080-exec-6] INFO  f.d.s.CachedDormitoryStatsService 342 已分配宿舍的学生总数: 5
2025-08-01 16:38:07.854 [http-nio-8080-exec-6] INFO  f.d.s.CachedDormitoryStatsService 399 开始分析5名学生的缓存状态
2025-08-01 16:38:07.857 [http-nio-8080-exec-6] INFO  f.d.s.CachedDormitoryStatsService 434 状态分析完成 - 在寝:0, 外出:0, 无记录:5
2025-08-01 16:38:07.857 [http-nio-8080-exec-6] INFO  f.d.s.CachedDormitoryStatsService 373 统计完成 - 总人数:5, 在寝:0, 外出:0, 无记录:5, 归寝率:0.0%, 耗时:8ms
2025-08-01 16:38:07.898 [http-nio-8080-exec-6] INFO  f.d.s.CachedDormitoryStatsService 84 基于已分配宿舍学生的统计信息计算并缓存完成，耗时: 56ms
2025-08-01 16:38:07.899 [http-nio-8080-exec-6] INFO  f.d.c.DashboardController 373 统一大屏数据查询成功: 楼栋=null, 总人数=5, 在寝=0, 外出=0, 无记录=5, 归寝率=0.0%, 刷新=false, 耗时=66ms
2025-08-01 16:38:08.416 [http-nio-8080-exec-5] INFO  f.d.c.WebSocketStatusController 46 WebSocket状态查询: 连接数=0
2025-08-01 16:38:08.427 [http-nio-8080-exec-9] INFO  f.d.s.CachedDormitoryStatsService 62 开始获取基于已分配宿舍学生的统计信息（优化版本）
2025-08-01 16:38:08.472 [http-nio-8080-exec-9] INFO  f.d.s.CachedDormitoryStatsService 70 从缓存获取统计信息成功，耗时: 45ms
2025-08-01 16:38:08.693 [http-nio-8080-exec-7] INFO  f.d.w.DashboardWebSocketHandler 39 WebSocket连接建立: sessionId=850a9c24-b13d-b03e-f604-c90bb3e5fcd6, 当前连接数: 1
2025-08-01 16:45:06.314 [http-nio-8080-exec-9] INFO  f.d.w.DashboardWebSocketHandler 66 WebSocket连接关闭: sessionId=850a9c24-b13d-b03e-f604-c90bb3e5fcd6, status=CloseStatus[code=1001, reason=null], 当前连接数: 0
2025-08-01 16:45:06.716 [http-nio-8080-exec-7] INFO  f.d.c.DashboardApiController 39 获取楼栋列表请求
2025-08-01 16:45:06.720 [http-nio-8080-exec-7] INFO  f.d.s.DormitoryService 507 查询楼栋列表成功，共22个楼栋
2025-08-01 16:45:06.720 [http-nio-8080-exec-7] INFO  f.d.c.DashboardApiController 57 获取楼栋列表成功，共23个楼栋
2025-08-01 16:45:06.743 [http-nio-8080-exec-8] INFO  f.d.c.DashboardController 309 统一大屏数据查询: buildingCodes=null, forceRefresh=false
2025-08-01 16:45:06.743 [http-nio-8080-exec-8] INFO  f.d.c.DashboardController 326 查询全部统计数据
2025-08-01 16:45:06.743 [http-nio-8080-exec-8] INFO  f.d.s.CachedDormitoryStatsService 62 开始获取基于已分配宿舍学生的统计信息（优化版本）
2025-08-01 16:45:06.744 [http-nio-8080-exec-8] INFO  f.d.s.CachedDormitoryStatsService 334 开始计算基于已分配宿舍学生的统计信息
2025-08-01 16:45:06.747 [http-nio-8080-exec-8] INFO  f.d.s.CachedDormitoryStatsService 342 已分配宿舍的学生总数: 5
2025-08-01 16:45:06.748 [http-nio-8080-exec-8] INFO  f.d.s.CachedDormitoryStatsService 399 开始分析5名学生的缓存状态
2025-08-01 16:45:06.750 [http-nio-8080-exec-8] INFO  f.d.s.CachedDormitoryStatsService 434 状态分析完成 - 在寝:0, 外出:0, 无记录:5
2025-08-01 16:45:06.750 [http-nio-8080-exec-8] INFO  f.d.s.CachedDormitoryStatsService 373 统计完成 - 总人数:5, 在寝:0, 外出:0, 无记录:5, 归寝率:0.0%, 耗时:5ms
2025-08-01 16:45:06.753 [http-nio-8080-exec-8] INFO  f.d.s.CachedDormitoryStatsService 84 基于已分配宿舍学生的统计信息计算并缓存完成，耗时: 10ms
2025-08-01 16:45:06.753 [http-nio-8080-exec-8] INFO  f.d.c.DashboardController 373 统一大屏数据查询成功: 楼栋=null, 总人数=5, 在寝=0, 外出=0, 无记录=5, 归寝率=0.0%, 刷新=false, 耗时=10ms
2025-08-01 16:45:07.594 [http-nio-8080-exec-3] INFO  f.d.c.WebSocketStatusController 46 WebSocket状态查询: 连接数=0
2025-08-01 16:45:07.602 [http-nio-8080-exec-10] INFO  f.d.s.CachedDormitoryStatsService 62 开始获取基于已分配宿舍学生的统计信息（优化版本）
2025-08-01 16:45:07.604 [http-nio-8080-exec-10] INFO  f.d.s.CachedDormitoryStatsService 70 从缓存获取统计信息成功，耗时: 2ms
2025-08-01 16:45:07.628 [http-nio-8080-exec-1] INFO  f.d.w.DashboardWebSocketHandler 39 WebSocket连接建立: sessionId=c3c44269-198a-ae12-818c-d1afe771e4a2, 当前连接数: 1
2025-08-01 16:45:46.985 [http-nio-8080-exec-6] INFO  f.d.w.DashboardWebSocketHandler 66 WebSocket连接关闭: sessionId=c3c44269-198a-ae12-818c-d1afe771e4a2, status=CloseStatus[code=1001, reason=null], 当前连接数: 0
2025-08-01 16:45:47.201 [http-nio-8080-exec-5] INFO  f.d.c.DashboardApiController 39 获取楼栋列表请求
2025-08-01 16:45:47.205 [http-nio-8080-exec-5] INFO  f.d.s.DormitoryService 507 查询楼栋列表成功，共22个楼栋
2025-08-01 16:45:47.205 [http-nio-8080-exec-5] INFO  f.d.c.DashboardApiController 57 获取楼栋列表成功，共23个楼栋
2025-08-01 16:45:47.230 [http-nio-8080-exec-9] INFO  f.d.c.DashboardController 309 统一大屏数据查询: buildingCodes=null, forceRefresh=false
2025-08-01 16:45:47.230 [http-nio-8080-exec-9] INFO  f.d.c.DashboardController 326 查询全部统计数据
2025-08-01 16:45:47.230 [http-nio-8080-exec-9] INFO  f.d.s.CachedDormitoryStatsService 62 开始获取基于已分配宿舍学生的统计信息（优化版本）
2025-08-01 16:45:47.231 [http-nio-8080-exec-9] INFO  f.d.s.CachedDormitoryStatsService 70 从缓存获取统计信息成功，耗时: 1ms
2025-08-01 16:45:47.231 [http-nio-8080-exec-9] INFO  f.d.c.DashboardController 373 统一大屏数据查询成功: 楼栋=null, 总人数=5, 在寝=0, 外出=0, 无记录=5, 归寝率=0.0%, 刷新=false, 耗时=1ms
2025-08-01 16:45:48.131 [http-nio-8080-exec-7] INFO  f.d.c.WebSocketStatusController 46 WebSocket状态查询: 连接数=0
2025-08-01 16:45:48.138 [http-nio-8080-exec-8] INFO  f.d.s.CachedDormitoryStatsService 62 开始获取基于已分配宿舍学生的统计信息（优化版本）
2025-08-01 16:45:48.139 [http-nio-8080-exec-8] INFO  f.d.s.CachedDormitoryStatsService 70 从缓存获取统计信息成功，耗时: 2ms
2025-08-01 16:45:48.154 [http-nio-8080-exec-3] INFO  f.d.w.DashboardWebSocketHandler 39 WebSocket连接建立: sessionId=eb0d7b72-a6d0-4b4f-67c7-36b8ff981618, 当前连接数: 1
2025-08-01 16:53:25.935 [http-nio-8080-exec-1] INFO  f.d.w.DashboardWebSocketHandler 66 WebSocket连接关闭: sessionId=eb0d7b72-a6d0-4b4f-67c7-36b8ff981618, status=CloseStatus[code=1000, reason=手动断开], 当前连接数: 0
2025-08-01 16:53:25.965 [http-nio-8080-exec-2] INFO  f.d.c.StudentDormitoryQueryController 189 获取宿舍楼列表
2025-08-01 16:53:25.965 [http-nio-8080-exec-4] INFO  f.d.c.StudentDormitoryQueryController 223 获取已分配宿舍学生统计信息，楼栋: null
2025-08-01 16:53:25.965 [http-nio-8080-exec-4] INFO  f.d.s.StudentDormitoryQueryService 131 开始获取已分配宿舍学生统计信息: buildingCode=null
2025-08-01 16:53:25.965 [http-nio-8080-exec-2] INFO  f.d.s.StudentDormitoryQueryService 185 获取宿舍楼列表
2025-08-01 16:53:25.966 [http-nio-8080-exec-6] INFO  f.d.c.StudentDormitoryQueryController 53 查询已分配宿舍学生状态，页码: 0, 大小: 20, 姓名: null, 楼栋: null, 状态: null
2025-08-01 16:53:25.966 [http-nio-8080-exec-6] INFO  f.d.s.StudentDormitoryQueryService 55 开始查询已分配宿舍学生状态: personName=null, buildingCode=null, dormitoryStatus=null
2025-08-01 16:53:25.974 [http-nio-8080-exec-2] INFO  f.d.s.StudentDormitoryQueryService 206 获取到10个宿舍楼
2025-08-01 16:53:25.974 [http-nio-8080-exec-4] INFO  f.d.s.StudentDormitoryQueryService 166 已分配宿舍学生统计完成: 总数=5, 在寝=0, 外出=0, 无记录=5, 归寝率=0.0%, 耗时=9ms
2025-08-01 16:53:25.974 [http-nio-8080-exec-4] INFO  f.d.c.StudentDormitoryQueryController 234 获取已分配宿舍学生统计信息成功
2025-08-01 16:53:25.974 [http-nio-8080-exec-2] INFO  f.d.c.StudentDormitoryQueryController 201 获取宿舍楼列表成功，共10个楼栋
2025-08-01 16:53:25.975 [http-nio-8080-exec-6] INFO  f.d.s.StudentDormitoryQueryService 66 找到5名已分配宿舍的学生
2025-08-01 16:53:25.980 [http-nio-8080-exec-6] INFO  f.d.s.StudentDormitoryQueryService 110 查询已分配宿舍学生状态完成: 总数=5, 筛选后=5, 当前页=5, 耗时=14ms
2025-08-01 16:53:25.980 [http-nio-8080-exec-6] INFO  f.d.c.StudentDormitoryQueryController 102 查询已分配宿舍学生状态成功，总数: 5, 当前页: 5
2025-08-01 16:53:29.920 [http-nio-8080-exec-7] INFO  f.d.c.DormitoryManagementController 35 开始获取楼栋列表
2025-08-01 16:53:29.927 [http-nio-8080-exec-7] INFO  f.d.c.DormitoryManagementController 44 获取楼栋列表成功，共22个楼栋
2025-08-01 16:53:53.778 [http-nio-8080-exec-8] INFO  f.d.c.DashboardApiController 39 获取楼栋列表请求
2025-08-01 16:53:53.781 [http-nio-8080-exec-8] INFO  f.d.s.DormitoryService 507 查询楼栋列表成功，共22个楼栋
2025-08-01 16:53:53.782 [http-nio-8080-exec-8] INFO  f.d.c.DashboardApiController 57 获取楼栋列表成功，共23个楼栋
2025-08-01 16:53:53.801 [http-nio-8080-exec-3] INFO  f.d.c.DashboardController 309 统一大屏数据查询: buildingCodes=null, forceRefresh=false
2025-08-01 16:53:53.801 [http-nio-8080-exec-3] INFO  f.d.c.DashboardController 326 查询全部统计数据
2025-08-01 16:53:53.801 [http-nio-8080-exec-3] INFO  f.d.s.CachedDormitoryStatsService 62 开始获取基于已分配宿舍学生的统计信息（优化版本）
2025-08-01 16:53:53.802 [http-nio-8080-exec-3] INFO  f.d.s.CachedDormitoryStatsService 334 开始计算基于已分配宿舍学生的统计信息
2025-08-01 16:53:53.805 [http-nio-8080-exec-3] INFO  f.d.s.CachedDormitoryStatsService 342 已分配宿舍的学生总数: 5
2025-08-01 16:53:53.806 [http-nio-8080-exec-3] INFO  f.d.s.CachedDormitoryStatsService 399 开始分析5名学生的缓存状态
2025-08-01 16:53:53.807 [http-nio-8080-exec-3] INFO  f.d.s.CachedDormitoryStatsService 434 状态分析完成 - 在寝:0, 外出:0, 无记录:5
2025-08-01 16:53:53.807 [http-nio-8080-exec-3] INFO  f.d.s.CachedDormitoryStatsService 373 统计完成 - 总人数:5, 在寝:0, 外出:0, 无记录:5, 归寝率:0.0%, 耗时:4ms
2025-08-01 16:53:53.808 [http-nio-8080-exec-3] INFO  f.d.s.CachedDormitoryStatsService 84 基于已分配宿舍学生的统计信息计算并缓存完成，耗时: 7ms
2025-08-01 16:53:53.809 [http-nio-8080-exec-3] INFO  f.d.c.DashboardController 373 统一大屏数据查询成功: 楼栋=null, 总人数=5, 在寝=0, 外出=0, 无记录=5, 归寝率=0.0%, 刷新=false, 耗时=8ms
2025-08-01 16:53:54.329 [http-nio-8080-exec-10] INFO  f.d.c.WebSocketStatusController 46 WebSocket状态查询: 连接数=0
2025-08-01 16:53:54.338 [http-nio-8080-exec-1] INFO  f.d.s.CachedDormitoryStatsService 62 开始获取基于已分配宿舍学生的统计信息（优化版本）
2025-08-01 16:53:54.339 [http-nio-8080-exec-1] INFO  f.d.s.CachedDormitoryStatsService 70 从缓存获取统计信息成功，耗时: 1ms
2025-08-01 16:53:54.357 [http-nio-8080-exec-2] INFO  f.d.w.DashboardWebSocketHandler 39 WebSocket连接建立: sessionId=2c359bb1-fedd-8dc6-5e96-be5aa3516442, 当前连接数: 1
2025-08-01 17:00:44.441 [Thread-21] INFO  c.b.d.d.DynamicRoutingDataSource 211 dynamic-datasource start closing ....
2025-08-01 17:00:44.443 [Thread-21] INFO  c.b.d.d.DynamicRoutingDataSource 215 dynamic-datasource all closed success,bye
2025-08-01 17:00:46.856 [localhost-startStop-2] INFO  f.d.w.DashboardWebSocketHandler 66 WebSocket连接关闭: sessionId=2c359bb1-fedd-8dc6-5e96-be5aa3516442, status=CloseStatus[code=1001, reason=The web application is stopping], 当前连接数: 0
